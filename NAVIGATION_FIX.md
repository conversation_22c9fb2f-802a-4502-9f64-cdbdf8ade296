# Navigation Tab Selection Fix

## Issue
When users log into the app, the Profile tab was being selected and shown on the home screen instead of the Home tab.

## Root Cause
The `BottomNavNotifier` was initialized with the correct initial state (index 0 for Home tab), but it wasn't synchronizing with the `StatefulNavigationShell`'s current index when the navigation shell was first set. This caused a mismatch between the visual tab selection and the actual navigation state.

## Solution
Modified the `setNavigationShell` method in `BottomNavNotifier` to synchronize the state with the navigation shell's current index after the build cycle completes:

```dart
void setNavigationShell(StatefulNavigationShell shell) {
  _navigationShell = shell;
  // Sync the state with the current navigation shell index after the build cycle
  WidgetsBinding.instance.addPostFrameCallback((_) {
    if (state != shell.currentIndex) {
      state = shell.currentIndex;
    }
  });
}
```

### Why `addPostFrameCallback`?
The initial approach of directly setting `state = shell.currentIndex` caused a crash because it tried to modify the provider state during the widget build lifecycle (specifically during `useEffect`). Using `addPostFrameCallback` ensures the state update happens after the build cycle completes, avoiding the "Tried to modify a provider while the widget tree was building" error.

## Files Changed
- `lib/core/providers/menus/bottom_nav_notifier.dart`

## Tab Index Mapping
- Index 0: Home tab
- Index 1: Cookbooks tab  
- Index 2: Shopping tab
- Index 3: Profile tab

## Testing
- Created unit tests to verify the `BottomNavNotifier` initializes with index 0 (Home tab)
- Verified the fix works across all bottom navigation implementations:
  - `CustomBottomNavScreen` (mobile)
  - `CustomBottomNavIpad` (iPad)
  - `BottomNavScreen` (desktop)

## Impact
This fix ensures that when users log into the app, they will see the Home tab selected by default, providing the expected user experience.
