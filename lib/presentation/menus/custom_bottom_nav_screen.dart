import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../app/assets_manager.dart';
import '../../app/imports/packages_imports.dart';
import '../../core/data/models/plan_type.dart';
import '../../core/providers/cookbook_notifier.dart';
import '../../core/providers/menus/bottom_nav_notifier.dart';
import '../../core/providers/profile/user_profile_notifier.dart';
import '../../core/providers/shopping/pantry_notifier.dart';
import '../../core/providers/shopping/shopping_notifier.dart';
import '../../core/utils/Utils.dart';
import '../../core/widgets/premium_cooking_dialog.dart';
import 'bottom_nav_item.dart';

class CustomBottomNavScreen extends HookConsumerWidget {
  final StatefulNavigationShell navigationShell;
  final Color selectedColor;
  final Color unselectedColor;
  final Color backgroundColor;

  const CustomBottomNavScreen({
    super.key,
    required this.navigationShell,
    this.selectedColor = const Color(0xFFE01010),
    this.unselectedColor = const Color(0xFF747474),
    this.backgroundColor = Colors.white,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavProvider);
    final bottomNavNotifier = ref.read(bottomNavProvider.notifier);
    // Animation controller for FAB menu
    final isOpen = useState(false);
    final animationController = useAnimationController(
      duration: const Duration(milliseconds: 250),
      initialValue: 0,
    );
    final userProfile =
        ref.watch(userProfileNotifierProvider).data?.userProfile;

    // Timer for auto-closing the FAB menu
    final timer = useRef<Timer?>(null);

    // Toggle menu and manage timer
    void toggleMenu() {
      if (isOpen.value) {
        animationController.reverse();
        isOpen.value = false;
        timer.value?.cancel(); // Cancel timer when closing manually
      } else {
        animationController.forward();
        isOpen.value = true;
        // Start timer to auto-close after 20 seconds
        timer.value = Timer(const Duration(seconds: 20), () {
          if (isOpen.value) {
            animationController.reverse();
            isOpen.value = false;
          }
        });
      }
    }

    // Clean up timer on widget disposal
    useEffect(() {
      bottomNavNotifier.setNavigationShell(navigationShell);
      return () {
        timer.value?.cancel(); // Cancel timer when widget is disposed
      };
    }, []);

    // Define icon assets as a Map with unselected and selected icons
    final Map<String, Map<String, String>> iconAssets = {
      'Home': {
        'unselected': AssetsManager.ic_home,
        'selected': AssetsManager.ic_home,
        // Update with selected icon if available
      },
      'Cookbooks': {
        'unselected': AssetsManager.ic_cookbooks,
        'selected': AssetsManager.ic_cookbooks,
        // Update with selected icon if available
      },
      'Shop': {
        'unselected': AssetsManager.ic_shop,
        'selected': AssetsManager.ic_shop,
        // Update with selected icon if available
      },
      'Profile': {
        'unselected': userProfile?.currentPlan?.planName == 'Free'
            ? AssetsManager.ic_profile
            : (userProfile?.currentPlan != null
                ? AssetsManager.ic_profilePremium
                : AssetsManager.ic_profile),
        'selected': userProfile?.currentPlan?.planName == 'Free'
            ? AssetsManager.ic_profile_selected
            : (userProfile?.currentPlan != null
                ? AssetsManager.ic_premium_user_selected
                : AssetsManager.ic_profile_selected),
      },
    };

    final List<String> labels = const [
      'Home',
      'Cookbooks',
      'Shop',
      'Profile',
    ];

    // Handle tab selection with confirmation for Home
    void handleTabSelection(int index) async {
      if (isOpen.value) {
        animationController.reverse();
        isOpen.value = false;
        timer.value?.cancel(); // Cancel timer when closing via nav tap
      }

      // if (index == 0) {
      //   // Show confirmation dialog for Home tab
      //   final confirmed = await showExitConfirmationDialog();
      //   if (confirmed) {
      //     bottomNavNotifier.onTabSelected(index);
      //   }
      // } else
      if (index == 2) {
        // Handle Shop tab with premium check
        if (userProfile!.currentPlan!.planName == PlanType.Free.name) {
          showDialog(
            context: context,
            builder: (context) => const PremiumCookingDialog(),
          );
        } else {
          bottomNavNotifier.onTabSelected(index);
        }
      } else {
        bottomNavNotifier.onTabSelected(index);
      }
    }

    // Handle Android back button
    Future<bool> handleBackButton() async {
      print(" currentIndex== ${currentIndex}");
      if (currentIndex > 0) {
        // Navigate to Home tab (index 0) without confirmation
        bottomNavNotifier.onTabSelected(0);
        return false; // Prevent app exit
      } else {
        Navigator.pop(context);
        // final confirmed = await Utils().showExitConfirmationDialog(context);
        // On Home tab, allow default back button behavior (exit app)
        return true;
      }
    }

    return PopScope(
      canPop: false, // prevents auto-pop
      onPopInvoked: (didPop) async {
        print("currentIndex:  ${currentIndex}");
        if (!didPop) {
          final shouldExit = await handleBackButton();
          if (shouldExit) {
            Navigator.of(context).maybePop();
          }
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.transparent,
        body: GestureDetector(
          onTap: () => Utils().hideKeyboardOnTap(),
          behavior: HitTestBehavior.opaque,
          child: navigationShell,
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: SizedBox(
          width: 200, // Increased width to accommodate circular layout
          height: 200, // Added height for circular layout
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Option 1: Shopping (Top position)
              AnimatedPositioned(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOut,
                top: isOpen.value ? (Platform.isAndroid ? 20 : 40) : 90,
                // Move to top when open
                left: 75,
                // Center horizontally
                child: ScaleTransition(
                  scale: animationController,
                  child: FloatingActionButton(
                    heroTag: "shopping",
                    shape: CircleBorder(),
                    backgroundColor: Colors.blue,
                    onPressed: () {
                      toggleMenu();
                      if (userProfile!.currentPlan!.planName ==
                          PlanType.Free.name) {
                        showDialog(
                          context: context,
                          builder: (context) => const PremiumCookingDialog(),
                        );
                      } else {
                        ref
                            .read(shoppingNotifierProvider.notifier)
                            .showShoppingBottomSheet(
                              context: context,
                            );
                      }
                    },
                    child: SvgPicture.asset(AssetsManager.ic_option_shopping),
                  ),
                ),
              ),
              // Option 2: Cookbooks (Left position)
              AnimatedPositioned(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOut,
                top: isOpen.value ? (Platform.isAndroid ? 50 : 80) : 90,
                // Move to left when open
                left: isOpen.value ? 10 : 75,
                // Move further left when open
                child: ScaleTransition(
                  scale: animationController,
                  child: FloatingActionButton(
                    heroTag: "cookbooks",
                    shape: CircleBorder(),
                    backgroundColor: Colors.red,
                    onPressed: () {
                      toggleMenu();
                      ref
                          .read(cookbookNotifierProvider.notifier)
                          .showCookbookBottomSheet(
                            context: context,
                            ref: ref,
                          );
                    },
                    child: SvgPicture.asset(AssetsManager.ic_option_cookbooks),
                  ),
                ),
              ),
              // Option 3: Pantry (Right position)
              AnimatedPositioned(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOut,
                top: isOpen.value ? (Platform.isAndroid ? 50 : 80) : 90,
                // Move to right when open
                left: isOpen.value ? 140 : 75,
                // Move further right when open
                child: ScaleTransition(
                  scale: animationController,
                  child: FloatingActionButton(
                    heroTag: "pantry",
                    shape: CircleBorder(),
                    backgroundColor: Colors.green,
                    onPressed: () {
                      toggleMenu();
                      if (userProfile!.currentPlan!.planName ==
                          PlanType.Free.name) {
                        showDialog(
                          context: context,
                          builder: (context) => const PremiumCookingDialog(),
                        );
                      } else {
                        ref
                            .read(pantryNotifierProvider.notifier)
                            .showPantryBottomSheet(
                              context: context,
                            );
                      }
                    },
                    child: SvgPicture.asset(AssetsManager.ic_option_pantry),
                  ),
                ),
              ),
              // Main FAB (Center position)
              Positioned(
                top: Platform.isAndroid ? 90 : 120, // Center vertically
                left: 75, // Center horizontally
                child: FloatingActionButton(
                  heroTag: "mainFab",
                  shape: const CircleBorder(),
                  backgroundColor: Colors.red,
                  elevation: 4,
                  onPressed: toggleMenu,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 200),
                    transitionBuilder: (child, anim) => RotationTransition(
                      turns: child.key == const ValueKey('plus')
                          ? Tween<double>(begin: 0.75, end: 1).animate(anim)
                          : Tween<double>(begin: 1, end: 0.75).animate(anim),
                      child: FadeTransition(opacity: anim, child: child),
                    ),
                    child: isOpen.value
                        ? const Icon(
                            Icons.close,
                            key: ValueKey('close'),
                            color: Colors.white,
                          )
                        : const Icon(
                            Icons.add,
                            key: ValueKey('plus'),
                            color: Colors.white,
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: Container(
          height: 85,
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.2),
                spreadRadius: 2,
                blurRadius: 5,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(labels.length, (index) {
              // Provide a fallback icon if the map entry is null
              final iconMap = iconAssets[labels[index]] ??
                  {
                    'unselected': index == 2
                        ? AssetsManager.ic_shop
                        : AssetsManager.ic_profile,
                    'selected': index == 2
                        ? AssetsManager.ic_shop
                        : AssetsManager.ic_profile_selected,
                  };
              return BottomNavItem(
                index: index,
                iconAsset:
                    iconMap[currentIndex == index ? 'selected' : 'unselected']!,
                label: labels[index],
                isSelected: currentIndex == index,
                selectedColor: selectedColor,
                unselectedColor: unselectedColor,
                onTap: () =>
                    handleTabSelection(index), // Use handleTabSelection
              );
            }),
          ),
        ),
      ),
    );
  }
}
