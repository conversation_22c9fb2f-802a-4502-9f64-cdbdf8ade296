import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../app/assets_manager.dart';
import '../../app/imports/packages_imports.dart';
import '../../core/data/models/plan_type.dart';
import '../../core/providers/menus/bottom_nav_notifier.dart';
import '../../core/providers/profile/user_profile_notifier.dart';
import '../../core/utils/Utils.dart';
import '../../core/widgets/custom_text.dart';
import '../../core/widgets/premium_cooking_dialog.dart';
import 'bottom_nav_item.dart';

class CustomBottomNavIpad extends HookConsumerWidget {
  final StatefulNavigationShell navigationShell;
  final Color selectedColor;
  final Color unselectedColor;
  final Color backgroundColor;

  const CustomBottomNavIpad({
    super.key,
    required this.navigationShell,
    this.selectedColor = const Color(0xFFE01010),
    this.unselectedColor = const Color(0xFF747474),
    this.backgroundColor = Colors.white,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavProvider);
    final bottomNavNotifier = ref.read(bottomNavProvider.notifier);
    final userProfile = ref.watch(userProfileNotifierProvider).data?.userProfile;

    // detect keyboard
    final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom > 0;

    useEffect(() {
      bottomNavNotifier.setNavigationShell(navigationShell);
      return null;
    }, []);

    final Map<String, Map<String, String>> iconAssets =  {
      'Home': {
        'unselected': AssetsManager.ic_home,
        'selected': AssetsManager.ic_home,
        // Update with selected icon if available
      },
      'Cookbooks': {
        'unselected': AssetsManager.ic_cookbooks,
        'selected': AssetsManager.ic_cookbooks,
        // Update with selected icon if available
      },
      'Shop': {
        'unselected': AssetsManager.ic_shop,
        'selected': AssetsManager.ic_shop,
        // Update with selected icon if available
      },
      'Profile': {
        'unselected': userProfile?.currentPlan?.planName == 'Free'
            ? AssetsManager.ic_profile
            : (userProfile?.currentPlan != null
            ? AssetsManager.ic_profilePremium
            : AssetsManager.ic_profile),
        'selected': userProfile?.currentPlan?.planName == 'Free'
            ? AssetsManager.ic_profile_selected
            : (userProfile?.currentPlan != null
            ? AssetsManager.ic_premium_user_selected
            : AssetsManager.ic_profile_selected),
      },
    };

    final List<String> labels = const [
      'Home',
      'Cookbooks',
      'Shop List',
      'Profile',
    ];

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.transparent,
      body: GestureDetector(
        onTap: () => Utils().hideKeyboardOnTap(),
        behavior: HitTestBehavior.opaque,
        child: navigationShell,
      ),
      bottomNavigationBar: isKeyboardOpen
          ? const SizedBox.shrink() // hide when keyboard is open
          : Container(
        height: 85,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(labels.length, (index) {
            final iconMap = iconAssets[labels[index]] ?? {
              'unselected': index == 2
                  ? AssetsManager.ic_shop
                  : AssetsManager.ic_profile,
              'selected': index == 2
                  ? AssetsManager.ic_shop
                  : AssetsManager.ic_profile_selected,
            };
            return BottomNavItem(
              index: index,
              iconAsset: iconMap[
              currentIndex == index ? 'selected' : 'unselected']!,
              label: labels[index],
              isSelected: currentIndex == index,
              selectedColor: selectedColor,
              unselectedColor: unselectedColor,
              onTap: () {
                if (index == 2 &&
                    userProfile?.currentPlan?.planName ==
                        PlanType.Free.name) {
                  showDialog(
                    context: context,
                    builder: (context) => const PremiumCookingDialog(),
                  );
                } else {
                  bottomNavNotifier.onTabSelected(index);
                }
              },
            );
          }),
        ),
      ),
    );
  }

}