library floating_bottom_bar;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:floating_bottom_bar/animated_bottom_navigation_bar.dart';



class CustomAnimatedBottomNavigationBar extends StatefulWidget {
  const CustomAnimatedBottomNavigationBar({
    required this.bottomBar,
    this.barColor = Colors.white,
    this.barGradient,
    this.controller,
    Key? key,
  }) : super(key: key);
  final List<Widget> bottomBar;
  final Color barColor;
  final Gradient? barGradient;
  final FloatingBottomBarController? controller;

  @override
  CustomAnimatedBottomNavigationBarState createState() =>
      CustomAnimatedBottomNavigationBarState();
}

class CustomAnimatedBottomNavigationBarState
    extends State<CustomAnimatedBottomNavigationBar>
    with TickerProviderStateMixin {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void initState() {
    _checkValidations();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Stack(
        fit: StackFit.loose,
        alignment: Alignment.bottomCenter,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: widget.bottomBar,
          )
        ],
      ),
    );
  }

  /// Check tab bat items count is even.
  bool isEvenCount(int length) => length % 2 == 0;

  /// Check validations like maximum items exceed and item count is even.
  void _checkValidations() {
    assert(widget.bottomBar.length <= 4,
        'The maximum number of items is 4');
    assert(isEvenCount(widget.bottomBar.length), 'The number of items must be even');
  }
}
