import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../../../core/widgets/custom_text.dart';

void showSyncInfoBottomSheet(BuildContext context) {
  showModalBottomSheet(
    backgroundColor: AppColors.whiteColor,
    isScrollControlled: false,
    context: context,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      // You can call your passwordResetDialog content directly here
      return syncInfoContent(context);
    },
  );
}

Widget syncInfoContent(BuildContext context) {
  return Consumer(
    builder: (context, ref, child) {
      void closeSheet() {
        Navigator.of(context).pop();
      }

      return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: CustomText(
            text: 'Why Sync?',
            weight: FontWeight.w500,
            size: 20,
            color: AppColors.primaryLightTextColor,
                  ),
          ),
      _buildSyncBenefits(context),
      ]
      );
    },
  );
}

Widget _buildSyncBenefits(BuildContext context) {
  final List<String> features = [
    'Access Anywhere – Your recipes on any device',
    'Instant Updates – Changes reflect everywhere',
    'Cloud Backup – Never lose a recipe',
    'Private & Secure – Encrypted data',
  ];

  return Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: features.map((feature) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: '•',
                weight: FontWeight.w400,
                size: 14,
                color: AppColors.primaryLightTextColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: CustomText(
                  text: feature,
                  weight: FontWeight.w400,
                  size: 14,
                  color: AppColors.primaryLightTextColor,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    ),
  );
}

