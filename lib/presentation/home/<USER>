import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/core/data/models/user_profile_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../../../core/providers/profile/user_profile_notifier.dart';
import '../../core/data/models/plan_type.dart';
import '../../core/providers/categories_notifier.dart';
import '../../core/providers/cuisines_notifier.dart';
import '../../core/providers/home/<USER>';
import '../../core/providers/menus/bottom_nav_notifier.dart';
import '../../core/providers/sync_notifier.dart';
import '../../core/utils/device_utils.dart';
import '../../core/widgets/custom_crausal.dart';
import '../../core/widgets/premium_cooking_dialog.dart';
import '../cookbook/widgets/recipe_clipper_dialog.dart';
import '../shimer/banner_shimmer.dart';
import '../sync/sync_dialog.dart';
import 'mobile_ui/mobile_home_screen.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  int? _previousBottomNavIndex;

  @override
  void initState() {
    final homeNotifier = ref.read(homeNotifierProvider.notifier);

    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
      ref
          .read(categoriesNotifierProvider.notifier)
          .fetchCategories(context: context);
      ref
          .read(cuisinesNotifierProvider.notifier)
          .fetchCuisines(context: context);
      homeNotifier.banners(context, getDeviceType(context).name);
    });
  }

  @override
  Widget build(BuildContext context) {
    final icons = ref.watch(homeScreenProvider).icons;
    final homestate = ref.watch(homeNotifierProvider);
    var userProfile = ref.watch(userProfileNotifierProvider).data?.userProfile;

    final bannerImages = homestate.data?.data.banners;

    // Listen to bottom navigation changes and refresh user profile when Home tab is selected
    final currentBottomNavIndex = ref.watch(bottomNavProvider);

    // Check if we've returned to Home tab (index 0) from another tab
    if (_previousBottomNavIndex != null &&
        _previousBottomNavIndex != 0 &&
        currentBottomNavIndex == 0) {
      // Refresh user profile when returning to Home tab
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
      });
    }
    _previousBottomNavIndex = currentBottomNavIndex;

    return getDeviceType(context).name == 'mobile' ||
            getDeviceType(context).name == 'tablet' ||
            getDeviceType(context).name == 'ipad'
        ? MobileHomeScreen()
        : Scaffold(
            backgroundColor: Colors.transparent,
            body: Stack(
              fit: StackFit.expand,
              children: [
                Image.asset(
                  AssetsManager.background_img,
                  fit: BoxFit.cover,
                ),
                Column(
                  children: [
                    SizedBox(height: 80.h), // Adjust top padding as needed
                    SizedBox(height: 50.h), // Adjust top padding as needed

                    // Top Search Bar
                    Visibility(
                      visible: false,
                      child: CustomSearchBar(
                        width: 500.w,
                        hintText: "Search",
                        controller: searchController,
                        autoFocus: true,
                        formats: [
                          FilteringTextInputFormatter.allow(
                              RegExp(r'[a-zA-Z0-9\s,]')),
                        ],
                      ),
                    ),
                    SizedBox(height: 80.h),
                    LayoutBuilder(
                      builder: (context, constraints) {
                        final double totalWidth = constraints.maxWidth;
                        final int iconsPerRow = 3; // Based on first row
                        final double spacing = 20.w;

                        // Calculate ideal icon size based on spacing and screen width
                        final double availableWidth =
                            totalWidth - (spacing * (iconsPerRow - 1));
                        final double iconSize = (availableWidth / iconsPerRow)
                            .clamp(
                                DeviceUtils().isTabletOrIpad(context)
                                    ? 150
                                    : 250.w,
                                DeviceUtils().isTabletOrIpad(context)
                                    ? 150
                                    : 250.w);

                        // Split icons into three rows
                        final firstRow =
                            icons.take(4).toList(); // First 6 icons
                        final secondRow =
                            icons.skip(4).take(4).toList(); // Next 4 icons
                        // final thirdRow =
                        //     icons.skip(10).take(2).toList(); // Last 2 icons

                        return Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // First row: 6 icons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: firstRow
                                  .map((icon) => Padding(
                                        padding: EdgeInsets.only(right: 10.w),
                                        child: _buildHomeIcon(icon, iconSize,
                                            context, userProfile),
                                      ))
                                  .toList(),
                            ),
                            SizedBox(height: 32.h),
                            // Second row: 4 icons
                            if (secondRow.isNotEmpty)
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: secondRow
                                    .map((icon) => Padding(
                                          padding: EdgeInsets.only(right: 10.w),
                                          child: _buildHomeIcon(icon, iconSize,
                                              context, userProfile),
                                        ))
                                    .toList(),
                              ),
                            SizedBox(height: 50.h),

                            // Banner Carousel at the top
                            if (homestate.status == AppStatus.loading ||
                                bannerImages == null ||
                                bannerImages.isEmpty)
                              BannerShimmer(
                                itemsPerPage: 1,
                              )
                            else
                              BannerCarousel(
                                bannerImages: bannerImages,
                                onImageClick: (index) {
                                  Utils()
                                      .launchURL(url: 'https://mastercook.ai/');
                                  // This is the function that will be called when an image is clicked
                                  debugPrint(
                                      'Banner image at index $index was clicked!');
                                },
                              ),
                            // Third row: 2 icons
                            // if (thirdRow.isNotEmpty)
                            //   Row(
                            //     mainAxisAlignment: MainAxisAlignment.center,
                            //     children: thirdRow
                            //         .map((icon) => Padding(
                            //               padding: EdgeInsets.only(right: 10.w),
                            //               child:
                            //                   _buildHomeIcon(icon, iconSize, context),
                            //             ))
                            //         .toList(),
                            //   ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          );
  }

  Widget _buildHomeIcon(HomeIcon item, double width, BuildContext context,
      UserProfile? userProfile) {
    return GestureDetector(
      onTap: () {
        if (item.id == "gpt") {
          if (userProfile!.currentPlan!.planName == PlanType.Free.name) {
            showDialog(
              context: context,
              builder: (context) => const PremiumCookingDialog(),
            );
          } else {
            context.go('/home/<USER>');
          }
        } else if (item.id == "meal") {
          context.go('/home/<USER>');
        } else if (item.id == "masterList") {
          context.go('/home');
        } else if (item.id == "account") {
          context.go('/myAccount');
        } else if (item.id == "recipeCliper") {
          showDialog(
            context: context,
            useRootNavigator: true, //<--- add this
            barrierDismissible: false,
            builder: (_) => RecipeClipperDialog(),
          );
        } else if (item.id == "sync") {
          final profileState = ref.watch(userProfileNotifierProvider).data;
          var isMasterCook24User =
              profileState?.userProfile?.isMasterCook24User ?? false;
          if (isMasterCook24User) {
            ref.read(syncNotifierProvider.notifier).initialize();

            showDialog(
              context: context,
              useRootNavigator: true, //<--- add this
              barrierDismissible: false,
              builder: (_) => SyncDialog(),
            );
          } else {
            Utils().showFlushbar(context,
                message:
                    'No data available for sync. Please ensure your account has content to sync from MasterCook24.',
                isError: true);
          }
        } else {
          if (userProfile!.currentPlan!.planName == PlanType.Free.name &&
              item.id == 'shopping') {
            showDialog(
              context: context,
              builder: (context) => const PremiumCookingDialog(),
            );
          } else {
            context.go('/${item.id}');
          }
        }
      },
      child: SizedBox(
        width: width,
        height: width, // Ensure square container
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              // height: 120.w,
              alignment: Alignment.center,
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    // Soft shadow color
                    blurRadius: 8,
                    // Softness
                    offset: const Offset(0, 4), // Shadow position
                  ),
                ],
              ),

              child: SvgPicture.asset(
                item.assetPath,
                width: DeviceUtils().isTabletOrIpad(context) ? 62 : 120.w,
                // height: 120.w,
                fit: BoxFit.contain,
              ),
            ),
            SizedBox(height: 10.h),
            CustomText(
              text: item.label,
              size: 14,
              weight: FontWeight.w400,
              color: Colors.white,
              align: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

final homeScreenProvider = Provider((ref) => HomeScreenController());
final TextEditingController searchController = TextEditingController();

class HomeScreenController {
  List<HomeIcon> get icons => [
        HomeIcon('Cookbooks', AssetsManager.cookbook_svg, "cookbook"),
        HomeIcon('Shopping', AssetsManager.ic_shopping_premium, "shopping"),
        HomeIcon('Recipe Clipper', AssetsManager.recipe_cliper, "recipeCliper"),
        HomeIcon('GPT', AssetsManager.ic_gpt_premium, "gpt"),
        HomeIcon('Sync', AssetsManager.sync_svg, "sync"),
        // HomeIcon('Tips', AssetsManager.tips_svg, "tip"),
        // HomeIcon('Favorites', AssetsManager.fav_svg, "fav"),
        // HomeIcon('MasterList', AssetsManager.masterList_svg, "masterList"),
        // HomeIcon('Media', AssetsManager.media_svg, "media"),
        // HomeIcon('Backup', AssetsManager.backup_svg, "backUp"),
        HomeIcon('Account', AssetsManager.myProfile, "account"),
        //  HomeIcon('Meals', AssetsManager.meals_svg, "meal"),
      ];
}

class HomeIcon {
  final String label;
  final String id;
  final String assetPath;

  HomeIcon(this.label, this.assetPath, this.id);
}
