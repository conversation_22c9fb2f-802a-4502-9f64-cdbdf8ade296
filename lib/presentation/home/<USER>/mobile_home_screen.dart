import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_mobile_searchbar.dart';
import 'package:mastercookai/presentation/cookbook/cookbook_screen.dart';
import 'package:mastercookai/presentation/cookbook/mobile/recipe_clipper_screen.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/providers/profile/user_profile_notifier.dart';
import '../../../core/data/models/plan_type.dart';
import '../../../core/data/models/view_type.dart';
import '../../../core/providers/categories_notifier.dart';
import '../../../core/providers/cuisines_notifier.dart';
import '../../../core/providers/home/<USER>';
import '../../../core/providers/menus/bottom_nav_notifier.dart';
import '../../../core/providers/sync_notifier.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_crausal_mobile.dart';
import '../../../core/widgets/custom_text.dart';
import '../../../core/widgets/paginated_responsive_grid_list.dart';
import '../../../core/widgets/premium_cooking_dialog.dart';
import '../../cookbook/widgets/recipe_clipper_dialog.dart';
import '../../shimer/banner_shimmer.dart';
import '../../shopping/shopping_list_screen.dart';
import '../../sync/mobile_ui/mobile_sync_screen.dart';
import '../../sync/sync_dialog.dart';

class MobileHomeScreen extends ConsumerStatefulWidget {
  const MobileHomeScreen({super.key});

  @override
  ConsumerState<MobileHomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<MobileHomeScreen> {
  int? _previousBottomNavIndex;

  @override
  void initState() {
    final homeNotifier = ref.read(homeNotifierProvider.notifier);

    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
      ref
          .read(categoriesNotifierProvider.notifier)
          .fetchCategories(context: context);
      ref
          .read(cuisinesNotifierProvider.notifier)
          .fetchCuisines(context: context);
      homeNotifier.banners(context, getDeviceType(context).name);
    });
  }

  @override
  Widget build(BuildContext context) {
    final homestate = ref.watch(homeNotifierProvider);
    final bannerImages = homestate.data?.data.banners;
    var userProfile = ref.watch(userProfileNotifierProvider).data?.userProfile;
    final bottomNavNotifier = ref.read(bottomNavProvider.notifier);

    // Listen to bottom navigation changes and refresh user profile when Home tab is selected
    final currentBottomNavIndex = ref.watch(bottomNavProvider);

    // Check if we've returned to Home tab (index 0) from another tab
    if (_previousBottomNavIndex != null &&
        _previousBottomNavIndex != 0 &&
        currentBottomNavIndex == 0) {
      // Refresh user profile when returning to Home tab
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
      });
    }
    _previousBottomNavIndex = currentBottomNavIndex;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        toolbarHeight: 100,
        // Increased height for the header content
        backgroundColor: Colors.white,
        elevation: 0,
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0),
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 200,
                    child: CustomText(
                      text: userProfile?.name != null &&
                              userProfile!.name.isNotEmpty
                          ? 'Hey ${userProfile.name}!'
                          : '',
                      // Use a default name if null or empty
                      size: responsiveFont(14),
                      weight: FontWeight.w400,
                      color: AppColors.primaryGreyColor,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    Utils().getGreetingMessage(),
                    style: TextStyle(
                      fontSize: 18,
                      color: AppColors.primaryGreyColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Spacer(),
              InkWell(
                onTap: () {
                  //context.go(Routes.myAccount);
                  bottomNavNotifier.onTabSelected(3);
                },
                child: CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.grey.shade200,
                  backgroundImage:
                      const AssetImage(AssetsManager.profile_placeholder),
                  foregroundImage: userProfile?.profilePic != null
                      ? NetworkImage(userProfile!.profilePic!)
                      : const AssetImage(AssetsManager.profile_placeholder),
                ),
              ),
              const SizedBox(height: 2),
            ],
          ),
        ),
      ),
      body: SingleChildScrollView(
          // Wrap the Column with SingleChildScrollViehome_icons
          child: Column(
        children: [
          if (homestate.status == AppStatus.loading ||
              bannerImages == null ||
              bannerImages.isEmpty)
            BannerShimmer(
              itemsPerPage: 1,
            )
          else
            BannerCarouselMobile(
              bannerImages: bannerImages,
              onImageClick: (index) {
                Utils().launchURL(url: 'https://mastercook.ai/');
                // This is the function that will be called when an image is clicked
                debugPrint('Banner image at index $index was clicked!');
                // Or show a snackbar
              },
            ),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Row(
                children: <Widget>[
                  // Start Line
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.only(right: 20.0),
                      child: Divider(
                        color: AppColors.lightestGreyColor,
                        height: 36,
                        thickness: 1,
                      ),
                    ),
                  ),

                  // Text
                  const Text(
                    "EXPLORE",
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.darkGreyColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  // End Line
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.only(left: 20.0),
                      child: Divider(
                        color: AppColors.lightestGreyColor,
                        height: 36,
                        thickness: 1,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 26),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
            child: GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: getDeviceType(context).name == 'tablet' ? 5 : 3,
              crossAxisSpacing: 16,
              mainAxisSpacing: 25,
              children: [
                _buildGridItem(
                  AssetsManager.cookbook_svg,
                  'Cookbooks',
                  () {
                    bottomNavNotifier.onTabSelected(1);
                  },
                ),
                _buildGridItem(
                  AssetsManager.ic_shopping_premium,
                  'Shopping',
                  () {
                    if (userProfile!.currentPlan!.planName ==
                        PlanType.Free.name) {
                      showDialog(
                        context: context,
                        builder: (context) => const PremiumCookingDialog(),
                      );
                    } else {
                      bottomNavNotifier.onTabSelected(2);
                    }
                  },
                ),
                _buildGridItem(
                  AssetsManager.recipe_cliper,
                  'Recipe Clipper',
                  () {
                    if (getDeviceType(context).name == 'mobile') {
                      context.go('/home/<USER>');
                    } else {
                      showDialog(
                        context: context,
                        useRootNavigator: true, //<--- add this
                        barrierDismissible: false,
                        builder: (_) => RecipeClipperDialog(),
                      );
                    }
                  },
                ),
                _buildGridItem(
                  AssetsManager.ic_gpt_premium,
                  'GPT',
                  () {
                    if (userProfile!.currentPlan!.planName ==
                        PlanType.Free.name) {
                      showDialog(
                        context: context,
                        builder: (context) => const PremiumCookingDialog(),
                      );
                    } else {
                      if (getDeviceType(context).name == 'tablet' ||
                          getDeviceType(context).name == 'ipad') {
                        context.go('/home/<USER>');
                      } else {
                        context.go('/home/<USER>');
                      }
                    }
                  },
                ),
                _buildGridItem(
                  AssetsManager.sync_svg,
                  'Sync',
                  () {
                    final profileState =
                        ref.watch(userProfileNotifierProvider).data;
                    var isMasterCook24User =
                        profileState?.userProfile?.isMasterCook24User ?? false;
                    if (isMasterCook24User) {
                      if (getDeviceType(context).name == 'mobile') {
                        context.go('/home/<USER>');
                      } else {
                        ref.read(syncNotifierProvider.notifier).initialize();
                        showDialog(
                          context: context,
                          useRootNavigator: true, //<--- add this
                          barrierDismissible: false,
                          builder: (_) => SyncDialog(),
                        );
                      }
                    } else {
                      Utils().showFlushbar(context,
                          message:
                              'No data available for sync. Please ensure your account has content to sync from MasterCook24.',
                          isError: true);
                    }
                  },
                ),
                _buildGridItem(
                  AssetsManager.myProfile,
                  'Account',
                  () {
                    bottomNavNotifier.onTabSelected(3);
                  },
                ),
              ],
            ),
          ),
          SizedBox(height: 10)
        ],
      )),
    );
  }
}

Widget _buildGridItem(String iconPath, String title, VoidCallback onTap) {
  return InkWell(
      splashColor: Colors.transparent,
      onTap: onTap,
      child: Container(
        // The main card container
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              spreadRadius: 2,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          clipBehavior: Clip.none,
          // Allows children to render outside the Stack's bounds
          children: [
            // The main card content, offset from the top
            Padding(
              padding: const EdgeInsets.only(top: 75.0),
              // Adjust padding to prevent overlap
              child: Column(
                // mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.black, // Using a generic black color
                      ),
                    ),
                  ),
                  const SizedBox(height: 5), // Adjust spacing
                ],
              ),
            ),
            // The image positioned with a negative top margin
            Positioned(
              top: -20,
              // Adjust this value to control how much the image pops out
              child: Container(
                margin: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      spreadRadius: 2,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: SvgPicture.asset(
                  iconPath,
                  width: 75,
                  height: 75,
                ),
              ),
            ),
          ],
        ),
      ));
}

List<HomeIcon> get icons => [
      HomeIcon('Cookbooks', AssetsManager.cookbook_svg, "cookbook"),
      HomeIcon('Shopping', AssetsManager.ic_shopping_premium, "shopping"),
      HomeIcon('Recipe Cliper', AssetsManager.recipe_cliper, "recipeCliper"),
      HomeIcon('GPT', AssetsManager.ic_gpt_premium, "gpt"),
      HomeIcon('Sync', AssetsManager.sync_svg, "sync"),
      // HomeIcon('Tips', AssetsManager.tips_svg, "tip"),
      // HomeIcon('Favorites', AssetsManager.fav_svg, "fav"),
      // HomeIcon('MasterList', AssetsManager.masterList_svg, "masterList"),
      // HomeIcon('Media', AssetsManager.media_svg, "media"),
      // HomeIcon('Backup', AssetsManager.backup_svg, "backUp"),
      HomeIcon('Account', AssetsManager.myProfile, "account"),
      //  HomeIcon('Meals', AssetsManager.meals_svg, "meal"),
    ];

class HomeIcon {
  final String label;
  final String id;
  final String assetPath;

  HomeIcon(this.label, this.assetPath, this.id);
}
