
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../app/imports/packages_imports.dart';
import '../../app/theme/themes.dart';
import '../../main.dart';
import '../core/network/app_repository.dart';
import '../../core/widgets/custom_button.dart';


class StartUpErrorScreen extends HookConsumerWidget {
  const StartUpErrorScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isRetrying = useState(false);

    ScreenUtil.init(
      context,
      minTextAdapt: true,
      splitScreenMode: true,
    );
    void handleRetry() async {
      isRetrying.value = true;
      final hasConnection = await providerContainer
          .read(appRepositoryProvider)
          .hasInternetConnection();
      if (hasConnection) {
        isRetrying.value = false;

        runApp(
          UncontrolledProviderScope(
            container: providerContainer,
            child: const MyApp(),
          ),
        );
      }
    }

    return MaterialApp(
      title: 'MasterCook AI',
      debugShowCheckedModeBanner: false,
      theme: light,
      home: Scaffold(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FlutterRemix.wifi_off_fill,
                size: 70.sp,
                color: context.theme.disabledColor.withValues(alpha: 0.1),
              ),
              const SizedBox(height: 20),
              Text(
                'No Internet Connection',
                style: context.textTheme.titleLarge?.copyWith(
                  color: context.theme.disabledColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                'Please check your internet connection\nand try again.',
                textAlign: TextAlign.center,
                style: context.textTheme.labelMedium?.copyWith(
                  color: context.theme.disabledColor.withValues(alpha: 0.5),
                  fontWeight: FontWeight.w400,
                ),
              ),
              SizedBox(height: context.height * 0.1),
              CustomButton(
                text: 'Retry',
                width: context.width * 0.5,
                isLoading: isRetrying.value,
                isDisabled: isRetrying.value,
                onPressed: handleRetry,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
