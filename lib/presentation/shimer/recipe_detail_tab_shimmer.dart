import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

class RecipeDetailTabShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 30.h, bottom: 30.h),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white, // Actual card color
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Padding(
              padding: EdgeInsets.all(24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Recipe title shimmer
                  Container(
                    width: double.infinity,
                    height: 50,
                    color: Colors.white, // This will be shimmered
                  ),
                  SizedBox(height: 20.h),
                  Container(
                    width: double.infinity,
                    height: 2,
                    color: Colors.white,
                  ),
                  SizedBox(height: 20.h),
                  Container(
                    width: double.infinity,
                    height: 80,
                    color: Colors.white,
                  ),
                  SizedBox(height: 30.h),

                  // Recipe image shimmer
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 3,
                        child: Column(
                          children: [
                            Container(
                              height: 300,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12), // Adjust radius as needed
                              ),
                            ),
                            SizedBox(height: 20.h),
                            Container(
                              height: 80,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12), // Same radius for consistency
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 30.w),
                      Expanded(
                        flex: 2,
                        child: Container(
                          height: 400,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.grey[300]!, width: 1),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 40.h),

                  // Directions shimmer
                  Container(
                    width: 200,
                    height: 30,
                    color: Colors.white,
                  ),
                  SizedBox(height: 20.h),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: 3,
                    itemBuilder: (context, index) {
                      return Container(
                        height: 200,
                        margin: EdgeInsets.only(bottom: 20.h),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[300]!, width: 1),
                        ),
                      );
                    },
                  ),
                  SizedBox(height: 40.h),

                  // Serving ideas shimmer
                  Container(
                    height: 150,
                    color: Colors.white,
                  ),
                  SizedBox(height: 40.h),

                  // Wine pairing shimmer
                  Container(
                    height: 150,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}