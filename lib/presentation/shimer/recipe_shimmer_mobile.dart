import 'package:flutter/material.dart';
import 'package:responsive_grid/responsive_grid.dart';
import 'package:shimmer/shimmer.dart';
import '../../app/imports/core_imports.dart';
import '../../core/utils/device_utils.dart';

class RecipeShimmerMobile extends StatelessWidget {
  final int crossAxisCount;
  final double childAspectRatio;
  final int itemsPerPage;

  const RecipeShimmerMobile({
    super.key,
    required this.crossAxisCount,
    required this.itemsPerPage,
    this.childAspectRatio = 0.78,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;
    // Use the provided crossAxisCount or calculate based on device type
    final effectiveCrossAxisCount = crossAxisCount > 0
        ? crossAxisCount
        : (DeviceUtils().isTabletOrIpad(context) ? 3 : isHighRes ? 5 : 5).toInt();

    return ResponsiveGridList(
      shrinkWrap: true,
      desiredItemWidth: 164, // Match desired item width for recipes
      minSpacing: 10,
      children: List.generate(itemsPerPage, (index) {
        return _buildShimmerCard(context);
      }),
    );
  }

  Widget _buildShimmerCard(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      elevation: 2,
      color: Colors.grey[200],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: SizedBox(
        width: double.infinity,
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[200]!,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image placeholder
                Container(
                  width: double.infinity,
                  height: 100, // Fixed height for image placeholder
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(height: 12),
                // Title placeholder
                Container(
                  width: double.infinity,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 8),
                // Subtitle placeholder
                Container(
                  width: 100,
                  height: 14,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 12),
                // Additional text placeholder
                Container(
                  width: double.infinity,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 8),
                // Button or additional placeholder
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 30, vertical: 0),
                  width: 200,
                  height: 34,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}