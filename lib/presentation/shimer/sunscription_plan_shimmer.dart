import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerSubscriptionPlan extends StatelessWidget {
  final int firstListCount;
  final int secondListCount;
  final bool showBothLists;

  const ShimmerSubscriptionPlan({
    super.key,
    this.firstListCount = 10,
    this.secondListCount = 2,
    this.showBothLists = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (showBothLists) _buildFirstList(),
        // _buildSecondList(),
      ],
    );
  }

  Widget _buildFirstList() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: firstListCount,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
            child:Row(
              children: [
                // First box - wide (e.g., product name)
                Expanded(
                  flex: 2, // large width
                  child: Container(
                    height: 24,
                    margin: const EdgeInsets.only(right: 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                  ),
                ),

                // Repeating small boxes (checkboxes)
                for (int i = 0; i < 3; i++)
                  Expanded(
                    flex: 1,
                    child: Container(
                      height: 24,
                      margin: const EdgeInsets.only(right: 16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                    ),
                  ),


              ],
            )
          );
        },
      ),
    );
  }
  //
  // Widget _buildSecondList() {
  //   return Expanded(
  //     child: Shimmer.fromColors(
  //       baseColor: Colors.grey[300]!,
  //       highlightColor: Colors.grey[100]!,
  //       child: ListView.builder(
  //         itemCount: secondListCount,
  //         itemBuilder: (context, index) {
  //           return Padding(
  //             padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 // Store info placeholder
  //                 Container(
  //                   height: 16,
  //                   width: double.infinity,
  //                   margin: const EdgeInsets.only(bottom: 4),
  //                   decoration: BoxDecoration(
  //                     color: Colors.white,
  //                     borderRadius: BorderRadius.circular(4),
  //                   ),
  //                 ),
  //
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   children: [
  //                     // Product name placeholder
  //                     Container(
  //                       height: 18,
  //                       width: index.isEven ? 120 : 100,
  //                       decoration: BoxDecoration(
  //                         color: Colors.white,
  //                         borderRadius: BorderRadius.circular(4),
  //                       ),
  //                     ),
  //
  //                     // Price placeholder
  //                     Container(
  //                       height: 18,
  //                       width: 60,
  //                       decoration: BoxDecoration(
  //                         color: Colors.white,
  //                         borderRadius: BorderRadius.circular(4),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           );
  //         },
  //       ),
  //     ),
  //   );
  // }
}