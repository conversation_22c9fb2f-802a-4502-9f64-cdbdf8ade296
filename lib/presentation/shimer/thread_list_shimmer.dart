import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

class ThreadListShimmer extends StatelessWidget {
  final int itemCount;

  const ThreadListShimmer({super.key, this.itemCount = 8});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      itemCount: itemCount + 2, // Add 2 for section headers
      itemBuilder: (context, index) {
        // Show section header shimmer every few items
        if (index == 0 || index == 4) {
          return _buildSectionHeaderShimmer();
        }
        
        return _buildThreadItemShimmer();
      },
    );
  }

  Widget _buildSectionHeaderShimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Padding(
        padding: EdgeInsets.only(top: 24.h, bottom: 12.h),
        child: Container(
          height: 20.h,
          width: 120.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  Widget _buildThreadItemShimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        margin: EdgeInsets.only(bottom: 8.h),
        child: Row(
          children: [
            Expanded(
              child: Container(
                height: 18.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
            SizedBox(width: 8.w),
            // Three dots menu shimmer
            Container(
              width: 20.w,
              height: 18.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
