import 'package:mastercookai/core/utils/screen_sizer.dart';
import 'package:paged_grid_view/paged_grid_view.dart';
import 'package:shimmer/shimmer.dart';

import '../../app/imports/core_imports.dart';
import '../../core/utils/Utils.dart';
import '../../core/utils/device_utils.dart';

class RecipeShimmer extends StatelessWidget {
  final int crossAxisCount;
  final double childAspectRatio;
  final int itemsPerPage;

  const RecipeShimmer(
      {super.key, required this.crossAxisCount, required this.itemsPerPage,
      this.childAspectRatio = 0.78
      });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;
    final crossAxisCount =(DeviceUtils().isTabletOrIpad(context)?3: isHighRes ? 5 : 5).toInt();
    final size = MediaQuery.of(context).size;

    return
      PagedGridView.builder(
       itemCount: DeviceUtils().isTabletOrIpad(context)?9:10 ,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(

        crossAxisCount: crossAxisCount,
        mainAxisSpacing: size.height * 0.002,
        crossAxisSpacing: size.width * 0.002,
        childAspectRatio:childAspectRatio,

      ),
      scrollDirection: Axis.vertical,
      itemBuilder: (context, index) {
        return Card(
          margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          elevation: 2,
          color: Colors.grey[200]!,
          // Card background color
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          child: SizedBox(
            // height: 140, // Increased height to accommodate image
            width: double.infinity,
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[200]!,
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Image placeholder
                    Container(
                      width: double.infinity,
                     // height: 100,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Text placeholder 1
                    Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Text placeholder 2
                    Container(
                      width: 100,
                      height: 14,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Text placeholder 1
                    Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Text placeholder 2
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 30, vertical: 0),
                      width: 200,
                      height: 34,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
