import 'package:shimmer/shimmer.dart';
import '../../app/imports/core_imports.dart';

class BannerShimmer extends StatelessWidget {
  final int itemsPerPage;

  const BannerShimmer({super.key, required this.itemsPerPage});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: SizedBox(
        width: double.infinity,
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          period: const Duration(seconds: 2), // Smooth animation
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: List.generate(itemsPerPage, (index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Container(
                    width: 900, // Responsive width
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                );
              }),
            ),
          ),
        ),
      ),
    );
  }
}
