import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class MobileRecipeDetailShimmer extends StatelessWidget {
  const MobileRecipeDetailShimmer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                // Distribute the containers evenly
                children: [
                  Container(
                    height: 40,
                    width: 110,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                  Container(
                    height: 40,
                    width: 110,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                  Container(
                    height: 40,
                    width: 110,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),

              // Recipe Image Placeholder
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.0),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                // Distribute the containers evenly
                children: [
                  Container(
                    height: 40,
                    width: 110,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                  Container(
                    height: 40,
                    width: 110,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                  Container(
                    height: 40,
                    width: 110,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 5),
              Divider(),
              const SizedBox(height: 5),

              // Title Placeholder
              Container(
                height: 24,
                width: 250,
                color: Colors.white,
              ),
              const SizedBox(height: 5),



              const SizedBox(height: 5),

              // Ingredient List Placeholders
              _buildShimmerListItem(),
              const SizedBox(height: 8),
              _buildShimmerListItem(),
              const SizedBox(height: 8),
              _buildShimmerListItem(),
              const SizedBox(height: 24),

              // "Directions" Section Title Placeholder
              Container(
                height: 20,
                width: 200,
                color: Colors.white,
              ),
              const SizedBox(height: 12),

              // Directions List Placeholders
              _buildShimmerListItem(),
              const SizedBox(height: 8),
              _buildShimmerListItem(),
              const SizedBox(height: 8),
              _buildShimmerListItem(),
            ],
          ),
        ),
      ),
    );
  }

  // A helper method to create a list item placeholder
  Widget _buildShimmerListItem() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [

        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 16,
                width: 200,
                color: Colors.white,
              ),
              const SizedBox(height: 8),
              Container(
                height: 12,
                width: 130,
                color: Colors.white,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
