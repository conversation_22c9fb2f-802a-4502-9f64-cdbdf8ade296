import 'package:mastercookai/core/utils/screen_sizer.dart';
import 'package:shimmer/shimmer.dart';

import '../../app/imports/core_imports.dart';
import '../../core/utils/device_utils.dart';

class CookbookShimmer extends StatelessWidget {
  final int crossAxisCount;
  final int itemsPerPage;

  const CookbookShimmer(
      {super.key, required this.crossAxisCount, required this.itemsPerPage});

  @override
  Widget build(BuildContext context) {
    return Padding(
          padding: EdgeInsets.only(
      top: getDeviceType(context).name == 'mobile' ? 0 : 50),
          child: GridView.builder(
    shrinkWrap: true,
    padding: EdgeInsets.symmetric(
      horizontal: MediaQuery.of(context).size.width * 0.03,
      vertical: MediaQuery.of(context).size.width * 0.03,
    ),
    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: crossAxisCount,
      mainAxisSpacing: MediaQuery.of(context).size.height * 0.004,
      crossAxisSpacing: MediaQuery.of(context).size.width * 0.004,
      childAspectRatio: ScreenSizer().calculateChildAspectRatio(
        MediaQuery.of(context).size.width,
        MediaQuery.of(context).size.height,
      ),
    ),
    itemCount: itemsPerPage,
    itemBuilder: (context, index) {
      return Card(
        margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        elevation: 2,
        color: Colors.grey[200]!,
        // Card background color
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: SizedBox(
          //  height: 140, // Increased height to accommodate image
          width: double.infinity,
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[200]!,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image placeholder
                  Container(
                    width: double.infinity,
                    //  height: 120,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Text placeholder 1
                  Container(
                    width: double.infinity,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Text placeholder 2
                  Container(
                    width: 100,
                    height: 14,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Text placeholder 1
                  Container(
                    width: double.infinity,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Text placeholder 2
                  Container(
                    margin:
                        EdgeInsets.symmetric(horizontal: 30, vertical: 0),
                    width: 200,
                    height: 34,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
          ),
        );
  }
}
