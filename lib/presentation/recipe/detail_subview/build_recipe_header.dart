import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/recipe/detail_subview/recipe_nutrition_facts_dialog.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/providers/nutrions_notifier.dart';
import '../../../core/widgets/custom_doted_lines.dart';
import '../../../core/widgets/expandable_desc_text.dart';

class RecipeHeader extends ConsumerStatefulWidget {
  final RecipeDetails recipeDetail;

  const RecipeHeader({
    super.key,
    required this.recipeDetail,
  });

  @override
  ConsumerState<RecipeHeader> createState() => _RecipeHeaderState();
}

class _RecipeHeaderState extends ConsumerState<RecipeHeader> {
  @override
  Widget build(BuildContext context) {
    final nutritionDataState = ref.watch(nutritionInfoNotifierProvider);

    // Check if nutrition analysis should be visible
    bool showNutritionAnalysis = nutritionDataState.status == AppStatus.success &&
        nutritionDataState.data?.nutrients != null &&
        DeviceUtils().isTabletOrIpad(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  right: showNutritionAnalysis ? 20.w : 0, // Add padding when nutrition analysis is visible
                  top: 4.h, // Add small top padding to align with nutrition analysis
                ),
                child: CustomText(
                  text: widget.recipeDetail.name ?? '',
                  size: 30,
                  weight: FontWeight.w700,
                  color: AppColors.primaryGreyColor,
                  overflow: TextOverflow.ellipsis,
                  maxLine: 2, // Allow title to wrap to 2 lines if needed
                ),
              ),
            ),
            Visibility(
              visible: showNutritionAnalysis,
              child: GestureDetector(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return RecipeNutritionFactsDialog(
                        recipeDetails: widget.recipeDetail,
                        nutritionData: nutritionDataState.data,
                      );
                    },
                  );
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(AssetsManager.ic_nutrions),
                    SizedBox(width: 4.w),
                    CustomText(
                      text: 'Nutrition Analysis',
                      weight: FontWeight.w500,
                      size: 14,
                      overflow: TextOverflow.ellipsis,
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        CustomPaint(
          painter: DottedLinePainter(
            strokeWidth: 1,
            dashWidth: 6,
            color: AppColors.lightestGreyColor,
          ),
          size: Size(double.infinity, 2),
        ),
        SizedBox(height: 20.h),
        ExpandableDescText(
          desc: widget.recipeDetail.description ?? '',
          textColor: AppColors.primaryLightTextColor,
          size: 14,
        ),
      ],
    );
  }
}
