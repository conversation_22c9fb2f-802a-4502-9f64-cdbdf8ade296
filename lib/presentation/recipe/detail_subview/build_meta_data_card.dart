import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

import '../../../app/imports/core_imports.dart';

Widget buildMetadataCard({
  required BuildContext context,
  required String icon,
  required String label,
  required String value,
  Color? iconColor,
}) {
  return Container(
    padding: EdgeInsets.only(left: 20, right: 10),
    decoration: BoxDecoration(
      color: AppColors.greyCardColor,
      borderRadius: BorderRadius.circular(10),
    ),
    child: Center(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(icon),
          SizedBox(width: 10),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                text:   value,
                  color: AppColors.primaryGreyColor,
                  weight: FontWeight.w700,
                  size: 12,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 5.h),
                CustomText(
                  text: label,
                  color: AppColors.textGreyColor,
                  weight: FontWeight.w400,
                  size: 12,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}