import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';
import 'package:mastercookai/core/widgets/custom_info_cards.dart';

import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/category_response.dart';
import '../../../core/data/models/cuisines_response.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/providers/single_recipe_notifier.dart';
import '../../../core/widgets/custom_text.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import '../../cookbook/widgets/edit_button.dart';

Widget buildNotesSection(
    BuildContext context,
    String notes,
    RecipeDetails recipeDetail,
    List<Categories>? categoriesList,
    List<Cuisines>? cuisinesList,
    int recipeId,
    int cookbookId,
    List<Recipe> recipesList,
    {required WidgetRef ref}) {
  return Container(
    padding: EdgeInsets.all(24.sp),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CustomeTitleText(title: 'Notes'),
            Spacer(),
            Visibility(
              visible: notes.isNotEmpty,
              child: Align(
                alignment: Alignment.topRight,
                child: EditButton(onPressed: () {
                  ref
                      .read(singleRecipeNotifierProvider.notifier)
                      .goToEditRecipeScreen(context, recipeId, cookbookId,
                      recipesList, recipeDetail, categoriesList!, cuisinesList!,
                      initialStep: 4); // Step 4 is Notes
                }),
              ),
            ),
          ],
        ),
        notes.isEmpty
            ? CustomInfoCard(
                title: 'No notes added yet',
                subheading:
                    'Add preparation tips, ingredient swaps, or personal twists',
                actionText: 'Add Notes',
                onActionPressed: () {
                  ref
                      .read(singleRecipeNotifierProvider.notifier)
                      .goToEditRecipeScreen(context, recipeId, cookbookId,
                      recipesList, recipeDetail, categoriesList!, cuisinesList!,
                      initialStep: 4); // Step 4 is Notes
                })
            : CustomText(
                size: 14,
                text: notes,
                color: AppColors.primaryLightTextColor,
              ),
        SizedBox(height: 20.h),
      ],
    ),
  );
}
