import 'package:mastercookai/core/data/models/nutritionInfo_response.dart';

import 'package:mastercookai/core/network/app_status.dart';

import '../../../app/imports/packages_imports.dart';
import '../../../core/data/models/category_response.dart';
import '../../../core/data/models/cuisines_response.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/data/models/recipe_response.dart';
import '../../../core/providers/nutrions_notifier.dart';
import 'build_author_section.dart' show buildAuthorSection;
import 'build_ingredients_section.dart';
import 'build_notes_section.dart';
import 'build_nutrition_section.dart';

Widget responsiveRightView({
  required BuildContext context,
  required List<String>? ingredients,
  required String author,
  required String authorMediaUrl,
  required String copyright,
  required String source,
  required RecipeDetails recipeDetail,
  required List<Categories>? categoryList,
  required List<Cuisines>? cusineList,
  required int recipeId,
  required int cookbookId,
  required List<Recipe> recipesList,
  required int? selectedRecipeId,
  required int? selectedCookbookId,
  required WidgetRef ref,
  bool isSmallScreen = false,
  required AppState<NutritionInfoData> nutritionDataState,
}) {
  Widget content;

  if (isSmallScreen) {
    // For small screens: 2x2 grid layout
    content = Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // First row: Ingredients and Author info
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              //  Ingredients section
              Expanded(
                child: buildIngredientsSection(
                    context,
                    recipesList,
                    selectedCookbookId ?? 0,
                    selectedRecipeId ?? 0,
                    recipeDetail.ingredients!,
                    recipeDetail,
                    categoryList,
                    cusineList,
                    ref: ref),
              ),
              SizedBox(width: 20.w),
              //Author section
              Expanded(
                child: buildAuthorSection(
                  context,
                  recipeDetail.author ?? '',
                  recipeDetail.authorMediaUrl ?? '',
                  recipeDetail.copyright ?? '',
                  recipeDetail.source ?? '',
                  recipeDetail,
                  categoryList,
                  cusineList,
                  recipeId,
                  cookbookId,
                  recipesList,
                  ref: ref,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 30.h),
        // Second row: Notes and Nutrition facts
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // // Notes section
              Expanded(
                child: buildNotesSection(
                  context,
                  recipeDetail.notes ?? '',
                  recipeDetail,
                  categoryList!,
                  cusineList,
                  recipeId,
                  cookbookId,
                  recipesList,
                  ref: ref,
                ),
              ),
              SizedBox(width: 20.w),
              // Nutrition section
              NutritionSection(recipeDetail: recipeDetail,),
            ],
          ),
        ),
      ],
    );
  } else {
    // For large screens: original single column layout
    content = Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (ingredients != null)
          buildIngredientsSection(
              context,
              recipesList,
              selectedCookbookId ?? 0,
              selectedRecipeId ?? 0,
              recipeDetail.ingredients!,
              recipeDetail,
              categoryList,
              cusineList,
              ref: ref),
        SizedBox(height: 30.h),
        NutritionSection(recipeDetail: recipeDetail,),
        SizedBox(height: 30.h),

        buildAuthorSection(
          context,
          recipeDetail.author ?? '',
          recipeDetail.authorMediaUrl ?? '',
          recipeDetail.copyright ?? '',
          recipeDetail.source ?? '',
          recipeDetail,
          categoryList,
          cusineList,
          recipeId,
          cookbookId,
          recipesList,
          ref: ref,
        ),
        SizedBox(height: 30.h),
        buildNotesSection(
          context,
          recipeDetail.notes ?? '',
          recipeDetail,
          categoryList,
          cusineList,
          recipeId,
          cookbookId,
          recipesList,
          ref: ref,
        ),
      ],
    );
  }

  if (isSmallScreen) {
    // For small screens, return content without Expanded wrapper and no ScrollView
    return Padding(
      padding:
          EdgeInsets.only(left: 20.w, right: 20.w, top: 30.h, bottom: 30.h),
      child: content,
    );
  } else {
    // For large screens, return with Expanded wrapper (original behavior)
    return Expanded(
      flex: 2,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding:
              EdgeInsets.only(left: 20.w, right: 20.w, top: 30.h, bottom: 30.h),
          child: content,
        ),
      ),
    );
  }
}
