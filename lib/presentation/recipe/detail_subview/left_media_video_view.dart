import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/category_response.dart';
import 'package:mastercookai/core/data/models/cuisines_response.dart';
import 'package:mastercookai/core/data/models/nutritionInfo_response.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/presentation/recipe/detail_subview/build_recipe_header.dart';
import 'package:mastercookai/presentation/recipe/detail_subview/build_recipe_metadata.dart';

import '../../../app/imports/core_imports.dart';
import '../../../app/theme/colors.dart';
import '../../../core/helpers/app_constant.dart';
import '../../../core/widgets/common_image.dart';
import '../../../core/widgets/video_player_widget.dart';
import 'build_directions_list.dart';
import 'build_serving_ideas_section.dart';
import 'build_similar_recipes_list.dart';

Widget leftMediaVideoWidget(
    BuildContext context,
    RecipeDetails recipeDetail,
    String? selectedImageUrl,
    List<Categories>? categoryList,
    List<Cuisines>? cusineList,
    int recipeId,
    int cookbookId,
    List<Recipe> recipesList,
    {required Null Function(String newImageUrl) onImageSelected,
    required WidgetRef ref, required AppState<NutritionInfoData> nutritionDataState}) {
  return Expanded(
    flex: 6,
    child: SingleChildScrollView(
      child: Padding(
        padding:
            EdgeInsets.only(left: 20.w, right: 20.w, top: 30.h, bottom: 30.h),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.secondaryColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(
                right: 30.w, left: 30.w, top: 30.h, bottom: 30.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RecipeHeader( recipeDetail: recipeDetail,),
                SizedBox(height: 30),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Column(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: selectedImageUrl != null &&
                                    Utils().isVideo(selectedImageUrl)
                                ? SizedBox(
                                    height: 350,
                                    width: MediaQuery.of(context).size.width,
                                    child: AspectRatio(
                                      aspectRatio: 16 / 9,
                                      child: VideoPlayerWidget(
                                        mediaUrl: selectedImageUrl,
                                        thumbnailPath:
                                            recipeDetail.recipeThumbnailFileUrl,
                                        height: 360,
                                      ),
                                    ),
                                  )
                                : CommonImage(
                                    imageSource: selectedImageUrl ?? '',
                                    height: 360,
                                    width: MediaQuery.of(context).size.width,
                                    fit: BoxFit.cover,
                                    placeholder:
                                        AssetsManager.recipe_place_holder,
                                  ),
                          ),
                          SizedBox(height: 30.h),
                          if (recipeDetail.recipeMedia != null &&
                              recipeDetail.recipeMedia!.isNotEmpty)
                            SimilarRecipesWidget(
                                similarRecipes: recipeDetail.recipeMedia!,
                                recipeThumbnailFileUrl:
                                    recipeDetail.recipeThumbnailFileUrl ?? '',
                                onImageSelected: onImageSelected),
                        ],
                      ),
                    ),
                    SizedBox(width: 30.w),
                    Expanded(
                      flex: 2,
                      child: buildRecipeMetadata(
                          context,
                          recipeDetail,
                          recipeDetail,
                          categoryList,
                          cusineList,
                          recipeId,
                          cookbookId,
                          recipesList,
                          ref: ref),
                    ),
                  ],
                ),
                SizedBox(height: 40.h),
                if (recipeDetail.directions != null)
                  buildDirectionsList(
                      context,
                      recipeDetail.directions!,
                      recipeDetail,
                      categoryList,
                      cusineList,
                      recipeId,
                      cookbookId,
                      recipesList,
                      ref: ref),
                SizedBox(height: 40.h),
                buildServingIdeasSection(
                    recipeDetail.servingIdeas ?? '',
                    context,
                    recipeDetail.directions!,
                    recipeDetail,
                    categoryList,
                    cusineList,
                    recipeId,
                    cookbookId,
                    recipesList,
                    ref: ref),
                SizedBox(height: 40.h),
                buildWinePairingSection(
                    recipeDetail.wine ?? '',
                    context,
                    recipeDetail.directions!,
                    recipeDetail,
                    categoryList,
                    cusineList,
                    recipeId,
                    cookbookId,
                    recipesList,
                    ref: ref),
              ],
            ),
          ),
        ),
      ),
    ),
  );
}
