import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/Recipe_item_tabbar.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/utils/Utils.dart';
import '../../../core/widgets/common_image.dart';
import '../subview/custom_left_right_arrow.dart';

class SimilarRecipesWidget extends StatefulWidget {
  final List<RecipeMedia> similarRecipes;
  final String recipeThumbnailFileUrl;
  final Function(String) onImageSelected;

  const SimilarRecipesWidget({
    Key? key,
    required this.similarRecipes,
    required this.recipeThumbnailFileUrl,
    required this.onImageSelected,
  }) : super(key: key);

  @override
  _SimilarRecipesWidgetState createState() => _SimilarRecipesWidgetState();
}

class _SimilarRecipesWidgetState extends State<SimilarRecipesWidget> {
  final ScrollController _scrollController = ScrollController();
  int _selectedSimilarImageIndex = 0;
  final double itemWidth = 102.0; // Width of each item including padding

  @override
  void dispose() {
    _scrollController.dispose();

    super.dispose();
  }

  void _handleLeftArrowTap() {
    if (_selectedSimilarImageIndex > 0) {
      setState(() {
        _selectedSimilarImageIndex--;
        widget.onImageSelected(widget.similarRecipes[_selectedSimilarImageIndex].mediaUrl??'');
      });
      _scrollController.animateTo(
        _selectedSimilarImageIndex * itemWidth,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _handleRightArrowTap() {
    if (_selectedSimilarImageIndex < widget.similarRecipes.length - 1) {
      setState(() {
        _selectedSimilarImageIndex++;
        widget.onImageSelected(widget.similarRecipes[_selectedSimilarImageIndex].mediaUrl??'');
      });
      _scrollController.animateTo(
        _selectedSimilarImageIndex * itemWidth,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _handleImageTap(int index) {
    setState(() {
      _selectedSimilarImageIndex = index;
      widget.onImageSelected(widget.similarRecipes[index].mediaUrl??'');
    });
    _scrollController.animateTo(
      _selectedSimilarImageIndex * itemWidth,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  @override
  Widget build(BuildContext context) {
      return SizedBox(
        width: MediaQuery.of(context).size.width,
      height: 120.h,
      child: Row(
        children: [
       CustomRightLeftArrow(
            icon: AssetsManager.ic_left_arrow,
            onPressed: _handleLeftArrowTap,
          ),
          SizedBox(width: 10.w),
          Expanded(
            child: ListView.separated(
              controller: _scrollController,
              scrollDirection: Axis.horizontal,
              itemCount: widget.similarRecipes.length,
              padding: EdgeInsets.symmetric(horizontal: 20),
              separatorBuilder: (context, index) => SizedBox(width: 20.w),
              itemBuilder: (context, index) {
                final similarRecipe = widget.similarRecipes[index];
                return MouseRegion(
                  cursor: SystemMouseCursors.basic,
                  child: InkWell(
                    onTap: () => _handleImageTap(index),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _selectedSimilarImageIndex == index
                              ? Colors.blue
                              : Colors.transparent,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CommonImage(
                          imageSource: Utils().isVideo(similarRecipe.mediaUrl)
                              ? widget.recipeThumbnailFileUrl
                              : similarRecipe.mediaUrl ?? '',
                          placeholder: AssetsManager.recipe_place_holder,
                          width: 82,
                          height: 82,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          SizedBox(width: 10),
          CustomRightLeftArrow(
            icon: AssetsManager.ic_right_arrow,
            onPressed: _handleRightArrowTap,
          ),

        ],
      ),
    );
  }
}