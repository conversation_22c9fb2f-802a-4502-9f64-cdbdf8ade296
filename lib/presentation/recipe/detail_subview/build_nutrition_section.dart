import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/nutrions_notifier.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/presentation/recipe/detail_subview/recipe_nutrition_facts_dialog.dart';
import 'package:mastercookai/presentation/shimer/nutritions_shimmer.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/nutritionInfo_response.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/widgets/custom_text.dart';

import 'build_nutrition_card.dart';

class NutritionSection extends ConsumerStatefulWidget {
  final RecipeDetails? recipeDetail;

  const NutritionSection({
    super.key,
    required this.recipeDetail,
  });

  @override
  ConsumerState<NutritionSection> createState() => _NutritionSectionState();
}

class _NutritionSectionState extends ConsumerState<NutritionSection> {
  @override
  Widget build(BuildContext context) {
    final nutritionDataState = ref.watch(nutritionInfoNotifierProvider);

    return switch (nutritionDataState.status) {
      AppStatus.loading => nutritionsShimmer(context),
      AppStatus.success => _buildNutritionContent(context, nutritionDataState),
      AppStatus.error => _buildNutritionContent(context, nutritionDataState), // Handle "No Nutritions Available" case
      AppStatus.otpVerificationSuccess => const SizedBox.shrink(),
      AppStatus.idle => const SizedBox.shrink(),
      AppStatus.loadingMore => const SizedBox.shrink(),
      AppStatus.empty => const SizedBox.shrink(),
      AppStatus.creating => const SizedBox.shrink(),
      AppStatus.createSuccess => const SizedBox.shrink(),
      AppStatus.createError => const SizedBox.shrink(),
      AppStatus.updating => const SizedBox.shrink(),
      AppStatus.updateSuccess => const SizedBox.shrink(),
      AppStatus.updateError => const SizedBox.shrink(),
      AppStatus.deleting => const SizedBox.shrink(),
      AppStatus.deleteSuccess => const SizedBox.shrink(),
      AppStatus.deleteError => const SizedBox.shrink(),
    };
  }

  Widget _buildNutritionContent(BuildContext context, AppState<NutritionInfoData> nutritionDataState) {
    // Check if nutrition data is available
    final hasNutritionData = nutritionDataState.status == AppStatus.success &&
        nutritionDataState.data != null &&
        nutritionDataState.data!.nutrients != null &&
        nutritionDataState.data!.nutrients!.isNotEmpty;

    // If there's an error OR if success but no data (regardless of message), show message in a box
    if (nutritionDataState.status == AppStatus.error ||
        (nutritionDataState.status == AppStatus.success && !hasNutritionData)) {
      return Container(
        padding: EdgeInsets.all(getDeviceType(context).name != 'mobile' ? 16 : 16),
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 255, 255, 255),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color.fromARGB(255, 255, 255, 255)),
          boxShadow: [
            if (getDeviceType(context).name != 'mobile')
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              text: 'Nutrition Analysis',
              size: 14,
              weight: FontWeight.w600,
              color: AppColors.primaryGreyColor,
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              // decoration: BoxDecoration(
              //   color: Colors.red.shade50,
              //   borderRadius: BorderRadius.circular(8),
              //   border: Border.all(color: Colors.red.shade300),
              // ),
              child: Row(
                children: [
                  // Icon(
                  //   Icons.error_outline,
                  //   color: Colors.red.shade700,
                  //   size: 20,
                  // ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: CustomText(
                      text: nutritionDataState.errorMessage?.isNotEmpty == true
                          ? Utils().cleanHtmlText(nutritionDataState.errorMessage!)
                          : 'No Nutritions Available',
                      size: 12,
                      weight: FontWeight.w400,
                      color: Colors.red.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    // If no nutrition data available (success but empty), hide the section
    if (!hasNutritionData) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(getDeviceType(context).name != 'mobile' ? 16 : 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          if (getDeviceType(context).name != 'mobile')
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible: getDeviceType(context).name == 'mobile',
            child: const SizedBox(height: 16),
          ),
          CustomText(
            text: 'Nutrition Analysis',
            size: 14,
            weight: FontWeight.w600,
            color: AppColors.primaryGreyColor,
          ),
          const SizedBox(height: 20),
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 2,
            crossAxisSpacing: 15,
            mainAxisSpacing: 10,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 2,
            children: nutritionDataState.data?.nutrients?.map((fact) {
              return buildNutritionCard(
                context,
                fact.name ?? '',
                fact.amount ?? '',
              );
            }).toList() ??
                [],
          ),
          const SizedBox(height: 20),
          Center(
            child: TextButton(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (_) => RecipeNutritionFactsDialog(
                    recipeDetails: widget.recipeDetail!,
                    nutritionData: nutritionDataState.data,
                  ),
                );
              },
              child: CustomText(
               text:  'View detailed nutrition facts',
                color: AppColors.primaryBorderColor,
                weight: FontWeight.w400,
                decoration: TextDecoration.underline,
                decorationColor: AppColors.primaryBorderColor,
                decorationThickness: 1.0,
                size: 12,
              ),
            ),
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}