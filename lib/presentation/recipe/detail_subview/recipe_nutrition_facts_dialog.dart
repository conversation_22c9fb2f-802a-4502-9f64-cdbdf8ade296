import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/data/models/nutritionInfo_response.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../mealplan/widgets/NutritionRow.dart';

class RecipeNutritionFactsDialog extends StatelessWidget {
  final RecipeDetails recipeDetails;
  final NutritionInfoData? nutritionData;

  const RecipeNutritionFactsDialog(
      {super.key, required this.recipeDetails, required this.nutritionData});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 10,
      backgroundColor: Colors.white,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Container(
          width: 600,
          padding: EdgeInsets.only(
            left: 10,
            right: 10,
            bottom: 10,
          ),
          color: Colors.white,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: _closeButton(context),
                  ),
                ),
                Center(
                  child: CustomText(
                   text: "Nutrition Analysis",
                    size: 24,
                    color: AppColors.primaryGreyColor,
                    weight: FontWeight.w700,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                    right: 10,
                    left: 10,
                    top: 20,
                    bottom: 20,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    border: Border.all(color: Colors.black, width: 4),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Column(
                      children: [
                        Divider(thickness: 1, color: Colors.black),
                        Container(
                          color: AppColors.yellowRowColor,
                          child: Column(
                            children: [
                              Visibility(
                                visible: recipeDetails.servings != null,
                                child: Align(
                                  alignment: Alignment.topLeft,
                                  child: CustomText(
                                    text:
                                    '${recipeDetails.servings} Servings per container',
                                    size: 12,
                                    color: AppColors.blackColor,
                                    weight: FontWeight.w400,
                                  ),
                                ),
                              ),
                              SizedBox(height: 10),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Visibility(
                                    visible: recipeDetails.yieldUnit != null,
                                    child:  CustomText(
                                      text:
                                      'Serving size',
                                      size: 12,
                                      color: AppColors.blackColor,
                                      weight: FontWeight.w700,
                                    ),
                                  ),
                                  Visibility(
                                      visible: recipeDetails.yield != null,
                                      child: CustomText(
                                        text:
                                        '${recipeDetails.yield ?? ''} cup (${recipeDetails.yieldUnit})',
                                        // This could be dynamic if provided in API
                                        size: 12,
                                        color: AppColors.blackColor,
                                        weight: FontWeight.w700,
                                      )),
                                  SizedBox(width: 40),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Divider(thickness: 8, color: Colors.black),
                        SizedBox(height: 5),
                        Container(
                          color: Colors.lightBlueAccent.withValues(alpha: .2),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  CustomText(
                                    text:
                                    'Amount per serving',
                                    size: 14,
                                    color: AppColors.blackColor,
                                    weight: FontWeight.w700,
                                  ),
                                  CustomText(
                                    text:
                                    '${nutritionData?.caloriesPerServing}',
                                    size: 18,
                                    color: AppColors.blackColor,
                                    weight: FontWeight.w700,
                                  ),
                                  SizedBox(width: 40),
                                ],
                              ),
                              Align(
                                alignment: Alignment.topLeft,
                                child:  CustomText(
                                  text:
                                  'Calories',
                                  size: 18,
                                  color: AppColors.blackColor,
                                  weight: FontWeight.w700,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Divider(thickness: 4, color: Colors.black),
                        SizedBox(height: 4),
                        Container(
                          color: AppColors.yellowRowColor,
                          child: Column(
                            children:
                                _buildNutrientRows(nutritionData?.nutrients, 0),
                          ),
                        ),
                        Divider(thickness: 5, color: Colors.black),
                        Container(
                          color: Colors.greenAccent.withValues(alpha: .2),
                          child: Column(
                            children:
                                _buildVitaminRows(nutritionData?.nutrients),
                          ),
                        ),
                        Divider(thickness: 5, color: Colors.black),
                        SizedBox(height: 5),
                        CustomText(
                          text:
                          '*The % Daily Value tells you how much a nutrient in a serving food contributes to a daily diet. 2000 calories a day is used for general nutrition advice.',
                          size: 10,
                          color: context.theme.hintColor,
                          weight: FontWeight.w300,
                        ),
                        SizedBox(height: 10),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildNutrientRows(dynamic nutrients, int indentLevel) {
    List<Widget> rows = [];
    if (nutrients == null || nutrients is! List) return rows;

    for (var nutrient in nutrients) {
      // Ensure nutrient is of correct type
      if (nutrient is! Nutrients && nutrient is! SubNutrients) continue;

      String? name;
      String? amount;
      String? dailyValue;
      dynamic subNutrients;

      if (nutrient is Nutrients) {
        name = nutrient.name;
        amount = nutrient.amount;
        dailyValue = nutrient.dailyValue;
        subNutrients = nutrient.subNutrients;
      } else if (nutrient is SubNutrients) {
        name = nutrient.name;
        amount = nutrient.amount;
        dailyValue = nutrient.dailyValue;
        subNutrients = nutrient.subNutrients;
      }

      // Skip specific nutrients at top level
      if (indentLevel == 0 &&
          (name == 'Vitamin D' ||
              name == 'Calcium' ||
              name == 'Iron' ||
              name == 'Potassium')) {
        continue;
      }

      // Add nutrient row
      rows.add(NutritionRow(
        name: '$name ${amount ?? ''}',
        percent: dailyValue,
        isBold: indentLevel == 0,
        indent: indentLevel > 0,
      ));
      rows.add(Divider(thickness: 1, color: Colors.black));

      // Recursively handle subNutrients if it's a list
      if (subNutrients != null && subNutrients is List) {
        rows.addAll(_buildNutrientRows(subNutrients, indentLevel + 1));
      }
    }
    return rows;
  }

  List<Widget> _buildVitaminRows(List<Nutrients>? nutrients) {
    List<Widget> rows = [];
    if (nutrients == null) return rows;

    for (var nutrient in nutrients) {
      if (nutrient.name == 'Vitamin D' ||
          nutrient.name == 'Calcium' ||
          nutrient.name == 'Iron' ||
          nutrient.name == 'Potassium') {
        rows.add(NutritionRow(
          name: '${nutrient.name} ${nutrient.amount}',
          percent: nutrient.dailyValue,
          isBold: nutrient.name != 'Vitamin D',
        ));
        rows.add(Divider(thickness: 1, color: Colors.black));
      }
    }
    return rows;
  }

  Widget _closeButton(BuildContext context) {
    return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }
}
