import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';

import '../../../app/assets_manager.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/category_response.dart';
import '../../../core/data/models/cuisines_response.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/providers/single_recipe_notifier.dart';
import '../../cookbook/widgets/edit_button.dart';
import 'build_meta_data_card.dart';

Widget buildRecipeMetadata(
    BuildContext context,
    RecipeDetails recipe,
    RecipeDetails recipeDetail,
    List<Categories>? categoriesList,
    List<Cuisines>? cuisinesList,
    int recipeId,
    int cookbookId,
    List<Recipe> recipesList,
    {required WidgetRef ref}) {
  final metadata = [
    {
      'icon': AssetsManager.time,
      'label': 'Prep Time',
      'value': recipe.prepTime ?? ''
    },
    {
      'icon': AssetsManager.time,
      'label': 'Cook Time',
      'value': recipe.cookTime ?? ''
    },
    {
      'icon': AssetsManager.time,
      'label': 'Total Time',
      'value': recipe.totalTime ?? ''
    },
    {
      'icon': AssetsManager.group,
      'label': 'Category',
      'value': recipe.category ?? ''
    },
    {
      'icon': AssetsManager.cuisine,
      'label': 'Cuisine',
      'value': recipe.cuisine ?? ''
    },
    {
      'icon': AssetsManager.yield,
      'label': 'Yield',
      'value': recipe.yieldUnit != null && recipe.yield != null
          ? '${recipe.yield.toString()} ${recipe.yieldUnit.toString()}'
          : ""
    },
    {
      'icon': AssetsManager.serving,
      'label': 'Servings',
      'value': recipe.servings != null ? recipe.servings.toString() : ''
    },
  ];

  return Container(
    padding: EdgeInsets.all(20.sp),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: AppColors.greyBorderColor, width: 1),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 10,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Column(
      children: [
        Align(
            alignment: Alignment.topRight,
            child: EditButton(
              onPressed: () {
                ref
                    .read(singleRecipeNotifierProvider.notifier)
                    .goToEditRecipeScreen(
                        context,
                        recipeId,
                        cookbookId,
                        recipesList,
                        recipeDetail,
                        categoriesList!,
                        cuisinesList!);
              },
            )),
        SizedBox(height: 15.h),
        GridView.count(
          shrinkWrap: true,
          crossAxisCount: 2,
          crossAxisSpacing: 15,
          mainAxisSpacing: 10,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 2,
          children: metadata.map((item) {
            return buildMetadataCard(
              context: context,
              icon: item['icon'] as String,
              label: item['label'] as String,
              value: item['value'] as String,
              iconColor: item['color'] as Color?,
            );
          }).toList(),
        ),
      ],
    ),
  );
}
