import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../app/imports/core_imports.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import '../subview/custom_notes_widget.dart';

Widget buildNotesSection(String notes, {required Null Function() onUpdateData}) {
  return Container(
    padding: const EdgeInsets.all(24),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const CustomeTitleText(title: 'Notes'),
        Sized<PERSON>ox(height: 8.h),
        CustomNotesWidget(
          title: notes,
          isCallFromEdit: true,
          onUpdateData: onUpdateData
        ),
      ],
    ),
  );
}