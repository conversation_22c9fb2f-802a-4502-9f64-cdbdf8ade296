import 'package:mastercookai/core/data/models/category_response.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/core/data/models/cuisines_response.dart';
import 'package:mastercookai/core/providers/add_recipe_meta_data_notifier.dart';

import '../../../app/imports/core_imports.dart';
import '../subview/custom_update_info.dart';

Widget buildRecipeMetadata(List<Categories> categories, List<Cuisines> cuisines,
    String? category, String? cuisine,
    {required Function() onUpdateData}) {
  return Consumer(builder: (context, ref, child) {
    final addRecipeMetadata = ref.watch(addRecipeMetadataProvider);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: UpdateInfoScreen(
          categories,
          cuisines,
          addRecipeMetadata.selectedCategory ?? category,
          addRecipeMetadata.selectedCuisine ?? cuisine,
          callFromUpdate: true,
          onUpdateData: onUpdateData
          //     () {
          //   _saveBasicInfo(context);
          // },
          ),
    );
  });
}
