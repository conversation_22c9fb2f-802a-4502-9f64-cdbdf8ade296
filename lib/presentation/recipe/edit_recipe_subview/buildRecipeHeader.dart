import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/widgets/custom_input_field.dart';

Widget buildRecipeHeader(BuildContext context, WidgetRef ref,
    {required TextEditingController recipeNameController,
    required TextEditingController recipeDescController}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      CustomInputField(
        hintText: "Recipe Name",
        controller: recipeNameController,
        maxLines: 1,
        maxLength: 100,
       // height: 70.h,
        onChanged: (value) {
          ref.read(recipeMetadataProvider.notifier).updateName(value);
        },
      ),
      <PERSON><PERSON><PERSON><PERSON>(height: 16.h),
      CustomInputField(
        hintText: "Recipe Description",
        controller: recipeDescController,
        maxLines: 3,
       // height: 200.h,
        onChanged: (value) {
          ref.read(recipeMetadataProvider.notifier).updateDescription(value);
        },
      ),
    ],
  );
}
