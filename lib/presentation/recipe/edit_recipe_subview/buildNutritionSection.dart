import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../cookbook/widgets/custom_title_text.dart';

Widget buildNutritionSection(
  BuildContext context,
  NutritionInfo nutritionFacts, {
  required VoidCallback onSaveNutrition,
}) {
  return Container(
    padding: EdgeInsets.all(16.sp),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const CustomeTitleText(title: 'Nutrition facts'),
        SizedBox(height: 20.h),
        GridView.count(
          shrinkWrap: true,
          crossAxisCount: 2,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 2,
          children:nutritionFacts.nutrients != null? nutritionFacts.nutrients!.map((fact) {
            return _buildNutritionCard(
                context, fact.name ?? '', fact.amount ?? '');
          }).toList()
              : [
            _buildNutritionCard(context, 'No Data', 'N/A'),
          ],
        ),
        SizedBox(
          height: 16,
        ),
        Center(
          child: MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {

              },
              child: CustomText(
                text: 'View detailed nutrition facts',
                color: AppColors.primaryBorderColor,
                size: 14,
                weight: FontWeight.w400,
                decoration: TextDecoration.underline,
                decorationThickness: 1.0,
                decorationColor: AppColors.primaryBorderColor,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

Widget _buildNutritionCard(BuildContext context, String value, String label) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.grey[100],
      borderRadius: BorderRadius.circular(10),
    ),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CustomText(
          text: label,
          maxLine: 1,
          color: AppColors.backgroudInActiveColor,
          weight: FontWeight.w700,
          size: context.theme.textTheme.displaySmall!.fontSize,
        ),
        SizedBox(height: 4.h),
        Flexible(
          child: CustomText(
            text: value,
            maxLine: 1,
            size: 12,
            weight: FontWeight.w400,
            overflow: TextOverflow.ellipsis,
            color: AppColors.primaryLightTextColor,
          ),
        ),
      ],
    ),
  );
}
