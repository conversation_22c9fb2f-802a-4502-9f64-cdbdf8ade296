import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../app/imports/core_imports.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import '../subview/add_ingredent.dart';

Widget buildIngredientsSection(BuildContext context,{  required VoidCallback onSaveIngredient,}) {
  return Container(
    padding: EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header Row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomeTitleText(
              title: 'Ingredients',
            ),
            // AddIngredientScreen()
          ],
        ),

        SizedBox(height: 8.h),

        // Ingredient list
        AddIngredientScreen(
            isCallFromEdit: true,
            onUpdateData: onSaveIngredient
          // ) {
          //     _saveIngrdient(context);
          //   },
        )
      ],
    ),
  );
}