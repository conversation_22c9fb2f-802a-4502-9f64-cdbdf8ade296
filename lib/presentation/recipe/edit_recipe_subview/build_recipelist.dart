import 'package:mastercookai/core/data/models/recipe_response.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_searchbar.dart';
import '../subview/recipe_cards.dart';

Widget buildRecipeList(
    {required BuildContext context,
    required List<Recipe> recipes,
    required int selectedCookbookIndex,
    required TextEditingController searchController,
    required Function(int, Recipe) onRecipeSelected}) {

  return Column(
    children: [
      SizedBox(height: 20),
      CustomSearchBar(
        controller: searchController,
        width: DeviceUtils().isTabletOrIpad(context)
            ? 368
            : MediaQuery.of(context).size.width,
      ),
      Expanded(
        child: recipes.isEmpty
            ? Center(
                child: Text(
                  'No recipes available',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 16,
                  ),
                ),
              )
            : ListView.separated(
                padding: const EdgeInsets.all(12),
                itemCount: recipes.length,
                separatorBuilder: (_, __) => const SizedBox(height: 12),
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      onRecipeSelected(index, recipes[index]);
                    },
                    child: RecipeCardItem(
                      key: ValueKey(recipes[index].id),
                      recipes: recipes[index],
                      isSelected: selectedCookbookIndex == index,
                      isEditable: false,
                      onMenuItemSelected: (String value) {},
                    ),
                  );
                },
              ),
      ),
    ],
  );
}
