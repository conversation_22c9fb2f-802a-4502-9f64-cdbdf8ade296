import 'package:flutter/cupertino.dart';
import 'package:mastercookai/core/data/models/category_response.dart';
import 'package:mastercookai/core/data/models/cuisines_response.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';
import 'package:mastercookai/core/providers/add_recipe_meta_data_notifier.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../app/theme/colors.dart';
import '../../../core/providers/recipe/media_provider.dart';
import '../../../core/providers/recipe_notifier.dart';
import '../../../core/providers/single_recipe_notifier.dart';
import '../../../core/providers/tab_index_notifier.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_input_field.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import '../../cookbook/widgets/edit_button.dart';
import '../edit_recipe_subview/buildAuthorSection.dart';
import '../edit_recipe_subview/buildMainContent.dart';
import '../edit_recipe_subview/buildRecipeHeader.dart';
import '../subview/add_ingredent.dart';
import '../subview/build_add_recipe_metadata.dart';
import '../subview/custom_author_widget.dart';
import '../subview/custom_directions_widegts.dart';
 import '../subview/custom_notes_widget.dart';
import '../subview/custom_serving_ideas_widget.dart';
import '../subview/custom_wine_widgets.dart';
import '../subview/media_picker_grid.dart';
import 'custom_Tabbar.dart';

class TabAddRecipe extends ConsumerStatefulWidget {
  final TextEditingController recipeNameController;
  final TextEditingController recipeDescController;
  final bool? callFromClipper;

  TabAddRecipe(
      {required this.recipeNameController,
      required this.recipeDescController,
      required this.callFromClipper});

  @override
  ConsumerState<TabAddRecipe> createState() => _TabRecipeDetailState();
}

class _TabRecipeDetailState extends ConsumerState<TabAddRecipe> {
  String? selectedMediaUrl;
  dynamic selectedMediaFile; // Adjust type if known

  @override
  void initState() {
    super.initState();
    Future.microtask(() => ref.read(addRecipeMetadataProvider.notifier).reset());
  }

  @override
  Widget build(BuildContext context) {
    final selectedTabIndex = ref.watch(tabIndexNotifierProvider);

    return Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.secondaryColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: SingleChildScrollView(
            child: Column(
          children: [
            SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomInputField(
                    hintText: 'Recipe Name',
                    controller: widget.recipeNameController,
                    maxLines: 1,
                   // height: 70.h,
                    maxLength: 100,
                  ),
                  SizedBox(height: 16.h),
                  CustomInputField(
                    hintText: 'Recipe Description',
                    controller: widget.recipeDescController,
                    maxLines: 3,
                   // height: 200.h,
                  ),
                ],
              ),
            ),
            SizedBox(height: 20),
            CustomTabBar(
              selectedTabIndex: selectedTabIndex,
              isAddRecipe: true,
              onTabSelected: (index) {
                setState(() {
                  ref.read(tabIndexNotifierProvider.notifier).selectTab(index);

                  // selectedTabIndex = index;
                });
              },
            ),
            SizedBox(height: 20.h),
            _buildTabContent(),
          ],
        )));
  }

  Widget _buildTabContent() {
    switch (ref.read(tabIndexNotifierProvider)) {
      case 0:
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[200]!),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: MediaPickerGrid(
                    recipeThumbnailFileUrl: '',
                    recipeMedia: ref.watch(mediaFilesProvider).mediaFiles,
                  ),
                ),
              ),
              SizedBox(width: 30.w),
              Expanded(
                flex: 2,
                child: buildAddRecipeMetadata(ref: ref),
              ),
            ],
          ),
        );

      case 1:
         return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const CustomeTitleText(title: 'Ingredients'),
              SizedBox(height: 8.h),
              AddIngredientScreen(),
            ],
          ),
        );
      case 2:
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: CustomDirectionsWidgets(
              isCallFromEdit: false, callFromClipper: widget.callFromClipper),
        );

      case 3:
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 8),
              CustomeTitleText(title: 'Notes'),
              SizedBox(height: 8),
              CustomNotesWidget(),
            ],
          ),
        );
      case 4:
        return Container(
          // width: 600.w,
          margin: EdgeInsets.all(40),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              const CustomeTitleText(title: 'Author info'),
              const SizedBox(height: 20),
              CustomAuthorWidget(),
            ],
          ),
        );
      case 5:
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              CustomServingWidget(),
              SizedBox(height: 20.h),
              CustomWineWidget(),
            ],
          ),
        );

      default:
        return SizedBox.shrink();
    }
  }
}
