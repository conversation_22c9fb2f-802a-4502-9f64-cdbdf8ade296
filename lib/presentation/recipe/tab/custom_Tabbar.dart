import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/theme/colors.dart';

import '../../../app/imports/core_imports.dart';

class CustomTabBar extends StatefulWidget {
  final int selectedTabIndex;
  final ValueChanged<int> onTabSelected;
  final bool isScrollable;
  final double topMargin;
  final bool showHighlight;
  final bool isAddRecipe;

  const CustomTabBar({
    Key? key,
    required this.selectedTabIndex,
    required this.onTabSelected,
    this.isScrollable = true,
    this.topMargin = 16.0,
    this.showHighlight = true,
    this.isAddRecipe = false,
  }) : super(key: key);

  @override
  _CustomTabBarState createState() => _CustomTabBarState();
}

class _CustomTabBarState extends State<CustomTabBar> {
  @override
  Widget build(BuildContext context) {
    List<BottomNavigationBarItem> tabItems = [
      BottomNavigationBarItem(
        label: 'Ingridients',
        icon: _svgIcon(AssetsManager.ic_ingridient,AppColors.blackColor),
        activeIcon: _svgIcon(AssetsManager.ic_ingridient,AppColors.primaryColor),
      ),
      BottomNavigationBarItem(
        label: 'Directions',
        icon: _svgIcon(AssetsManager.ic_directions,AppColors.blackColor),
        activeIcon: _svgIcon(AssetsManager.ic_ingridient,AppColors.primaryColor),
      ),
      BottomNavigationBarItem(
        label: 'Notes',
        icon: _svgIcon(AssetsManager.ic_notes,AppColors.blackColor),
        activeIcon: _svgIcon(AssetsManager.ic_notes,AppColors.primaryColor),
      ),
      BottomNavigationBarItem(
        label: 'Author Info',
        icon: _svgIcon(AssetsManager.ic_author,AppColors.blackColor),
        activeIcon: _svgIcon(AssetsManager.ic_author,AppColors.primaryColor),
      ),
      BottomNavigationBarItem(
        label: 'Other',
        icon: _svgIcon(AssetsManager.ic_others,AppColors.blackColor),
        activeIcon: _svgIcon(AssetsManager.ic_others,AppColors.primaryColor),
      ),
    ];

    if (widget.isAddRecipe) {
      tabItems.insert(
        0,
        BottomNavigationBarItem(
          label: 'Recipe Data',
          icon: _svgIcon(AssetsManager.cuisine,AppColors.blackColor),
          activeIcon: _svgIcon(AssetsManager.cuisine,AppColors.primaryColor),
        ),
      );
    }

    return CupertinoTabBar(
      currentIndex: widget.selectedTabIndex,
      onTap: widget.onTabSelected,
      backgroundColor: AppColors.secondaryColor.withOpacity(0.9),
      activeColor: AppColors.primaryColor,
      inactiveColor: Colors.grey,
      border: Border(
        top: BorderSide(
          color: Colors.grey.withValues(alpha: 0.2),
          width: 1.0,
        ),
      ),
      items: tabItems,
    );
  }

  Widget _svgIcon(String assetPath,Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: SvgPicture.asset(
        assetPath,
        width: 28,
        height: 28,
        color: color,
      ),
    );
  }
}
