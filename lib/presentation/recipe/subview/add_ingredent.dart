import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import '../../../core/providers/recipe/ingrident_provider.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_input_field.dart';

class AddIngredientScreen extends ConsumerWidget {
  final bool isCallFromEdit;
  final bool isCallFromMobile;
  final VoidCallback? onUpdateData;

  AddIngredientScreen({
    super.key,
    this.isCallFromEdit = false,
    this.isCallFromMobile = false,
    this.onUpdateData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the provider state to trigger rebuilds when controllers are added/removed
    ref.watch(ingredientsProvider);
    final ingredientsNotifier = ref.watch(ingredientsProvider.notifier);
    final controllers = ingredientsNotifier.controllers;

    debugPrint("AddIngredientScreen: Controllers count: ${controllers.length}");
    debugPrint("AddIngredientScreen: Ingredients state: ${ref.watch(ingredientsProvider)}");

    // Check and reset controllers after the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controllers.isEmpty) {
        ingredientsNotifier.resetWithDefaults();
      }
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: controllers.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Row(
                children: [
                  Expanded(
                    child: CustomInputField(
                      controller: controllers[index],
                      hintText: 'Add ingredient',
                      //autoFocus: true,
                     // height: 70.h,
                       verticalPadding: 12,
                      onChanged: (value) {
                        // Update provider on text change
                        ingredientsNotifier.setIngredients();
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        SizedBox(height: 10.h),
        Visibility(
          visible: !isCallFromMobile,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                flex: 1,
                child: Padding(
                  padding:   EdgeInsets.symmetric(horizontal:isCallFromEdit || getDeviceType(context).name!=DeviceType.tablet.name?0: MediaQuery.of(context).size.width*0.3),
                  child: CustomButton(
                    text: "Add row",
                    fontSize: 14,
                    onPressed: () {
                      ingredientsNotifier.addController();
                    },
                  ),
                ),
              ),
              Visibility(
                  visible: isCallFromEdit,
                  child: SizedBox(width: 10.w)),
              Visibility(
                visible: isCallFromEdit,
                child: Expanded(
                  flex: 1,
                  child: CustomButton(
                    text: "Save Changes",
                    fontSize: 14,
                    onPressed: () {
                      // Ensure ingredients are updated before notifying parent
                      ingredientsNotifier.setIngredients();
                      onUpdateData?.call();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}