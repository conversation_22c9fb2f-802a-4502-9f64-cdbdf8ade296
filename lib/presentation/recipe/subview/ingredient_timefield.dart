import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:numberpicker/numberpicker.dart';

import '../../../core/widgets/custom_text.dart';
import '../../../core/widgets/non_editable_textfield.dart';

class TimePickerField extends StatefulWidget {
  final String label;
  final TextEditingController controller;
  final void Function(int hours, int minutes)? onTimeSelected;

  const TimePickerField({
    super.key,
    required this.label,
    required this.controller,
    this.onTimeSelected,
  });

  @override
  State<TimePickerField> createState() => _TimePickerFieldState();
}

class _TimePickerFieldState extends State<TimePickerField> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  int selectedHour = 0;
  int selectedMinute = 0;

  void _showPopup() {
    _removePopup(); // Ensure only one shown

    final overlay = Overlay.of(context);

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Detect taps outside the popup
          Positioned.fill(
            child: GestureDetector(
              onTap: _removePopup,
              behavior: HitTestBehavior.translucent,
              child: Container(), // Transparent layer
            ),
          ),

          // Your popup
          Positioned(
            child: CompositedTransformFollower(
              link: _layerLink,
              offset: const Offset(0, 60),
              showWhenUnlinked: false,
              child: Material(
                elevation: 2,
                borderRadius: BorderRadius.circular(16),
                color: Colors.white,
                child: Container(
                  constraints: const BoxConstraints(
                    minWidth: 320,
                    maxWidth: 320, // Set a max if you want to avoid over-expanding
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [


                      CustomText(
                        text: "Select ${widget.label}",
                        color: Colors.black87,
                        size:   12,
                        weight: FontWeight.w600,
                        fontFamily: 'Inter',

                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          StatefulBuilder(
                            builder: (context, setStateHr) {
                              return NumberPicker(
                                value: selectedHour,
                                minValue: 0,
                                maxValue: 23,
                                itemHeight: 30,
                                textStyle: const TextStyle(
                                    fontSize: 14, color: Colors.grey),
                                selectedTextStyle: const TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.bold),
                                onChanged: (value) =>
                                    setStateHr(() => selectedHour = value),
                              );
                            },
                          ),
                          const SizedBox(width: 4),
                          const Text("hr",
                              style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black87)),
                          const SizedBox(width: 8),
                          StatefulBuilder(
                            builder: (context, setStateMin) {
                              return NumberPicker(
                                value: selectedMinute,
                                minValue: 0,
                                maxValue: 59,
                                step: 5,
                                itemHeight: 30,
                                textStyle: const TextStyle(
                                    fontSize: 14, color: Colors.grey),
                                selectedTextStyle: const TextStyle(
                                    fontSize: 20, fontWeight: FontWeight.bold),
                                onChanged: (value) =>
                                    setStateMin(() => selectedMinute = value),
                              );
                            },
                          ),
                          const SizedBox(width: 4),
                          const Text("min",
                              style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black87)),
                        ],
                      ),
                      const SizedBox(height: 15),
                      GestureDetector(
                        onTap: () {
                          widget.controller.text =
                              "${selectedHour}h ${selectedMinute}m";
                          widget.onTimeSelected
                              ?.call(selectedHour, selectedMinute);
                          _removePopup();
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 15, vertical: 8),
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Text(
                            "Done",
                            style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w400,
                                fontSize: 12),
                          ),
                        ),
                      ),
                      const SizedBox(height: 6),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    overlay.insert(_overlayEntry!);
  }

  void _removePopup() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _removePopup();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: DeviceUtils().isTabletOrIpad(context) ? 70 : 170.w,
          child: CustomText(text:widget.label,
            color: context.theme.hintColor,
            size: 12,
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: SizedBox(
            height: 60.h, // Match text field height
            child: CompositedTransformTarget(
              link: _layerLink,
              child: GestureDetector(
                onTap: _showPopup,
                behavior: HitTestBehavior.translucent,
                child: AbsorbPointer(
                  child:CustomInputField(
                  //NonEditableTextField(
                    hintText: "Select",
                    controller: widget.controller,
                    editable: false,

                    // height: 60.h,
                    // hideCross: true,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
