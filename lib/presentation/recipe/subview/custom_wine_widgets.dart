import '../../../../../app/imports/packages_imports.dart';
import '../../../app/theme/colors.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_input_field.dart';
import '../../../core/widgets/custom_text.dart';
import '../../cookbook/widgets/custom_title_text.dart';

class CustomWineWidget extends ConsumerStatefulWidget {
  String? wine;
  final bool? isCallFromAdd;

  CustomWineWidget({super.key, this.wine, this.isCallFromAdd = false});

  @override
  ConsumerState<CustomWineWidget> createState() => _CustomWineWidgetState();
}

class _CustomWineWidgetState extends ConsumerState<CustomWineWidget> {
  late TextEditingController wineController;

  @override
  void initState() {
    super.initState();
    final metadata = ref.read(recipeMetadataProvider);
    // Initialize noteController with value from notesProvider or widget.notes

    wineController = TextEditingController(
      text:
          widget.isCallFromAdd ?? false ? metadata.wineDesc : widget.wine ?? '',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          getDeviceType(context).name == 'mobile'
              ? CustomText(
                  text: 'Wine',
                  size: 14,
                  weight: FontWeight.w500,
                  color: AppColors.primaryGreyColor)
              : CustomeTitleText(title: 'Wine'),
          SizedBox(height: 20.h),
          CustomInputField(
            onChanged: (val) {
              ref.read(recipeMetadataProvider.notifier).updateWineDesc(val);
            },
            hintText: "Description",
            controller: wineController,
            maxLines: 5,
            isMoreLines: true,
            keyboardType: TextInputType.multiline,
          ),

          // CustomButton(text: "Save changes", onPressed: (){} , width: 210.w, fontSize: 18.sp,),
        ],
      ),
    );
  }
}
