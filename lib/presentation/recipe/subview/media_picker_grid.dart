import 'dart:io';
import 'package:card_banner/card_banner.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';
import 'package:mastercookai/core/providers/recipe/media_provider.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/video_player_widget.dart';
import '../../../core/widgets/image_cropper/image_cropper.dart';
import 'circle_icon.dart';
import 'custom_sub_media_widget.dart';

class MediaPickerGrid extends ConsumerStatefulWidget {
  final List<RecipeMedia>? recipeMedia;
  bool? isCallFromEdit;
  String? recipeThumbnailFileUrl;
  VoidCallback? onUpdateData;
  final int? coverMediaIndex; // Add coverMediaIndex parameter

  MediaPickerGrid({
    super.key,
    this.recipeMedia,
    required this.recipeThumbnailFileUrl,
    this.isCallFromEdit = false,
    this.onUpdateData,
    this.coverMediaIndex, // Add to constructor
  });

  @override
  ConsumerState<MediaPickerGrid> createState() => _MediaPickerGridState();
}

class _MediaPickerGridState extends ConsumerState<MediaPickerGrid> {
  List<RecipeMedia?> mediaFiles = List.generate(9, (_) => null); // Initialize with 9 null items

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final providerState = ref.read(mediaFilesProvider);
      if (providerState.mediaFiles.isNotEmpty) {
        addFromLocal();
      } else if (widget.recipeMedia != null && widget.recipeMedia!.isNotEmpty) {
        addFromRecipeMedia();
      }
    });
  }
  void addFromRecipeMedia() {
  if (widget.recipeMedia != null && widget.recipeMedia!.isNotEmpty) {
    setState(() {
      mediaFiles = List.generate(9, (_) => null); // Reset

      // Separate videos and images
      final videos = widget.recipeMedia!.where((m) => m.mediaType == 'VIDEO').toList();
      final images = widget.recipeMedia!.where((m) => m.mediaType == 'IMAGE').toList();

      // Index 0 reserved for video
      if (videos.isNotEmpty) {
        mediaFiles[0] = videos.first;
      }

      // Images always start from index 1
      for (int i = 0; i < images.length && (i + 1) < 9; i++) {
        mediaFiles[i + 1] = images[i];
      }

      // Handle cover index (must be image, not video)
      int newCoverIndex = 1; // Default cover index
      if (widget.coverMediaIndex != null && widget.coverMediaIndex! >= 0) {
        RecipeMedia originalCover = widget.recipeMedia![widget.coverMediaIndex!];
        if (originalCover.mediaType == 'IMAGE') {
          int imgIndex = images.indexOf(originalCover);
          if (imgIndex >= 0) newCoverIndex = imgIndex + 1;
        }
      }

      // Fallback: first image if available
      if (mediaFiles[newCoverIndex] == null) {
        final fallback = mediaFiles.indexWhere((m) => m != null && m.mediaType == 'IMAGE');
        if (fallback != -1) newCoverIndex = fallback;
      }

      ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles, newCoverIndex);
    });
  }
}

  void addFromLocal() {
  final providerState = ref.read(mediaFilesProvider);
  if (providerState.mediaFiles.isNotEmpty) {
    setState(() {
      mediaFiles = List.generate(9, (_) => null);

      // Separate video and images from local files
      final videos = providerState.mediaFiles.where((m) => m.mediaType == 'VIDEO').toList();
      final images = providerState.mediaFiles.where((m) => m.mediaType == 'IMAGE').toList();

      // Reserve index 0 for video if exists
      if (videos.isNotEmpty) {
        mediaFiles[0] = videos.first;
      }

      // Fill images starting from index 1
      for (int i = 0; i < images.length && (i + 1) < 9; i++) {
        mediaFiles[i + 1] = images[i];
      }

      // Set cover index to first image if exists
      final coverIndex = mediaFiles.indexWhere((m) => m != null && m.mediaType == 'IMAGE');
      ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles, coverIndex != -1 ? coverIndex : 1);
    });
  }
}


  @override
  Widget build(BuildContext context) {
    final providerState = ref.watch(mediaFilesProvider);
    debugPrint("Current coverIndex from provider: ${providerState.coverIndex}");

    return Column(
      children: [
        GridView.builder(
          shrinkWrap: true,
          itemCount: mediaFiles.length,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 12,
            crossAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemBuilder: (context, index) {
            debugPrint("Media at index $index: ${mediaFiles[index]?.toJson() ?? 'null'}");
            if (mediaFiles[index] != null) {
              return _buildMediaItem(index);
            } else {
              return _buildMediaSubImagesItem(index);
            }
          },
        ),
        SizedBox(height: 20.h),
        Visibility(
          visible: widget.isCallFromEdit ?? false,
          child: SizedBox(height: 20.h),
        ),
        Visibility(
          visible: widget.isCallFromEdit ?? false,
          child: CustomButton(
            text: "Save Changes",
            fontSize: 16,
            width: 240,
            onPressed: () {
              widget.onUpdateData?.call();
            },
          ),
        ),
      ],
    );
  }

  void _removeMedia(int index) {
    setState(() {
      mediaFiles[index] = null;
      // Update coverIndex if the removed media was the cover
      if (ref.read(mediaFilesProvider).coverIndex == index) {
        final newCoverIndex = mediaFiles
            .asMap()
            .entries
            .firstWhere(
              (entry) => entry.key != 0 && entry.value != null,
          orElse: () => MapEntry(1, null),
        )
            .key;
        ref.read(mediaFilesProvider.notifier).setCoverIndex(newCoverIndex);
      }
    });
    ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles, ref.read(mediaFilesProvider).coverIndex);
  }

  void _setAsCover(int index) {
    if (index != 0) {
      // Prevent setting cover to index 0 (video)
      setState(() {
        ref.read(mediaFilesProvider.notifier).setCoverIndex(index);
      });
    }
  }

  Widget _buildMediaItem(int index) {
    final media = mediaFiles[index];
    final providerState = ref.watch(mediaFilesProvider);
    final isCover = index == providerState.coverIndex && providerState.coverIndex != -1;

    // For index 0: show video if present, else show add video view
    if (index == 0) {
      if (media != null && media.mediaType == 'VIDEO') {
        return Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: isCover ? Colors.red : Colors.transparent,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: VideoPlayerWidget(
                  mediaFile: media.mediaFile,
                  mediaUrl: widget.isCallFromEdit == true && media.mediaFile != null ? null : media.mediaUrl,
                  thumbnailPath: widget.recipeThumbnailFileUrl,
                ),
              ),
            ),
            Visibility(
              visible: (media.mediaUrl?.isNotEmpty ?? false) || (media.mediaFile?.path.isNotEmpty ?? false),
              child: Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    circleIcon(AssetsManager.edit_white, () => _removeMedia(index)),
                    circleIcon(AssetsManager.dlt_white, () => addImage(index)),
                  ],
                ),
              ),
            ),
          ],
        );
      } else {
        return _buildMediaSubImagesItem(0);
      }
    }

    // For other indices: show image if present, else show add image view
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isCover ? Colors.red : Colors.transparent,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
              image: media!.mediaFile != null
                  ? FileImage(media.mediaFile!)
                  : NetworkImage(media.mediaUrl!) as ImageProvider,
              fit: BoxFit.cover,
            ),
          ),
        ),
        if (isCover)
          Positioned(
            top: -10,
            left: 2,
            child: CardBanner(
              text: 'Cover',
              color: isCover ? Colors.red : Colors.transparent,
              edgeColor: isCover ? Colors.red : Colors.transparent,
              child: const Text(" "),
            ),
          ),
        Visibility(
          visible: index > 0,
          child: Positioned(
            bottom: 3,
            left: 3,
            right: 3,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                circleIcon(AssetsManager.edit_white, () => _removeMedia(index)),
                circleIcon(AssetsManager.dlt_white, () => addImage(index)),
                Visibility(
                  visible: !isCover,
                  child: circleIcon(AssetsManager.swipe, () => _setAsCover(index)),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMediaSubImagesItem(int index) {
    return GestureDetector(
      onTap: () async {
        debugPrint('Opening picker for index $index');
        final files = await MediaPickerService.pickImages(
          allowMultiple: false,
          videoOnly: index == 0,
          allowVideo: index != 0,
        );

        if (files.isNotEmpty) {
          final file = files.first;
          final isVideo = Utils().isVideo(file.path);

          if (isVideo) {
            // Videos can only be added at index 0
            if (index != 0) return; // Ignore if clicked on non-zero index
            setState(() {
              mediaFiles[0] = RecipeMedia(mediaFile: file, mediaType: 'VIDEO');
            });
            ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles, ref.read(mediaFilesProvider).coverIndex);
          } else {
            // Find the first available index from 1 to 8 for images
            final targetIndex = mediaFiles
                .asMap()
                .entries
                .firstWhere(
                  (entry) => entry.key >= 1 && entry.key <= 8 && entry.value == null,
              orElse: () => MapEntry(-1, null),
            )
                .key;
            if (targetIndex == -1 || !mounted) return; // No available slot or widget unmounted

            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => ImageCropper(
                  pickedImage: file,
                  showCropPresets: true,
                  showGridLines: false,
                  useDelegate: true,
                  enableFreeformCrop: true,
                  onImageCropped: (File? croppedImageFile) {
                    if (croppedImageFile != null) {
                      setState(() {
                        mediaFiles[targetIndex] = RecipeMedia(
                          mediaFile: croppedImageFile,
                          mediaType: 'IMAGE',
                        );
                        // Set coverIndex to the first image if none is set
                        if (targetIndex == 1 && ref.read(mediaFilesProvider).coverIndex == 1 && mediaFiles[1] == null) {
                          ref.read(mediaFilesProvider.notifier).setCoverIndex(1);
                        }
                      });
                      ref.read(mediaFilesProvider.notifier).updateMedia(
                          mediaFiles, ref.read(mediaFilesProvider).coverIndex);
                    } else {
                      debugPrint("Cropped image is null!");
                    }
                  },
                ),
              ),
            );
          }
        }
      },
      child: CustomPaint(
        painter: DottedBorderPainter(),
        child: Container(
          height: 120.h,
          alignment: Alignment.center,
          child: Container(
            height: 42,
            margin: EdgeInsets.symmetric(horizontal: 8.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey.shade300),
              color: Colors.white,
            ),
            child: Padding(
              padding: const EdgeInsets.all(1.0),
              child: TextButton(
                onPressed: () async {
                  await addImage(index);
                },
                child: CustomText(
                  text: index == 0 ? "Add Video" : "Add Image",
                  color: AppColors.primaryLightTextColor,
                  size: 13,
                  weight: FontWeight.w400,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> addImage(int index) async {
    final files = await MediaPickerService.pickImages(
      allowMultiple: false,
      videoOnly: index == 0,
      allowVideo: false,
    );

    if (files.isNotEmpty) {
      final file = files.first;
      final fileSize = await file.length();


      if (index == 0) {
        final fileSize = await file.length();
        const maxSize = 150 * 1024 * 1024; // 150 MB
        if (fileSize > maxSize) {
          if (mounted) {
            Utils().showFlushbar(context, message: 'Video file size should not exceed 150MB', isError: true);
          }
          return;
        }
        setState(() {
          mediaFiles[0] = RecipeMedia(mediaFile: file, mediaType: 'VIDEO');
        });
        ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles, ref.read(mediaFilesProvider).coverIndex);
      } else {
        // Find the first available index from 1 to 8 for images
        final targetIndex = mediaFiles
            .asMap()
            .entries
            .firstWhere(
              (entry) => entry.key >= 1 && entry.key <= 8 && entry.value == null,
          orElse: () => MapEntry(-1, null),
        )
            .key;
        if (targetIndex == -1 || !mounted) return; // No available slot or widget unmounted

        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ImageCropper(
              pickedImage: file,
              showCropPresets: true,
              showGridLines: false,
              useDelegate: true,
              enableFreeformCrop: true,
              onImageCropped: (File? croppedImageFile) {
                if (croppedImageFile != null) {
                  setState(() {
                    mediaFiles[targetIndex] = RecipeMedia(
                      mediaFile: croppedImageFile,
                      mediaType: 'IMAGE',
                    );
                    // Set coverIndex to the first image if none is set
                    if (targetIndex == 1 && ref.read(mediaFilesProvider).coverIndex == 1 && mediaFiles[1] == null) {
                      ref.read(mediaFilesProvider.notifier).setCoverIndex(1);
                    }
                  });
                  ref.read(mediaFilesProvider.notifier).updateMedia(
                      mediaFiles, ref.read(mediaFilesProvider).coverIndex);
                } else {
                  debugPrint("Cropped image is null!");
                }
              },
            ),
          ),
        );
      }
    }
  }
}