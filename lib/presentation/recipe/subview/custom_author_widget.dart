import 'dart:io';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/core/utils/Utils.dart';

import '../../../../../app/imports/packages_imports.dart';
import '../../../../core/helpers/media_picker_service.dart';
import '../../../core/providers/recipe/author_provider.dart';
import '../../../core/widgets/circular_image.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_input_field.dart';
import '../../../core/widgets/image_cropper/image_cropper.dart';

class CustomAuthorWidget extends ConsumerStatefulWidget {
  String? author;
  String? authorMediaUrl;
  String? copyright;
  String? source;
  bool? isCallFromEdit;
  VoidCallback? onUpdateData;

  CustomAuthorWidget({
    super.key,
    this.author,
    this.authorMediaUrl,
    this.copyright,
    this.source,
    this.isCallFromEdit = false,
    this.onUpdateData,
  });

  @override
  ConsumerState<CustomAuthorWidget> createState() => _CustomAuthorWidgetState();
}

class _CustomAuthorWidgetState extends ConsumerState<CustomAuthorWidget> {
  late TextEditingController sourceController;
  late TextEditingController copyrightController;
  late TextEditingController authorNameController;

  File? authorImage;

  @override
  void initState() {
    super.initState();
    // Initialize noteController with value from notesProvider or widget.notes
    authorNameController = TextEditingController(
      text: widget.author ?? '',
    );
    sourceController = TextEditingController(
      text: widget.source ?? '',
    );
    copyrightController = TextEditingController(
      text: widget.copyright ?? '',
    );
  }

  void _pickAuthorImage() async {
    final file = await MediaPickerService.pickSingleImage();
    if (file != null && mounted) {
      try {
        // Use delegate pattern to avoid navigation issues
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ImageCropper(
              pickedImage: file,
              showCropPresets: true,
              showGridLines: false,
              enableFreeformCrop: true,
              useDelegate: true,
              // Use delegate pattern
              onImageCropped: (File? croppedImageFile) async {
                if (croppedImageFile != null && mounted) {
                  try {
                    // Update both local state and provider
                    if (mounted) {
                      setState(() {
                        authorImage = croppedImageFile;
                      });

                      // Update provider
                      ref
                          .read(authorProvider.notifier)
                          .updateImage(croppedImageFile);
                    }
                  } catch (e) {
                    print("Error processing cropped image: $e");
                  }
                } else {
                  print("Cropped image is null or user canceled.");
                }
              },
            ),
          ),
        );
      } catch (e) {
        print("Error in image cropping navigation: $e");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authorState = ref.watch(authorProvider);

    // Reset local authorImage when provider state is reset
    if (authorState.image == null && authorImage != null) {
      authorImage = null;
    }

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              CircularImage(
                imageFile: authorImage,
                // Get cropped image from authorProvider
                imageUrl: widget.authorMediaUrl,
                // Fallback to network URL if provided
                placeholderAsset: AssetsManager.profile_placeholder,
                // Replace with your asset path
                radius: 150.0,
                // Matches your original radius
                onTap: () => _pickAuthorImage(),
                borderColor: Colors.grey.shade300,
                placeholderText: 'Add Image',
              ),
              // if (authorImage != null || widget.authorMediaUrl != null)
              //   Positioned(
              //     bottom: 20,
              //     right: 20,
              //     child: GestureDetector(
              //       onTap: () => _pickAuthorImage(),
              //       child: Container(
              //         width: 30,
              //         height: 30,
              //         decoration: BoxDecoration(
              //           color: AppColors.primaryColor,
              //           shape: BoxShape.circle,
              //           boxShadow: [
              //             BoxShadow(
              //               color: Colors.black.withValues(alpha: 0.1),
              //               blurRadius: 4,
              //               offset: Offset(0, 2),
              //             ),
              //           ],
              //         ),
              //         child: Icon(
              //           Icons.edit,
              //           color: Colors.white,
              //           size: 15,
              //         ),
              //       ),
              //     ),
              //   ),
            ],
          ),
          SizedBox(
            height: 10,
          ),

          CustomInputField(
            hintText: "Author",
            controller: authorNameController,
         //   height: 70.h,
             verticalPadding: 12,
            onChanged: (value) =>
                ref.read(authorProvider.notifier).updateAuthorName(value),
          ),
          SizedBox(height: 10),
          CustomInputField(
            hintText: "Source",
            controller: sourceController,
           // height: 70.h,
             verticalPadding: 12,
            onChanged: (value) =>
                ref.read(authorProvider.notifier).updateSource(value),
          ),
          SizedBox(height: 10),
          CustomInputField(
            hintText: "Copyright",
            controller: copyrightController,
          //
             verticalPadding: 12,
            onChanged: (value) =>
                ref.read(authorProvider.notifier).updateCopyright(value),
          ),

          SizedBox(height: 12),
          // CustomButton(text: "Save changes", onPressed: (){} , width: 230.w, fontSize: 20.sp,),
          Visibility(
            visible: widget.isCallFromEdit ?? false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 50.0),
              child: CustomButton(
                text: "Save Changes",
                fontSize: 14,
                onPressed: () {
                  // Ensure ingredients are updated before notifying parent
                  widget.onUpdateData?.call();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
