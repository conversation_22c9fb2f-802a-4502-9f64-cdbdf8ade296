import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
 import 'package:mastercookai/core/widgets/custom_text_medium.dart';
import 'package:mastercookai/core/widgets/custom_text_title.dart';
 import '../../../../../core/widgets/custom_doted_lines.dart';
import '../../../../core/data/models/recipe_response.dart';
import '../../../../core/helpers/date_formatter.dart';
import '../../../../core/utils/device_utils.dart';
import '../../../../core/widgets/custom_network_image.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/providers/recipe_notifier.dart';
import '../../../core/widgets/custom_hover_menu.dart';
import '../../../core/widgets/custom_loading.dart';

class RecipeCardItem extends ConsumerWidget {
  final Recipe recipes;
  final bool isSelected;
  final bool isEditable;
  final void Function(String) onMenuItemSelected; // Callback for menu item selection


  const RecipeCardItem({
    super.key,
    required this.recipes,
    this.isSelected = false,
    this.isEditable = false,
    required this.onMenuItemSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    return Card(
      color: context.theme.cardColor,
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? const BorderSide(color: AppColors.selectionColor, width: 3)
            : BorderSide(color: AppColors.greyBorderColor, width: 1),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // Ensures vertical centering
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Vertically Center the Image
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // ClipRRect(
                //   borderRadius: BorderRadius.circular(8),
                //   child: (recipes.mediaUrl == null || recipes.mediaUrl!.isEmpty)
                //       ? Image.asset(
                //     AssetsManager.recipe_place_holder,
                //     width: 90,
                //     height: 90,
                //     fit: BoxFit.cover,
                //   )
                //       : CustomNetworkImage(
                //     imageUrl: recipes.mediaUrl!,
                //     width: 90,
                //     height: 90,
                //     errorWidget: Image.asset(
                //       AssetsManager.recipe_place_holder,
                //       width: 90,
                //       height: 90,
                //       fit: BoxFit.cover,
                //     ),
                //   ),
                // ),
                ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Builder(
              builder: (context) {
                // ✅ Check if recipe has media
                if (recipes.recipeMedia != null && recipes.recipeMedia!.isNotEmpty) {
                  // ✅ Step 1: Calculate safeIndex
                  int safeIndex;
                  if (recipes.recipeMedia![0].mediaType == "IMAGE") {
                    safeIndex = recipes.coverMediaIndex - 1;
                  } else {
                    safeIndex = recipes.coverMediaIndex;
                  }

                  // ✅ Step 2: Ensure safeIndex is within bounds
                  if (safeIndex < 0 || safeIndex >= recipes.recipeMedia!.length) {
                    safeIndex = 0;
                  }

                  // ✅ Step 3: Load from media list
                  return CommonImage(
                    imageSource: recipes.recipeMedia![safeIndex].mediaUrl,
                    placeholder: AssetsManager.recipe_place_holder,
                    width: 90,
                    height: 90,
                    fit: BoxFit.cover,
                  );
                } else {
                  // ✅ Fallback placeholder
                  return Image.asset(
                    AssetsManager.recipe_place_holder,
                    width: 90,
                    height: 90,
                    fit: BoxFit.cover,
                  );
                }
              },
            ),
          ),
              ],
            ),

            SizedBox(width: 8),

            // Text and Action Section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CustomTextTitle(title: recipes.name, size: 14,),
                      ),
                      Visibility(
                        visible: isEditable,
                        child: Transform.scale(
                          scale: 0.8, // Adjust the scale factor (e.g., 1.2 = 20% larger)
                          child: CustomHoverMenu(
                            items: ['Delete'],
                            itemIcons: [Icons.delete],
                            onItemSelected: onMenuItemSelected,
                            menuWidth: 140.0,
                            menuTitle: 'Show Menu',
                            triggerIcon: Icons.more_vert_outlined,
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 2),

                  CustomTextMedium(
                    title: recipes.description ?? '',
                    maxLines: 2,
                    size: 12,
                  ),

                  SizedBox(height: 15.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 5.w),
                    child: CustomPaint(
                      painter: DottedLinePainter(
                        strokeWidth: 1,
                        dashWidth: 5,
                        color: AppColors.lightestGreyColor,
                      ),
                      size: Size(double.infinity, 2),
                    ),
                  ),

                  SizedBox(height: 6),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset(
                            AssetsManager.rating,
                            height: 24.h,
                            width: 24.w,
                          ),
                          SizedBox(width: 4.w),
                          CustomTextMedium(
                            title: "${recipes.reviewsCount} ( reviews)",
                            size: 12,//responsiveFont(20).sp,
                          ),
                        ],
                      ),
                      CustomTextMedium(
                        title: DateFormatter.timeAgo(recipes.dateAdded!),
                        size: 12,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );

  }
}
