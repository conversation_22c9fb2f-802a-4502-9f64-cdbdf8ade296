

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../app/imports/core_imports.dart';

class CustomRightLeftArrow extends StatelessWidget {
  final String icon;
  final VoidCallback onPressed;
  const CustomRightLeftArrow({super.key , required this.icon , required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Container(
    height: 70.h,
      width: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(2, 2),
          ),
        ],
      ),
      child: Center(
        child: IconButton(
          icon:SvgPicture.asset(icon,height: 20,width: 15,), //Icon(icon, color: AppColors.primaryColor),
          onPressed: onPressed,
        ),
      ),
    );
  }
}

