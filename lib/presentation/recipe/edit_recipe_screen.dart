import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/presentation/recipe/subview/media_picker_grid.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_directions_widegts.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_serving_ideas_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_wine_widgets.dart';
import 'package:mastercookai/presentation/recipe/subview/recipe_cards.dart';
import 'package:mastercookai/presentation/recipe/tab/tab_edit_recipe.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_searchbar.dart';
import '../../../core/widgets/custom_drawer.dart';
import '../../core/data/models/category_response.dart';
import '../../core/data/models/cuisines_response.dart';
import '../../core/data/models/recipe_detail_response.dart';
import '../../core/data/models/recipe_response.dart';
import '../../core/data/request_query/create_recipe_request.dart';
import '../../core/data/request_query/recipe_meta_data.dart';
import '../../core/providers/recipe/directions_provider.dart';
import '../../core/providers/recipe/ingrident_provider.dart';
import '../../core/providers/recipe/media_provider.dart';
import '../../core/providers/recipe_notifier.dart';
import '../../core/providers/recipe/author_provider.dart';
import '../../core/providers/recipe/metadata_provider.dart';
import '../../core/providers/recipe/notes_provider.dart';
import '../../core/providers/single_recipe_notifier.dart';
import '../../core/utils/Utils.dart';
import '../../core/network/app_status.dart';
import '../../core/widgets/no_data_widget.dart';
import '../shimer/cookbook_list_shimmer.dart';
import '../shimer/recipe_detail_shimmer.dart';
import 'edit_recipe_subview/buildAuthorSection.dart';
import 'edit_recipe_subview/buildIngredientsSection.dart';
import 'edit_recipe_subview/buildMainContent.dart';
import 'edit_recipe_subview/buildNotesSection.dart';
import 'edit_recipe_subview/buildNutritionSection.dart';
import 'edit_recipe_subview/buildRecipeHeader.dart';
import 'edit_recipe_subview/buildRecipeMetadata.dart';

import 'mobile_ui/edit_recipe_screen_mobile.dart';

class EditRecipeScreen extends ConsumerStatefulWidget {
  int recipeId;
  final int cookbookId;
  List<Recipe> recipesList;
  RecipeDetails recipeDetails;
  final List<Categories> categories;
  final List<Cuisines> cuisines;
  final int? initialStep;

  EditRecipeScreen({
    required this.recipeId,
    required this.cookbookId,
    required this.recipesList,
    required this.recipeDetails,
    required this.categories,
    required this.cuisines,
    this.initialStep,
    Key? key,
  }) : super(key: key ?? ValueKey('edit_recipe_${recipeId}'));

  @override
  ConsumerState<EditRecipeScreen> createState() => _EditRecipeScreenState();
}

class _EditRecipeScreenState extends ConsumerState<EditRecipeScreen> {
  final TextEditingController searchController = TextEditingController();
  int _selectedRecipeIndex = 0;
  int? _selectedRecipeId;
  late TextEditingController recipeNameController;
  late TextEditingController recipeDescController;
  late List<String> ingredients;
  late NutritionInfo nutritionFacts;
  late List<Directions> directions;
  late List<RecipeMedia> recipeMedia;
  late String servingIdeas;
  late String wine;
  late String author;
  late String authorMediaUrl;
  late String copyright;
  late String source;
  late String notes;
  String? _category;
  String? _cuisine;
  bool _isLoading = true;
  bool isUpdate = false;
  Timer? _recipeSearchDebounce;
  int _currentPage = 1;
  String? selectedMediaUrl;
  File? selectedMediaFile;

  @override
  void initState() {
    super.initState();
    debugPrint('=== EDIT_RECIPE_INIT_STATE ===');
    debugPrint('InitState: Target recipeId = ${widget.recipeId}');
    debugPrint('InitState: Widget key = ${widget.key}');

    _initializeForRecipe();
  }

  @override
  void didUpdateWidget(EditRecipeScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the recipeId has changed
    if (oldWidget.recipeId != widget.recipeId) {
      debugPrint('=== EDIT_RECIPE_DID_UPDATE_WIDGET ===');
      debugPrint('Recipe ID changed from ${oldWidget.recipeId} to ${widget.recipeId}');

      // Reset and reinitialize everything for the new recipe
      _resetState();
      _initializeForRecipe();
    }
  }

  void _initializeForRecipe() {
    // Set the selected recipe ID immediately
    _selectedRecipeId = widget.recipeId;

    _initializeData();

    // Add listener to search controller for API-based search (only if not already added)
    if (!searchController.hasListeners) {
      searchController.addListener(_onSearchChanged);
    }

    // Initialize the selected recipe index after the frame is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSelectedRecipe();
    });
  }

  void _resetState() {
    // Reset all state variables to their initial values
    _selectedRecipeIndex = 0;
    _selectedRecipeId = null;
    _isLoading = true;
    isUpdate = false;
    _currentPage = 1;
    selectedMediaUrl = null;
    selectedMediaFile = null;
    _category = null;
    _cuisine = null;

    // Cancel any pending debounce timer
    _recipeSearchDebounce?.cancel();

    // Clear search controller
    searchController.clear();

    // Dispose text controllers if they exist (they will be recreated in _initializeData)
    try {
      recipeNameController.dispose();
      recipeDescController.dispose();
    } catch (e) {
      debugPrint('Error disposing controllers: $e');
    }

    // Clear all providers to ensure clean state
    _clearProviders();

    debugPrint('State reset completed for new recipe');
  }

  void _clearProviders() {
    // Clear all providers to ensure clean state for new recipe
    try {
      ref.read(directionStepsProvider.notifier).setDirections([]);
      ref.read(ingredientsProvider.notifier).clearIngredients();
      ref.read(mediaFilesProvider.notifier).clear();
      ref.read(notesProvider.notifier).setNote('');

      // Reset author provider
      final authorNotifier = ref.read(authorProvider.notifier);
      authorNotifier.updateAuthorName('');
      authorNotifier.updateSource('');
      authorNotifier.updateCopyright('');

      debugPrint('All providers cleared for new recipe');
    } catch (e) {
      debugPrint('Error clearing providers: $e');
    }
  }

  void _resetStateForNewRecipe() {
    // This method is called when user selects a different recipe within the same EditRecipeScreen
    debugPrint('=== RESETTING STATE FOR NEW RECIPE ===');

    // Reset loading state
    _isLoading = true;
    isUpdate = false;
    selectedMediaUrl = null;
    selectedMediaFile = null;
    _category = null;
    _cuisine = null;

    // Clear providers
    _clearProviders();

    // Note: We don't dispose text controllers here as they will be updated when new recipe data arrives
    // The controllers will be updated in _initializeDataFromRecipe method

    debugPrint('State reset completed for recipe selection change');
  }

  void _initializeSelectedRecipe() {
    debugPrint('=== _initializeSelectedRecipe CALLED ===');
    debugPrint('Target recipeId: ${widget.recipeId}');

    // Always set the selected recipe ID first
    _selectedRecipeId = widget.recipeId;

    // Try to find the recipe in the current list
    final recipeListState = ref.read(recipeNotifierProvider);
    if (recipeListState.data != null && recipeListState.data!.isNotEmpty) {
      debugPrint('INIT_SELECTED_RECIPE: Searching in ${recipeListState.data!.length} recipes');
      debugPrint('INIT_SELECTED_RECIPE: Available recipes: ${recipeListState.data!.map((r) => '${r.id}: ${r.name}').join(', ')}');

      final index = recipeListState.data!
          .indexWhere((recipe) => recipe.id == widget.recipeId);

      if (index != -1) {
        setState(() {
          _selectedRecipeIndex = index;
          widget.recipesList = recipeListState.data!;
          debugPrint('INIT_SELECTED_RECIPE SUCCESS: Found recipe at index=$_selectedRecipeIndex');
          debugPrint('INIT_SELECTED_RECIPE SUCCESS: Recipe name: ${recipeListState.data![index].name}');
        });
      } else {
        debugPrint('INIT_SELECTED_RECIPE: Recipe ID ${widget.recipeId} NOT FOUND in current list');
        debugPrint('INIT_SELECTED_RECIPE: Setting index to 0 and fetching fresh data');
        setState(() {
          _selectedRecipeIndex = 0;
          widget.recipesList = recipeListState.data!;
        });

        // Fetch fresh recipe list if current recipe not found
        if (context.mounted) {
          ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            context: context,
            cookbookId: widget.cookbookId,
            currentPage: 1,
          );
        }
      }
    } else {
      debugPrint('INIT_SELECTED_RECIPE: Recipe list is empty, fetching recipes');
      setState(() {
        _selectedRecipeIndex = 0;
      });

      // Fetch recipe list if empty
      if (context.mounted) {
        ref.read(recipeNotifierProvider.notifier).fetchRecipes(
          context: context,
          cookbookId: widget.cookbookId,
          currentPage: 1,
        );
      }
    }

    // Always fetch the specific recipe details
    debugPrint('INIT_SELECTED_RECIPE: Fetching recipe details for ID ${widget.recipeId}');
    ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
          context: context,
          cookbookId: widget.cookbookId,
          recipeId: widget.recipeId,
        );
  }

  void _onSearchChanged() {
    final text = searchController.text.trim();
    debugPrint('Search query changed: $text');
    if (text.length >= 3 || text.isEmpty) {
      _debounceSearch(text);
    }
  }

  void _debounceSearch(String query) {
    _recipeSearchDebounce?.cancel();
    _recipeSearchDebounce = Timer(const Duration(milliseconds: 500), () async {
      debugPrint('Debounced search with query: $query');
      await ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: widget.cookbookId,
            search: query.isEmpty ? null : query,
            reset: true,
            context: context,
            currentPage: 1,
          );
      if (context.mounted) {
        final recipeListState = ref.read(recipeNotifierProvider);
        if (recipeListState.data != null && recipeListState.data!.isNotEmpty) {
          final index = recipeListState.data!
              .indexWhere((recipe) => recipe.id == widget.recipeId);
          setState(() {
            _selectedRecipeIndex = index != -1 ? index : 0;
            if (index == -1) {
              // If current recipe not found in search results, select first recipe
              widget.recipeId = recipeListState.data![0].id;
              _fetchAndUpdateRecipe(recipeListState.data![0]);
            }
            debugPrint(
                'Search updated: index=$_selectedRecipeIndex, recipeId=${widget.recipeId}');
          });
        } else {
          setState(() {
            _selectedRecipeIndex = -1;
            debugPrint('No recipes found after search');
          });
        }
      }
    });
  }

  void _initializeData() {
    // Create new text controllers (they may have been disposed in _resetState)
    recipeNameController = TextEditingController(text: widget.recipeDetails.name ?? '');
    recipeDescController = TextEditingController(text: widget.recipeDetails.description ?? '');

    // Initialize all other data
    ingredients = widget.recipeDetails.ingredients ?? [];
    nutritionFacts = widget.recipeDetails.nutritionInfo ?? NutritionInfo();
    directions = List.from(widget.recipeDetails.directions ?? []);
    recipeMedia = List.from(widget.recipeDetails.recipeMedia ?? []);
    servingIdeas = widget.recipeDetails.servingIdeas ?? '';
    wine = widget.recipeDetails.wine ?? '';
    author = widget.recipeDetails.author ?? '';
    authorMediaUrl = widget.recipeDetails.authorMediaUrl ?? '';
    copyright = widget.recipeDetails.copyright ?? '';
    source = widget.recipeDetails.source ?? '';
    notes = widget.recipeDetails.notes ?? '';
    _category = widget.recipeDetails.category ??
        (widget.categories.isNotEmpty ? widget.categories.first.name : null);
    _cuisine = widget.recipeDetails.cuisine ??
        (widget.cuisines.isNotEmpty ? widget.cuisines.first.name : null);

    // Initialize selected index to 0 - will be properly set in initState's postFrameCallback
    _selectedRecipeIndex = 0;
    debugPrint('INIT_DATA: Initialized _selectedRecipeIndex to 0, will be updated in postFrameCallback');

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeProviders();
      setState(() => _isLoading = false);
    });
  }

  void _initializeProviders() {
    final categoryId = widget.categories
        .firstWhere(
          (c) => c.name == _category,
          orElse: () => widget.categories.isNotEmpty
              ? widget.categories.first
              : Categories(id: 0, name: 'Unknown'),
        )
        .id;
    final cuisineId = widget.cuisines
        .firstWhere(
          (c) => c.name == _cuisine,
          orElse: () => widget.cuisines.isNotEmpty
              ? widget.cuisines.first
              : Cuisines(id: 0, name: 'Unknown'),
        )
        .id;

    ref.read(directionStepsProvider.notifier).setDirections(directions);
    ref.read(ingredientsProvider.notifier)
      ..clearIngredients()
      ..updateIngredients(ingredients);

    ref.read(recipeMetadataProvider.notifier).updateAll(
          recipeId: widget.recipeId,
          cookbookId: widget.cookbookId,
          recipesList: widget.recipesList,
          recipeDetails: widget.recipeDetails,
          categoryId: categoryId,
          cuisineId: cuisineId,
        );

    ref.read(recipeMetadataProvider.notifier)
      ..updateYield(widget.recipeDetails.yield)
      ..updateServings(widget.recipeDetails.servings)
      ..updatePrepTime(widget.recipeDetails.prepTime)
      ..updateCookTime(widget.recipeDetails.cookTime)
      ..updateTotalTime(widget.recipeDetails.totalTime)
      ..updateAuthor(widget.recipeDetails.author)
      ..updateRecipeThumbnailFileUrl(
          widget.recipeDetails.recipeThumbnailFileUrl)
      ..updateAuthor(widget.recipeDetails.author)
      ..updateAuthorMediaUrl(widget.recipeDetails.authorMediaUrl)
      ..updateCopyright(widget.recipeDetails.copyright)
      ..updateSource(widget.recipeDetails.source)
      ..updateServingIdeas(widget.recipeDetails.servingIdeas)
      ..updateDirections(directions)
      ..updateWine(widget.recipeDetails.wine);

    ref.read(notesProvider.notifier).setNote(widget.recipeDetails.notes ?? '');
    ref.read(authorProvider.notifier)
      ..updateAuthorName(widget.recipeDetails.author ?? '')
      ..updateSource(widget.recipeDetails.source ?? '')
      ..updateCopyright(widget.recipeDetails.copyright ?? '');

    // Initialize media provider with current recipe's media
    ref.read(mediaFilesProvider.notifier).clear();
    if (recipeMedia.isNotEmpty) {
      ref.read(mediaFilesProvider.notifier).set(recipeMedia);
    }
  }



  void _initializeDataFromRecipe(RecipeDetails recipeDetails) {
    // Update controllers
    recipeNameController.text = recipeDetails.name ?? '';
    recipeDescController.text = recipeDetails.description ?? '';

    // Update local variables
    ingredients = recipeDetails.ingredients ?? [];
    nutritionFacts = recipeDetails.nutritionInfo ?? NutritionInfo();
    directions = List.from(recipeDetails.directions ?? []);
    recipeMedia = List.from(recipeDetails.recipeMedia ?? []);
    servingIdeas = recipeDetails.servingIdeas ?? '';
    wine = recipeDetails.wine ?? '';
    author = recipeDetails.author ?? '';
    authorMediaUrl = recipeDetails.authorMediaUrl ?? '';
    copyright = recipeDetails.copyright ?? '';
    source = recipeDetails.source ?? '';
    notes = recipeDetails.notes ?? '';
    _category = recipeDetails.category ??
        (widget.categories.isNotEmpty ? widget.categories.first.name : null);
    _cuisine = recipeDetails.cuisine ??
        (widget.cuisines.isNotEmpty ? widget.cuisines.first.name : null);

    // Reinitialize providers with new data
    _initializeProviders();
  }

  Widget _buildRecipeList(dynamic recipeListState) {
    return Column(
      children: [
        SizedBox(height: 20.h),
        CustomSearchBar(
          width: DeviceUtils().isTabletOrIpad(context)
              ? 368
              : MediaQuery.of(context).size.width,
          controller: searchController,
          onClear: () => _onClearFiltersPressed(context),
        ),
        Expanded(
          child: _buildRecipeListContent(recipeListState),
        ),
      ],
    );
  }

  Widget _buildRecipeListContent(dynamic recipeListState) {
    if (recipeListState.status == AppStatus.loading ||
        recipeListState.status == AppStatus.loadingMore) {
      return const CookbookListShimmer(itemCount: 20);
    } else if (recipeListState.status == AppStatus.error ||
        recipeListState.data == null ||
        recipeListState.data!.isEmpty) {
      return const Center(
        child: NoDataWidget(
          title: "No Recipe Lists Found",
          subtitle:
              "Try adjusting your search terms or create a new recipe list",
          width: 250,
          height: 250,
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(12),
      itemCount: recipeListState.data!.length,
      separatorBuilder: (_, __) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final recipe = recipeListState.data![index];
        return GestureDetector(
          onTap: () {
            // Check if this is a different recipe
            if (recipe.id != widget.recipeId) {
              debugPrint('=== RECIPE SELECTION CHANGED ===');
              debugPrint('Changing from recipe ${widget.recipeId} to ${recipe.id}');

              // Reset state for new recipe
              _resetStateForNewRecipe();

              setState(() {
                _selectedRecipeIndex = index;
                _selectedRecipeId = recipe.id;
                widget.recipeId = recipe.id;
                debugPrint('Selected recipe: ${recipe.name}, index: $_selectedRecipeIndex');
              });

              // Fetch the new recipe details
              ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
                    context: context,
                    cookbookId: widget.cookbookId,
                    recipeId: recipe.id,
                  );
            }
          },
          child: RecipeCardItem(
            key: ValueKey(recipe.id),
            recipes: recipe,
            isSelected: _selectedRecipeIndex == index,
            isEditable: false,
            onMenuItemSelected: (String value) {},
          ),
        );
      },
    );
  }

  Future<void> _fetchAndUpdateRecipe(Recipe selectedRecipe) async {
    await ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
      context: context,
      cookbookId: widget.cookbookId,
      recipeId: selectedRecipe.id,
    );

    final singleRecipeState = ref.read(singleRecipeNotifierProvider);
    if (singleRecipeState.status == AppStatus.success && singleRecipeState.data != null) {
      final newRecipeDetails = singleRecipeState.data!;

      // Update widget properties with new recipe data
      widget.recipeDetails = newRecipeDetails;

      // Reinitialize all data with the new recipe
      _initializeDataFromRecipe(newRecipeDetails);
    }
  }

  void _onClearFiltersPressed(BuildContext context) {
    debugPrint('Clearing filters...');
    setState(() {
      searchController.clear();
      _currentPage = 1; // Reset pagination
    });

    ref.read(recipeNotifierProvider.notifier).fetchRecipes(
          cookbookId: widget.cookbookId,
          search: null,
          reset: true,
          context: context,
          currentPage: 1,
        );
  }

  @override
  void dispose() {
    searchController.removeListener(_onSearchChanged);

    // Safely dispose controllers
    try {
      recipeNameController.dispose();
      recipeDescController.dispose();
    } catch (e) {
      debugPrint('Error disposing controllers in dispose: $e');
    }

    searchController.dispose();
    _recipeSearchDebounce?.cancel();
    super.dispose();
  }

  void updateData() {
    if (recipeNameController.text.trimRight().isEmpty) {
      Utils().showSnackBar(context, "Please enter a recipe name");
      return;
    }

    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    final authorData = ref.read(authorProvider);
    final notes = ref.read(notesProvider);
    ingredients = ref.read(ingredientsProvider);

    final updatedMetadata = RecipeMetadata(
      recipeId: widget.recipeId,
      cookbookId: widget.cookbookId,
      recipesList: widget.recipesList,
      name: recipeNameController.text,
      description: recipeDescController.text,
      ingredients: ingredients,
      nutritionFacts: nutritionFacts,
      author: authorData.authorName ?? author,
      authorMediaUrl: authorData.image?.path ?? authorMediaUrl,
      copyright: authorData.copyright ?? copyright,
      source: authorData.source ?? source,
      directions: directions,
      servingIdeas: metadata.servingIdeas ?? servingIdeas,
      wine: metadata.wine ?? wine,
      recipeMedia: recipeMedia,
      categoryId: metadata.categoryId,
      cuisineId: metadata.cuisineId,
      yieldValue: metadata.yieldValue,
      servings: metadata.servings,
      prepTime: metadata.prepTime,
      cookTime: metadata.cookTime,
      totalTime: metadata.totalTime,
      wineDesc: metadata.wineDesc,
    );

    ref.read(recipeMetadataProvider.notifier).updateAll(
          recipeId: updatedMetadata.recipeId!,
          cookbookId: updatedMetadata.cookbookId!,
          recipesList: updatedMetadata.recipesList!,
          recipeDetails: RecipeDetails(
            name: updatedMetadata.name,
            description: updatedMetadata.description,
            ingredients: updatedMetadata.ingredients,
            nutritionInfo: updatedMetadata.nutritionFacts,
            author: updatedMetadata.author,
            authorMediaUrl: updatedMetadata.authorMediaUrl,
            copyright: updatedMetadata.copyright,
            source: updatedMetadata.source,
            directions: updatedMetadata.directions,
            servingIdeas: updatedMetadata.servingIdeas,
            wine: updatedMetadata.wine,
            recipeMedia: updatedMetadata.recipeMedia,
            category: _category,
            cuisine: _cuisine,
            servings: updatedMetadata.servings,
            prepTime: updatedMetadata.prepTime,
            cookTime: updatedMetadata.cookTime,
            totalTime: updatedMetadata.totalTime,
          ),
          categoryId: updatedMetadata.categoryId,
          cuisineId: updatedMetadata.cuisineId,
        );

    ref.read(notesProvider.notifier).setNote(notes ?? '');
    ref.read(authorProvider.notifier)
      ..updateAuthorName(authorData.authorName ?? '')
      ..updateSource(authorData.source ?? '')
      ..updateCopyright(authorData.copyright ?? '');
  }

  Future<void> _updateRecipeAndRefresh(CreateRecipeRequest request) async {
    final result = await ref.read(recipeNotifierProvider.notifier).updateRecipe(
          context: context,
          request: request,
          cookbookId: widget.cookbookId,
          recipeId: widget.recipeId,
        );

    if (result) {
      await ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: widget.cookbookId,
            cookbookName: recipeNameController.text,
            reset: true,
            context: context,
            currentPage: 1,
          );

      final updatedState = ref.read(recipeNotifierProvider);
      setState(() {
        widget.recipesList = updatedState.data ?? widget.recipesList;
        isUpdate = true;
      });

      await ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
            context: context,
            cookbookId: widget.cookbookId ?? 0,
            recipeId: widget.recipeId,
          );
    }
  }

  void _saveIngredient(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null || metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select category and cuisine");
      return;
    }

    final request = CreateRecipeRequest(
      type: RecipeSection.INGREDIENT.name,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      ingredients: ingredients.isEmpty ? null : ingredients,
    );

    await _updateRecipeAndRefresh(request);
  }

  void _saveAuthor(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final authorData = ref.read(authorProvider);
    // Create UpdateRecipeRequest (adjust fields based on your API model)

    final request = CreateRecipeRequest(
      type: RecipeSection.AUTHOR.name,
      categoryId: metadata.categoryId!,
      existingAuthorMediaFileId: authorData.image != null
          ? null
          : widget.recipeDetails.authorMediaFileId,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      authorName:
          authorData.authorName?.isEmpty ?? true ? null : authorData.authorName,
      authorSource:
          authorData.source?.isEmpty ?? true ? null : authorData.source,
      authorCopyright:
          authorData.copyright?.isEmpty ?? true ? null : authorData.copyright,
      authorProfileFile: authorData.image,
    );

    updateRecipe(request);
  }

  void _saveNotes(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final notes = ref.read(notesProvider);
    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.NOTES.name,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      notes: notes ?? '',
    );

    debugPrint("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveBasicInfo(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.BASIC.name,
      name: recipeNameController.text,
      description: recipeDescController.text ?? '',
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      yieldValue: metadata.yieldValue ?? '',
      servings: metadata.servings,
      prepTime: metadata.prepTime ?? '',
      cookTime: metadata.cookTime ?? '',
      totalTime: metadata.totalTime ?? '',
    );

    debugPrint("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveDirections(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    final directionJson =
        ref.read(directionStepsProvider.notifier).directionsJson;
    // Filter media files to include only steps with valid media
    final directionMediaFiles = ref
        .read(directionStepsProvider.notifier)
        .mediaFiles!
        .asMap()
        .entries
        .where(
            (entry) => entry.value != null) // Only include non-null media files
        .map((entry) => entry.value)
        .toList();

    // Create UpdateRecipeRequest
    final request = CreateRecipeRequest(
      type: RecipeSection.DIRECTION.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      directionsJson: directionJson,
      directionMediaFiles:
          directionMediaFiles.isEmpty ? null : directionMediaFiles,
    );

    debugPrint("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveOthers(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    final recipeMetadata = ref.watch(recipeMetadataProvider);
    final wineDesc = recipeMetadata.wineDesc;

    if ((wineDesc == null || wineDesc.isEmpty ?? false) &&
        (metadata.servingIdeas == null || metadata.servingIdeas!.isEmpty ??
            false)) {
      Utils().showFlushbar(context,
          message: "Please add wine & sevings description", isError: true);
      return;
    }
    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.OTHER.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      servingIdeas: metadata.servingIdeas ?? '',
      wine: wineDesc,
    );

    debugPrint("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveMediaFiles(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final mediaFiles = ref.watch(mediaFilesProvider);
    final selectedMediaFiles = mediaFiles.mediaFiles
        .map((media) => media.mediaFile)
        .whereType<File>()
        .toList();
    List<int> existingMedia = mediaFiles.mediaFiles
        .map((media) => media.mediaFileId)
        .whereType<int>()
        .toList();

    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.RECIPE_MEDIA.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      existingRecipeMediaFileIds: existingMedia,
      coverRecipeMediaIndex: mediaFiles.coverIndex,
      recipeMediaFiles: selectedMediaFiles.isEmpty ? null : selectedMediaFiles,
    );

    debugPrint("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  Future<void> updateRecipe(CreateRecipeRequest request) async {
    final recipeState = ref.watch(recipeNotifierProvider);
    // //  Call updateRecipe API
    final result = await ref.read(recipeNotifierProvider.notifier).updateRecipe(
        context: context,
        request: request,
        cookbookId: widget.cookbookId,
        recipeId: widget.recipeId);

    if (result) {
      ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: widget.cookbookId,
            cookbookName: recipeNameController.text,
            reset: true,
            context: context,
            currentPage: 1,
          );

      ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
          context: context,
          cookbookId: widget.cookbookId ?? 0,
          recipeId: widget.recipeId);

      setState(() {
        isUpdate = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final recipeState = ref.watch(recipeNotifierProvider);

    // Listen to recipe list changes and update selected index - exactly like recipe detail screen
    ref.listen(recipeNotifierProvider, (previous, next) {
      if (next.data != null && next.data!.isNotEmpty && context.mounted) {
        final index =
            next.data!.indexWhere((recipe) => recipe.id == _selectedRecipeId);
        if (index != -1 && index != _selectedRecipeIndex) {
          setState(() {
            _selectedRecipeIndex = index;
            _selectedRecipeId = next.data![index].id;
            widget.recipesList = next.data!;
            debugPrint('RECIPE_LIST_LISTENER: Updated selected index to: $_selectedRecipeIndex for recipeId: $_selectedRecipeId');
            debugPrint('RECIPE_LIST_LISTENER: Selected recipe name: ${next.data![index].name}');
          });
          ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
                context: context,
                cookbookId: widget.cookbookId,
                recipeId: _selectedRecipeId!,
              );
        }
      }
    });

    // Listen to single recipe changes and update recipe details
    ref.listen(singleRecipeNotifierProvider, (previous, next) {
      if (next.status == AppStatus.success && next.data != null && context.mounted) {
        final newRecipeDetails = next.data!;

        // Update widget properties with new recipe data
        widget.recipeDetails = newRecipeDetails;
        if (newRecipeDetails.id != null) {
          widget.recipeId = newRecipeDetails.id!;
        }

        // Update the selected recipe ID to match the new recipe
        if (newRecipeDetails.id != null) {
          _selectedRecipeId = newRecipeDetails.id!;
          debugPrint('SINGLE_RECIPE_LISTENER: Updated _selectedRecipeId to: $_selectedRecipeId');
        }

        // Reinitialize all data with the new recipe
        _initializeDataFromRecipe(newRecipeDetails);

        // Reinitialize providers with new recipe data
        _initializeProviders();

        // Handle media updates
        final mediaList = newRecipeDetails.recipeMedia;
        if (mediaList != null && mediaList.isNotEmpty) {
          final firstMedia = mediaList.first;
          if (firstMedia.mediaFile != null) {
            setState(() {
              selectedMediaFile = firstMedia.mediaFile;
              selectedMediaUrl = null;
              debugPrint('Updated selectedMediaFile: ${firstMedia.mediaFile?.path}');
            });
          } else if (firstMedia.mediaUrl != null) {
            setState(() {
              selectedMediaUrl = firstMedia.mediaUrl;
              selectedMediaFile = null;
              debugPrint('Updated selectedMediaUrl: $selectedMediaUrl');
            });
          }
        } else {
          setState(() {
            selectedMediaUrl = null;
            selectedMediaFile = null;
            debugPrint('No valid media found in recipeMedia');
          });
        }

        setState(() {
          _isLoading = false;
        });
      }
    });

    // Listen to media provider changes
    ref.listen(mediaFilesProvider, (previous, next) {
      if (next.mediaFiles.isNotEmpty && next.mediaFiles[0] != null) {
        setState(() {
          selectedMediaFile = next.mediaFiles[0]!.mediaFile;
          selectedMediaUrl = null;
          debugPrint('Media updated from mediaFilesProvider: ${next.mediaFiles[0]!.mediaFile?.path}');
        });
      }
    });



    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 1200;

    final ingredientsCard =
        buildIngredientsSection(context, onSaveIngredient: () {
      _saveIngredient(context);
    });
    final nutritionCard =
        buildNutritionSection(context, nutritionFacts, onSaveNutrition: () {});
    final authorCard = buildEditAuthorSection(
        context, author, authorMediaUrl, copyright, source, onSaveAuthor: () {
      _saveAuthor(context);
    });
    final notesCard = buildNotesSection(notes, onUpdateData: () {
      _saveNotes(context);
    });

    return getDeviceType(context).name == 'mobile'
        ? EditRecipeScreenMobile(
            recipeId: widget.recipeId,
            recipesList: widget.recipesList,
            cookbookId: widget.cookbookId,
            recipeDetails: widget.recipeDetails,
            categories: widget.categories,
            cuisines: widget.cuisines,
            initialStep: widget.initialStep,
          )
        : Scaffold(
            drawer: isSmallScreen
                ? CustomDrawer(
                    title: 'Recipes',
                    state: recipeState,
                    buildContent: _buildRecipeList)
                : null,
            appBar: CustomAppBar(
              title: 'Edit Recipe',
              showDrawerIcon: isSmallScreen,
              onPressed: () async {
                if (isUpdate) {
                  final recipeState = ref.watch(recipeNotifierProvider);
                  widget.recipesList = recipeState.data ?? [];
                  context.go(
                    '/cookbook/cookbookDetail/recipeDetail/${widget.recipeId}',
                    extra: {
                      'id': widget.recipeId,
                      'recipeList': widget.recipesList,
                      'recipeName': recipeNameController.text,
                      'cookbookId': widget.cookbookId,
                    },
                  );
                } else {
                  Navigator.pop(context);
                }
              },
            ),
            body: Stack(
              fit: StackFit.expand,
              children: [
                Image.asset(
                  AssetsManager.background_img,
                  fit: BoxFit.cover,
                ),
                getDeviceType(context) == DeviceType.tablet
                    ? TabEditRecipe(
                        recipeNameController: recipeNameController,
                        recipeDescController: recipeDescController,
                        recipeId: widget.recipeId,
                        cookbookId: widget.cookbookId,
                        recipesList: widget.recipesList,
                        recipeDetails: widget.recipeDetails,
                        recipeMedia: recipeMedia,
                        recipeThumbnailFileUrl:
                            widget.recipeDetails.recipeThumbnailFileUrl ?? '',
                        categoriesList: widget.categories,
                        cuisinesList: widget.cuisines,
                        saveMediaFiles: () {
                          _saveMediaFiles(context);
                        },
                        saveBasicInfo: () {
                          _saveBasicInfo(context);
                        },
                        saveDirections: () {
                          _saveDirections(context);
                        },
                        saveAuthor: () {
                          _saveAuthor(context);
                        },
                        servingIdeas: servingIdeas,
                        wine: wine,
                        author: author,
                        authorMediaUrl: authorMediaUrl,
                        copyright: copyright,
                        source: source,
                        notes: notes,
                        notesCard: notesCard,
                        ingredientsCard: ingredientsCard,
                        saveOthers: () {
                          _saveOthers(context);
                        },
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 3,
                            child: Container(
                              color: AppColors.secondaryColor,
                              child: _buildRecipeList(recipeState),
                            ),
                          ),
                          Expanded(
                            flex: 6,
                            child: Consumer(
                              builder: (context, ref, _) {
                                final singleRecipeState = ref.watch(singleRecipeNotifierProvider);

                                if (singleRecipeState.status == AppStatus.loading) {
                                  return RecipeDetailsShimmer();
                                }

                                if (singleRecipeState.status == AppStatus.error ||
                                    singleRecipeState.data == null) {
                                  return const Center(
                                    child: NoDataWidget(
                                      title: "No Recipe Detail Found",
                                      subtitle: "Try selecting a different recipe to view the details.",
                                      width: 250,
                                      height: 250,
                                    ),
                                  );
                                }

                                return SingleChildScrollView(
                              child: Padding(
                                padding: EdgeInsets.only(
                                    left: 20.w,
                                    right: 20.w,
                                    top: 30.h,
                                    bottom: 30.h),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: AppColors.secondaryColor,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black
                                            .withValues(alpha: 0.05),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      )
                                    ],
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        right: 30.w, left: 30.w, top: 30.h),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        buildRecipeHeader(
                                          context,
                                          ref,
                                          recipeNameController:
                                              recipeNameController,
                                          recipeDescController:
                                              recipeDescController,
                                        ),
                                        SizedBox(height: 30.h),
                                        buildMainContent(
                                            recipeMedia: recipeMedia,
                                            recipeThumbnailFileUrl: widget
                                                .recipeDetails
                                                .recipeThumbnailFileUrl,
                                            categoriesList: widget.categories,
                                            cuisinesList: widget.cuisines,
                                            category: _category,
                                            cuisine: _cuisine,
                                            coverMediaIndex: widget.recipeDetails.coverMediaIndex,
                                            saveMediaFiles: () {
                                              _saveMediaFiles(context);
                                            },
                                            saveBasicInfo: () {
                                              _saveBasicInfo(context);
                                            }),
                                        SizedBox(height: 40.h),
                                        CustomDirectionsWidgets(
                                          isCallFromEdit: true,
                                          callFromClipper: false,
                                          onUpdateData: () {
                                            _saveDirections(context);
                                          },
                                        ),
                                        SizedBox(height: 40.h),
                                        CustomServingWidget(
                                          servingIdeas: servingIdeas,
                                        ),
                                        SizedBox(height: 40.h),
                                        CustomWineWidget(
                                          wine: wine,
                                        ),
                                        SizedBox(height: 40.h),
                                        Center(
                                          child: CustomButton(
                                            text: "Save Changes",
                                            fontSize: 14,
                                            width: 240,
                                            onPressed: () {
                                              _saveOthers(context);
                                            },
                                          ),
                                        ),
                                        SizedBox(height: 40.h),
                                      ],
                                    ), // Close Column
                                  ), // Close inner Padding
                                ), // Close Container
                              ), // Close outer Padding
                            ); // Close SingleChildScrollView
                              },
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: SingleChildScrollView(
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        right: 20.w, top: 30.h, bottom: 30.h),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        ingredientsCard,
                                        SizedBox(height: 30.h),
                                        Visibility(
                                            visible: nutritionFacts.nutrients!=null||(nutritionFacts.nutrients?.isEmpty?? false),
                                            child: nutritionCard),
                                        SizedBox(height: 30.h),
                                        authorCard,
                                        SizedBox(height: 30.h),
                                        notesCard,
                                      ],
                                    ),
                                  ),
                            ),
                          ),
                        ],
                      ),
              ],
            ),
          );
  }
}
