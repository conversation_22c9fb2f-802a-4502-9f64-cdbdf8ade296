import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/core/providers/single_recipe_notifier.dart';

import '../../../../../app/imports/core_imports.dart';
import '../../../../../core/data/models/category_response.dart';
import '../../../../../core/data/models/cuisines_response.dart';
import '../../../../../core/data/models/recipe_detail_response.dart';
import '../../../../../core/data/models/recipe_response.dart';
import '../../../../../core/widgets/custom_info_cards.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../../cookbook/widgets/edit_button.dart';

Widget buildIngredientsSectionMobile(
    BuildContext context,
    List<Recipe> recipesList,
    int cookbookId,
    int recipeId,
    List<String>? ingredients,
    RecipeDetails recipeDetail,
    List<Categories>? categoriesList,
    List<Cuisines>? cuisinesList,
    {required WidgetRef ref}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      SizedBox(
        height: 16,
      ),
      Row(
        children: [
          CustomText(
            text: 'Ingredients',
            size: 14,
            weight: FontWeight.w600,
            color: AppColors.primaryGreyColor,
          ),
          Spacer(),
          Visibility(
            visible: ingredients?.isNotEmpty ?? false,
            child: Align(
              alignment: Alignment.topRight,
              child: EditButton(onPressed: () {
                ref
                    .read(singleRecipeNotifierProvider.notifier)
                    .goToEditRecipeScreen(
                        context,
                        recipeId,
                        cookbookId,
                        recipesList,
                        recipeDetail,
                        categoriesList ?? [],
                        cuisinesList ?? []);
              }),
            ),
          ),
        ],
      ),
      SizedBox(height: 20.h),
      ingredients!.isNotEmpty
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: ingredients.map((ingredient) {
                return Padding(
                  padding: EdgeInsets.only(bottom: 12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: SvgPicture.asset(AssetsManager.dot,
                            height: 4, width: 4),
                      ),
                      SizedBox(width: 10),
                      Expanded(
                          child: CustomText(
                        text: ingredient,
                        size: 14,
                        color: AppColors.primaryLightTextColor,
                        weight: FontWeight.w400,
                      ))
                    ],
                  ),
                );
              }).toList(),
            )
          : CustomInfoCard(
              title: 'No ingredients added yet',
              subheading: 'Add all the ingredients you need for this dish.',
              actionText: 'Add Ingredients',
              onActionPressed: () {
                ref
                    .read(singleRecipeNotifierProvider.notifier)
                    .goToEditRecipeScreen(
                        context,
                        recipeId,
                        cookbookId,
                        recipesList,
                        recipeDetail,
                        categoriesList!,
                        cuisinesList!);
              }),
      SizedBox(height: 20.h),
    ],
  );
}
