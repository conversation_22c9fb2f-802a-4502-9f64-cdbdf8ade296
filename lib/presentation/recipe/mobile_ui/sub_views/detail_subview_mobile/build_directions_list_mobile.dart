import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:http/http.dart' as ref;
import 'package:mastercookai/core/data/models/recipe_response.dart';
import '../../../../../app/imports/core_imports.dart';
import '../../../../../core/data/models/category_response.dart';
import '../../../../../core/data/models/cuisines_response.dart';
import '../../../../../core/data/models/recipe_detail_response.dart';
import '../../../../../core/providers/single_recipe_notifier.dart';
import '../../../../../core/utils/device_utils.dart';
import '../../../../../core/widgets/common_image.dart';
import '../../../../../core/widgets/custom_info_cards.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../../cookbook/widgets/custom_title_text.dart';
import '../../../../cookbook/widgets/edit_button.dart';

Widget buildDirectionsListMobile(
    BuildContext context,
    List<Directions> directions,
    RecipeDetails recipeDetail,
    List<Categories>? categoriesList,
    List<Cuisines>? cuisinesList,
    int recipeId,
    int cookbookId,
    List<Recipe> recipesList,
    {required WidgetRef ref}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      SizedBox(
        height: 16,
      ),
      Row(
        children: [
          CustomText(
            text: 'Directions',
            size: 14,
            color: AppColors.primaryGreyColor,
            weight: FontWeight.w600,
          ),
          CustomText(
            text: "(${directions.length} Steps)",
            size: 14,
            color: AppColors.textGreyColor,
          ),
          Spacer(),
          Visibility(
            visible: directions.isNotEmpty,
            child: Align(
              alignment: Alignment.topRight,
              child: EditButton(onPressed: () {
                ref
                    .read(singleRecipeNotifierProvider.notifier)
                    .goToEditRecipeScreen(
                        context,
                        recipeId,
                        cookbookId,
                        recipesList,
                        recipeDetail,
                        categoriesList!,
                        cuisinesList!,
                        initialStep: 3); // Step 3 is Directions
              }),
            ),
          ),
        ],
      ),
      SizedBox(height: 20.h),
      if (directions.isNotEmpty)
        ListView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: directions.length,
          itemBuilder: (context, index) {
            final step = directions[index];
            return Stack(
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 20.h),
                  padding: EdgeInsets.only(
                      bottom: 15.h, left: 15.w, right: 15.w, top: 15.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border:
                        Border.all(color: AppColors.greyBorderColor, width: 1),
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Stack(
                        children: [
                          ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: CommonImage(
                                imageSource: step.mediaUrl,
                                placeholder:
                                    AssetsManager.directions_place_holder,
                                height: 80,
                                width: 107,
                                fit: BoxFit.cover,
                              )),
                        ],
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 12.0, right: 60.0, top: 12.0, bottom: 12.0),
                          child: CustomText(
                            text: step.description ?? '',
                            color: AppColors.primaryLightTextColor,
                            size: 14,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Positioned(
                  top: 12,
                  left: 0,
                  child: SizedBox(
                    width: 48,
                    height: 36,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        SvgPicture.asset(
                          AssetsManager.steps,
                          width: 48,
                          height: 36,
                          fit: BoxFit.contain,
                        ),
                        CustomText(
                          text: "Step${index + 1}",
                          align: TextAlign.center,
                          color: context.theme.scaffoldBackgroundColor,
                          size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
                        ),
                      ],
                    ),
                  ),
                ),
                Visibility(
                  visible: false,
                  child: Positioned(
                    top: 8,
                    right: 8,
                    child: Row(
                      children: [
                        SizedBox(width: 10.w),
                        EditButton(
                          onPressed: () {
                            ref
                                .read(singleRecipeNotifierProvider.notifier)
                                .goToEditRecipeScreen(
                                    context,
                                    recipeId,
                                    cookbookId,
                                    recipesList,
                                    recipeDetail,
                                    categoriesList!,
                                    cuisinesList!,
                                    initialStep: 3); // Step 3 is Directions
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        )
      else
        CustomInfoCard(
            title: 'Looks like the cooking steps are missing.',
            subheading:
                'Add step-by-step instructions to complete your recipe.',
            actionText: 'Add Cooking Instructions',
            onActionPressed: () {
              ref
                  .read(singleRecipeNotifierProvider.notifier)
                  .goToEditRecipeScreen(
                      context,
                      recipeId,
                      cookbookId,
                      recipesList,
                      recipeDetail,
                      categoriesList!,
                      cuisinesList!);
            }),
    ],
  );
}
