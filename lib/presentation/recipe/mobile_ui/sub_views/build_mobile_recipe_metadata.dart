import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';

import '../../../../app/imports/core_imports.dart';
import '../../../../core/data/models/category_response.dart';
import '../../../../core/data/models/cuisines_response.dart';
import '../../../../core/data/models/recipe_detail_response.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../../app/assets_manager.dart';

Widget buildMobileRecipeMetadata(
    BuildContext context,
    RecipeDetails recipe,
    RecipeDetails recipeDetail,
    List<Categories>? categoriesList,
    List<Cuisines>? cuisinesList,
    int recipeId,
    int cookbookId,
    List<Recipe> recipesList,
    {required WidgetRef ref}) {
  final metadata = [
    {
      'icon': AssetsManager.time,
      'label': 'Prep Time',
      'value': recipe.prepTime ?? ''
    },
    {
      'icon': AssetsManager.time,
      'label': 'Cook Time',
      'value': recipe.cookTime ?? ''
    },
    {
      'icon': AssetsManager.time,
      'label': 'Total Time',
      'value': recipe.totalTime ?? ''
    },
    {
      'icon': AssetsManager.group,
      'label': 'Category',
      'value': recipe.category ?? ''
    },
    {
      'icon': AssetsManager.cuisine,
      'label': 'Cuisine',
      'value': recipe.cuisine ?? ''
    },
    {
      'icon': AssetsManager.yield,
      'label': 'Yield',
      'value': recipe.yieldUnit != null && recipe.yield != null
          ? '${recipe.yield.toString()} ${recipe.yieldUnit.toString()}'
          : ""
    },
    {
      'icon': AssetsManager.serving,
      'label': 'Servings',
      'value': recipe.servings != null ? recipe.servings.toString() : ''
    },
  ];

  return SizedBox(
    height: 50, // Adjust based on your card height
    child: ListView.separated(
      scrollDirection: Axis.horizontal,
      itemCount: metadata.length,
      padding: EdgeInsets.only(left: 16, right: 16),
      separatorBuilder: (context, index) => SizedBox(width: 10),
      itemBuilder: (context, index) {
        final item = metadata[index];
        return Container(
          padding: EdgeInsets.only(left: 20, right: 10),
          decoration: BoxDecoration(
            color: AppColors.greyCardColor,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(item['icon'] as String),
                SizedBox(width: 10),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      text: item['label'] as String,
                      color: AppColors.primaryGreyColor,
                      weight: FontWeight.w700,
                      size: 12,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 5.h),
                    CustomText(
                      text: item['value'] as String,
                      color: AppColors.textGreyColor,
                      weight: FontWeight.w400,
                      size: 12,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    ),
  );
}
