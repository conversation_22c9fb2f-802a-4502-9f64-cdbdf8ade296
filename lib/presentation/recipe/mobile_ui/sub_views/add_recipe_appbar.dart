import '../../../../app/imports/packages_imports.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/providers/recipe/step_Provider.dart';
import '../../../../core/widgets/custom_text.dart';

class AddRecipeAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String title;
  final int totalStep;

  final VoidCallback onBack;

  const AddRecipeAppBar({
    required this.title,
    required this.totalStep,
    required this.onBack,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stepState = ref.watch(stepProvider);

    return AppBar(
      automaticallyImplyLeading: false,
      backgroundColor: Colors.white,
      elevation: 1,
      titleSpacing: 0,
      title: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              color: Colors.black,
              onPressed: onBack,
            ),
            const SizedBox(width: 4),
            CustomText(
              text: title,
              color: AppColors.primaryColor,
              size: 16,
              weight: FontWeight.w500,
            ),
            const Spacer(),
            CustomText(
              text: '${stepState.current}/${totalStep}',
              color: AppColors.textGreyColor,
              size: 16,
              weight: FontWeight.w500,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
