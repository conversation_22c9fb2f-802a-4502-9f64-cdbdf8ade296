import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text.dart';

class AddRecipeSuccessBottomSheet extends HookConsumerWidget {
  final bool callFromUpdate;
  final bool isScrollable;
  final VoidCallback? onSuccess;
  final ScrollController scrollController;

  const AddRecipeSuccessBottomSheet({
    super.key,
    this.callFromUpdate = false,
    this.isScrollable = false,
    this.onSuccess,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(26)),
      ),
      padding: EdgeInsets.only(
        left: 25,
        right: 25,
        top: 10, // Reduced top padding to accommodate drag handle
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
// Drag handle
          Container(
            margin: const EdgeInsets.only(top: 10.0),
            width: 40.0,
            height: 5.0,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.5),
            ),
          ),
          Expanded(
              child: SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 16),
                // _closeButton(context, ref),
                SvgPicture.asset(AssetsManager.success, height: 80, width: 80),
                const SizedBox(height: 30),
                CustomText(
                  text: 'Recipe added successfully',
                  color: AppColors.primaryGreyColor,
                  size: 14,
                  weight: FontWeight.w400,
                  align: TextAlign.center,
                ),
                const SizedBox(height: 30),
                CustomButton(
                  width: 250,
                  fontSize: 14,
                  borderRadius: 10,
                  weight: FontWeight.w600,
                  //  isLoading: ref.watch(cookbookNotifierProvider).status == AppStatus.loading,
                  text: 'Go to Recipe',
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (onSuccess != null) {
                      onSuccess!();
                    }
                  },
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }
}
