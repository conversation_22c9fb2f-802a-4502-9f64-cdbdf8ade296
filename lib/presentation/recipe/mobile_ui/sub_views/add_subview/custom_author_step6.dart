import 'dart:io';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/theme/colors.dart';

import '../../../../../app/imports/packages_imports.dart';
import '../../../../../core/helpers/media_picker_service.dart';
import '../../../../../core/providers/recipe/author_provider.dart';
import '../../../../../core/providers/recipe/step_Provider.dart';
import '../../../../../core/widgets/circular_image.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_input_field.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../../../core/widgets/image_cropper/image_cropper.dart';

class CustomAuthorStep6 extends ConsumerStatefulWidget {
  bool? isCallFromEdit;
  VoidCallback? onUpdateData;

  CustomAuthorStep6({
    super.key,
    this.isCallFromEdit = false,
    this.onUpdateData,
  });

  @override
  ConsumerState<CustomAuthorStep6> createState() => _CustomAuthorWidgetState();
}

class _CustomAuthorWidgetState extends ConsumerState<CustomAuthorStep6> {
  late TextEditingController sourceController;
  late TextEditingController copyrightController;
  late TextEditingController authorNameController;

  File? authorImage;

  @override
  void initState() {
    super.initState();

    // Initialize controllers first
    authorNameController = TextEditingController();
    sourceController = TextEditingController();
    copyrightController = TextEditingController();

    // Use post frame callback to read provider after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final metadata = ref.read(authorProvider);

        // Update controllers with provider data
        authorNameController.text = metadata.authorName ?? '';
        sourceController.text = metadata.source ?? '';
        copyrightController.text = metadata.copyright ?? '';

        // Update local image state
        if (metadata.image != null) {
          setState(() {
            authorImage = metadata.image;
          });
        }
      }
    });
  }

  void _pickAuthorImage() async {
    final file = await MediaPickerService.pickSingleImage();
    if (file != null && mounted) {
      try {
        // Use delegate pattern to avoid navigation issues
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ImageCropper(
              pickedImage: file,
              showCropPresets: true,
              showGridLines: false,
              enableFreeformCrop: true,
              useDelegate: true,
              // Use delegate pattern
              onImageCropped: (File? croppedImageFile) async {
                if (croppedImageFile != null && mounted) {
                  try {
                    // Update both local state and provider
                    if (mounted) {
                      setState(() {
                        authorImage = croppedImageFile;
                      });

                      // Update provider
                      ref
                          .read(authorProvider.notifier)
                          .updateImage(croppedImageFile);
                    }
                  } catch (e) {
                    print("Error processing cropped image: $e");
                  }
                } else {
                  print("Cropped image is null or user canceled.");
                }
              },
            ),
          ),
        );
      } catch (e) {
        print("Error in image cropping navigation: $e");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authorState = ref.watch(authorProvider);

    // Sync local state with provider state
    if (authorState.image != null && authorState.image != authorImage) {
      // Provider has a new image, update local state
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            authorImage = authorState.image;
          });
        }
      });
    } else if (authorState.image == null && authorImage != null) {
      // Provider state was reset, clear local state
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            authorImage = null;
          });
        }
      });
    }

    // return Scaffold(
    //   body: Padding(
    //     padding: const EdgeInsets.all(16.0),
    //     child: Column(
    //       crossAxisAlignment: CrossAxisAlignment.start,
    //       children: [
    //         CustomText(
    //             text: 'Author info',
    //             size: 14,
    //             weight: FontWeight.w500,
    //             color: AppColors.primaryGreyColor),
    //         SizedBox(
    //           height: 10,
    //         ),
    //         Center(
    //           child: CircularImage(
    //             imageFile: authorState.image ?? authorImage,
    //             // Use provider state first, fallback to local state
    //             imageUrl: '',
    //             // Fallback to network URL if provided
    //             placeholderAsset: AssetsManager.profile_placeholder,
    //             // Replace with your asset path
    //             radius: 400.0,
    //             // Reduced radius for better mobile display
    //             onTap: () => _pickAuthorImage(),
    //             borderColor: Colors.grey.shade300,
    //             placeholderText: 'Add Image',
    //           ),
    //         ),
    //         SizedBox(
    //           height: 20,
    //         ),

    //         IngredientTextField(
    //           hintText: "Author",
    //           height: 70.h,
    //           controller: authorNameController,
    //           //  height: 70.h,
    //           onChanged: (value) =>
    //               ref.read(authorProvider.notifier).updateAuthorName(value),
    //         ),
    //         SizedBox(height: 10),
    //         IngredientTextField(
    //           hintText: "Source",
    //           height: 70.h,
    //           controller: sourceController,
    //           onChanged: (value) =>
    //               ref.read(authorProvider.notifier).updateSource(value),
    //         ),
    //         SizedBox(height: 10),
    //         IngredientTextField(
    //           hintText: "Copyright",
    //           controller: copyrightController,
    //           height: 70.h,
    //           onChanged: (value) =>
    //               ref.read(authorProvider.notifier).updateCopyright(value),
    //         ),

    //         SizedBox(height: 12),
    //         // CustomButton(text: "Save changes", onPressed: (){} , width: 230.w, fontSize: 20.sp,),
    //         Visibility(
    //           visible: widget.isCallFromEdit ?? false,
    //           child: Padding(
    //             padding: const EdgeInsets.symmetric(horizontal: 50.0),
    //             child: CustomButton(
    //               text: "Save Changes",
    //               fontSize: 14,
    //               onPressed: () {
    //                 // Ensure ingredients are updated before notifying parent
    //                 widget.onUpdateData?.call();
    //               },
    //             ),
    //           ),
    //         ),
    //       ],
    //     ),
    //   ),
    //   bottomNavigationBar: SafeArea(
    //     child: Padding(
    //       padding: const EdgeInsets.all(16.0),
    //       child: CustomButton(
    //         borderRadius: 10,
    //         text: 'Next',
    //         onPressed: () {
    //           ref.read(stepProvider.notifier).nextStep();
    //         },
    //       ),
    //     ),
    //   ),
    // );
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
                text: 'Author info',
                size: 14,
                weight: FontWeight.w500,
                color: AppColors.primaryGreyColor),
            SizedBox(
              height: 10,
            ),
            Center(
              child: CircularImage(
                imageFile: authorState.image ?? authorImage,
                // Use provider state first, fallback to local state
                imageUrl: '',
                // Fallback to network URL if provided
                placeholderAsset: AssetsManager.profile_placeholder,
                // Replace with your asset path
                radius: 380.0,
                // Reduced radius for better mobile display
                onTap: () => _pickAuthorImage(),
                borderColor: Colors.grey.shade300,
                placeholderText: 'Add Image',
              ),
            ),
            SizedBox(
              height: 16,
            ),

            CustomInputField(
              hintText: "Author",
             // height: 70.h,
              controller: authorNameController,
              //  height: 70.h,
              onChanged: (value) =>
                  ref.read(authorProvider.notifier).updateAuthorName(value),
            ),
            SizedBox(height: 10),
            CustomInputField(
              hintText: "Source",
           //   height: 70.h,
              controller: sourceController,
              onChanged: (value) =>
                  ref.read(authorProvider.notifier).updateSource(value),
            ),
            SizedBox(height: 10),
            CustomInputField(
              hintText: "Copyright",
              controller: copyrightController,
             // height: 70.h,
              onChanged: (value) =>
                  ref.read(authorProvider.notifier).updateCopyright(value),
            ),

            SizedBox(height: 12),
            // CustomButton(text: "Save changes", onPressed: (){} , width: 230.w, fontSize: 20.sp,),
            Visibility(
              visible: widget.isCallFromEdit ?? false,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 50.0),
                child: CustomButton(
                  text: "Save Changes",
                  fontSize: 14,
                  onPressed: () {
                    // Ensure ingredients are updated before notifying parent
                    widget.onUpdateData?.call();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20),
          child: CustomButton(
            borderRadius: 10,
            text: 'Next',
            onPressed: () {
              ref.read(stepProvider.notifier).nextStep();
            },
          ),
        ),
      ),
    );
  }
}
