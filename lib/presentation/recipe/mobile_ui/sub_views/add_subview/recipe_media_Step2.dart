import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/providers/recipe/step_Provider.dart';
import '../../../../../app/imports/core_imports.dart';
import '../../../../../app/theme/colors.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../subview/media_picker_grid.dart';

Widget recipeMediaStep2({
  required BuildContext context,
  required StepNotifier stepNotifier,
  required List<RecipeMedia> recipeMedia,
  required bool isCallFromEdit,
  required String? recipeThumbnailFileUrl,
}) {
  return Scaffold(
    body: Padding(
      padding: const EdgeInsets.only(bottom: 70),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: 'Add Photo/Video',
                size: 14,
                weight: FontWeight.w500,
                color: AppColors.primaryGreyColor,
              ),
              SizedBox(height: 16.h),
              MediaPickerGrid(
                recipeMedia: recipeMedia,
                recipeThumbnailFileUrl: recipeThumbnailFileUrl,
                isCallFromEdit: isCallFromEdit,
                onUpdateData: () {
                  // Optional: Handle save logic if needed
                },
              ),
            ],
          ),
        ),
      ),
    ),
    bottomNavigationBar: SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(  16.0 ),
        child: CustomButton(
          borderRadius: 10,
          text: 'Next',
          onPressed: () {
            stepNotifier.nextStep();
          },
        ),
      ),
    ),
  );
}
