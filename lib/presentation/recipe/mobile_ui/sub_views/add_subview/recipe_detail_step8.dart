import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/providers/recipe/step_Provider.dart';

import '../../../../../app/imports/core_imports.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../subview/custom_serving_ideas_widget.dart';
import '../../../subview/custom_wine_widgets.dart';

Widget recipeDetailStep8(
    {required BuildContext context,
    required StepNotifier stepNotifier,
    required VoidCallback onSaveRecipe}) {
  return Scaffold(
    // Optional if you want safe area and scaffold benefits
    body: Padding(
      padding: const EdgeInsets.only(bottom: 70), // Leave space for button
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomServingWidget(
                isCallFromAdd: true,
              ),
              <PERSON><PERSON><PERSON><PERSON>(height: 40.h),
              CustomWineWidget(
                isCallFromAdd: true,
              ),
            ],
          ),
        ),
      ),
    ),
    bottomNavigationBar: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 35),
      child: CustomButton(
          borderRadius: 10, text: 'Complete', onPressed: onSaveRecipe),
    ),
  );
}
