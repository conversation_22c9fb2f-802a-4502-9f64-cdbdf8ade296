import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/providers/recipe/step_Provider.dart';
import '../../../../../app/imports/core_imports.dart';
import '../../../../../app/theme/colors.dart';
import '../../../../../core/providers/recipe/media_provider.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../subview/media_picker_grid.dart';

Widget editRecipeMediaStep2(
    {required List<RecipeMedia> recipeMedia,
    required recipeThumbnailFileUrl,
    required VoidCallback? onUpdateData,
    required WidgetRef ref,
    int? coverMediaIndex}) {
  return Scaffold(
    body: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: 'Update Photo/Video',
            size: 14,
            weight: FontWeight.w500,
            color: AppColors.primaryGreyColor,
          ),
          SizedBox(
            height: 12,
          ),
          MediaPickerGrid(
            recipeMedia: recipeMedia,
            recipeThumbnailFileUrl: recipeThumbnailFileUrl,
            isCallFromEdit: true,
            onUpdateData: onUpdateData,
            coverMediaIndex: coverMediaIndex,
          ),
        ],
      ),
    ),
    bottomNavigationBar: SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(  16.0 ),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: CustomButton(
                  borderRadius: 10,
                  text: 'Next',
                  onPressed: () {
                    ref.read(stepProvider.notifier).nextStep();
                  }),
            ),
            SizedBox(
              width: 16,
            ),
            Expanded(
                flex: 1,
                child: CustomButton(
                    borderRadius: 10, text: 'Save', onPressed: onUpdateData!)),
          ],
        ),
      ),
    ),
  );
}
