import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/recipe/detail_subview/recipe_nutrition_facts_dialog.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/build_mobile_recipe_metadata.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../core/data/models/recipe_detail_response.dart';
import '../../../../core/providers/categories_notifier.dart';
import '../../../../core/providers/cuisines_notifier.dart';
import '../../../../core/providers/nutrions_notifier.dart';
import '../../../../core/utils/Utils.dart';
import '../../../../core/widgets/common_image.dart';
import '../../../../core/widgets/expandable_desc_text.dart';
import '../../../../app/assets_manager.dart';
import '../../../../core/widgets/video_player_widget.dart';
import '../../detail_subview/build_similar_recipes_list.dart';

class RecipeDetailHeader extends ConsumerStatefulWidget {
  final RecipeDetails recipeDetail;
  final int recipeId;
  final int cookbookId;
  final List<Recipe> recipesList;
  final String? selectedMediaUrl;

  const RecipeDetailHeader(
    this.recipeId,
    this.cookbookId,
    this.recipesList, {
    super.key,
    required this.recipeDetail,
    required this.selectedMediaUrl,
  });

  @override
  ConsumerState<RecipeDetailHeader> createState() => _RecipeHeaderState();
}

class _RecipeHeaderState extends ConsumerState<RecipeDetailHeader> {
  var imageUrl = '';

  @override
  void initState() {
    imageUrl = widget.selectedMediaUrl ?? '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final nutritionDataState = ref.watch(nutritionInfoNotifierProvider);
    final categoriesState = ref.watch(categoriesNotifierProvider);
    final cuisinesState = ref.watch(cuisinesNotifierProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Stack(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: CustomText(
                      text: widget.recipeDetail.name ?? '',
                      size: 14,
                      weight: FontWeight.w500,
                      color: AppColors.primaryGreyColor,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Visibility(
                    visible: false,
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 12.w, vertical: 8.h),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: AppColors.primaryGreyColor,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                AssetsManager.share,
                                width: 20.w,
                                height: 20.h,
                                color: AppColors.primaryBorderColor,
                              ),
                              SizedBox(width: 8.w),
                              Text(
                                'Share',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppColors.primaryGreyColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 12.w, vertical: 8.h),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: AppColors.primaryGreyColor,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                AssetsManager.print,
                                width: 20.w,
                                height: 20.h,
                                color: AppColors.primaryBorderColor,
                              ),
                              SizedBox(width: 8.w),
                              Text(
                                'Print',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppColors.primaryGreyColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Visibility(
                visible: nutritionDataState.status == AppStatus.success &&
                    nutritionDataState.data?.nutrients != null &&
                    DeviceUtils().isTabletOrIpad(context),
                child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return RecipeNutritionFactsDialog(
                              recipeDetails: widget.recipeDetail,
                              nutritionData: nutritionDataState.data,
                            );
                          },
                        );
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(CupertinoIcons.heart_fill, size: 28),
                          CustomText(
                            text: 'Nutrition Facts',
                            weight: FontWeight.w500,
                            size: 14,
                            overflow: TextOverflow.ellipsis,
                          )
                        ],
                      ),
                    )),
              ),
            ],
          ),
        ),
        SizedBox(height: 8),
        Visibility(
          visible: widget.recipeDetail.description?.isNotEmpty ?? false,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: ExpandableDescText(
              desc: widget.recipeDetail.description ?? '',
              textColor: AppColors.primaryLightTextColor,
              size: 14,
            ),
          ),
        ),
    Visibility(
    visible: widget.recipeDetail.description?.isNotEmpty ?? false,
    child: SizedBox(height: 8)),
        buildMobileRecipeMetadata(
          context,
          widget.recipeDetail ?? RecipeDetails(),
          widget.recipeDetail ?? RecipeDetails(),
          categoriesState.data,
          cuisinesState.data,
          widget.recipeId,
          widget.cookbookId,
          widget.recipesList,
          ref: ref,
        ),
        SizedBox(
          height: 10,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Card(
            color: Colors.white,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: imageUrl.isNotEmpty && Utils().isVideo(imageUrl)
                  ? SizedBox(
                      height: 211,
                      width: MediaQuery.of(context).size.width,
                      child: AspectRatio(
                        aspectRatio: 16 / 9,
                        child: VideoPlayerWidget(
                          mediaUrl: imageUrl,
                          thumbnailPath:
                              widget.recipeDetail.recipeThumbnailFileUrl ?? '',
                          height: 211,
                        ),
                      ),
                    )
                  : CommonImage(
                      imageSource: imageUrl ?? '',
                      height: 211,
                      width: MediaQuery.of(context).size.width,
                      fit: BoxFit.cover,
                      placeholder: AssetsManager.recipe_place_holder,
                    ),
            ),
          ),
        ),
        SizedBox(height: 30.h),
        if (widget.recipeDetail.recipeMedia != null &&
            (widget.recipeDetail.recipeMedia?.isNotEmpty ?? false))
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: SimilarRecipesWidget(
                  similarRecipes: widget.recipeDetail.recipeMedia ?? [],
                  recipeThumbnailFileUrl:
                      widget.recipeDetail.recipeThumbnailFileUrl ?? '',
                  onImageSelected: (String newImageUrl) {
                    setState(() {
                      imageUrl = newImageUrl;
                      // widget.selectedMediaFile = null;
                    });
                  },
                ),
              ),

            ],
          ),
      ],
    );
  }
}
