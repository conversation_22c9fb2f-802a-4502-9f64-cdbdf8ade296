import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

// Change the provider to store the tab name instead of index
final selectedTabProvider = StateProvider<String>((ref) => 'Ingredients');

class RecipeItemTabbar extends HookConsumerWidget {
  final List<String> baseTabs = [
    'Ingredients',
    'Directions',
    'Nutrition Analysis',
    'Notes',
    'Author Info',
    'Other'
  ];

  RecipeItemTabbar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTab = ref.watch(selectedTabProvider);
    final tabController = ref.read(selectedTabProvider.notifier);

    // Always show all tabs including nutrition analysis
    final List<String> tabs = [...baseTabs];

    // Keep the tab visible when there's no data so users can see the "No Nutritions Available" message
    // The nutrition section will handle displaying the appropriate message
    // No need to remove the tab anymore

    // Initialize ScrollController
    final scrollController = ScrollController();

    // Create a list of GlobalKeys for each tab to measure their width
    final List<GlobalKey> tabKeys = List.generate(tabs.length, (_) => GlobalKey());

    // Update scroll position when tab changes
    ref.listen(selectedTabProvider, (previous, next) {
      if (next != previous) {
        final tabIndex = tabs.indexOf(next);
        if (tabIndex != -1) {
          // Calculate the scroll position based on actual tab widths
          double scrollPosition = 0.0;
          for (int i = 0; i < tabIndex; i++) {
            final renderBox = tabKeys[i].currentContext?.findRenderObject() as RenderBox?;
            scrollPosition += renderBox?.size.width ?? 0.0;
          }

          // Get the width of the selected tab
          final selectedTabRenderBox = tabKeys[tabIndex].currentContext?.findRenderObject() as RenderBox?;
          final selectedTabWidth = selectedTabRenderBox?.size.width ?? 0.0;

          // Get the viewport width
          final viewportWidth = MediaQuery.of(context).size.width;

          // Adjust scroll position to keep the selected tab fully visible
          final maxScrollExtent = scrollController.position.maxScrollExtent;
          final desiredScrollPosition = scrollPosition + selectedTabWidth / 2 - viewportWidth / 2;

          // Ensure the scroll position stays within bounds
          final clampedScrollPosition = desiredScrollPosition.clamp(0.0, maxScrollExtent);

          scrollController.animateTo(
            clampedScrollPosition,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });

    return SingleChildScrollView(
      controller: scrollController,
      scrollDirection: Axis.horizontal,
      child: Container(
        margin: const EdgeInsets.only(left: 16, right: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: tabs.asMap().entries.map((entry) {
            final index = entry.key;
            final tab = entry.value;
            return Padding(
              padding: const EdgeInsets.only(right: 4.0),
              child: GestureDetector(
                onTap: () {
                  tabController.state = tab;
                },
                child: Card(
                  key: tabKeys[index], // Assign GlobalKey to the Card
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 8.0, horizontal: 16.0),
                    decoration: BoxDecoration(
                      color: selectedTab == tab ? Colors.red : Colors.white,
                      borderRadius: BorderRadius.circular(8.0),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withAlpha(100),
                          spreadRadius: 1,
                          blurRadius: 1,
                          offset: const Offset(-1, 1),
                        ),
                        BoxShadow(
                          color: Colors.blue.withAlpha(100),
                          spreadRadius: 1,
                          blurRadius: 1,
                          offset: const Offset(1, 1),
                        ),
                      ],
                    ),
                    child: CustomText(
                      text: tab,
                      color: selectedTab == tab ? Colors.white : Colors.red,
                      size: 12.0,
                      weight: selectedTab == tab
                          ? FontWeight.w600
                          : FontWeight.w400,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}