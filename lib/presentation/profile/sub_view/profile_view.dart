import 'dart:io';
import 'package:country_pickers/utils/utils.dart';
import 'package:country_pickers/country.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/user_profile_response.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/data/request_query/update_profile_request.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/utils/validator.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:mastercookai/core/widgets/contact_text_field.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/core/widgets/image_cropper/image_cropper.dart';
import 'package:mastercookai/core/widgets/simple_dropdown.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';

import '../../../app/assets_manager.dart';
import '../../../app/theme/colors.dart';
import '../../../core/providers/profile/user_types_notifier.dart';

class ProfileView extends ConsumerStatefulWidget {
  const ProfileView({
    super.key,
  });

  @override
  ConsumerState<ProfileView> createState() => _ProfileViewState();
}

class _ProfileViewState extends ConsumerState<ProfileView>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController contactController = TextEditingController();
  final TextEditingController companyNameController = TextEditingController();
  final TextEditingController typesOfUsersController = TextEditingController();
  final TextEditingController dateOfBirthController = TextEditingController();

  // State variables
  int selectedGender = 3; // 0 for Male, 1 for Female
  String? selectedUserType;
  int? selectedUserTypeId;
  File? selectedProfileImage;
  String? selectedDay;
  String? selectedMonth;
  String? selectedYear;
  Country? selectedCountry;
  bool _isFieldsPopulated = false;

  final _formKey = GlobalKey<FormState>();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userProfileState = ref.read(userProfileNotifierProvider);
      if (userProfileState.data?.userProfile != null) {
        _populateFormFields(userProfileState.data!.userProfile!);
      }
    });
    super.initState();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
  }

  @override
  void didUpdateWidget(covariant ProfileView oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    // Dispose controllers to prevent memory leaks
    firstNameController.dispose();
    lastNameController.dispose();
    emailController.dispose();
    contactController.dispose();
    companyNameController.dispose();
    typesOfUsersController.dispose();
    dateOfBirthController.dispose();
    super.dispose();
  }

  // Helper method to detect country from phone number
  // void _detectCountryFromPhone(String phoneNumber) {
  //   final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
  //   setState(() {
  //     if (cleanPhone.startsWith('1') && cleanPhone.length == 11) {
  //       selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
  //     } else if (cleanPhone.startsWith('44')) {
  //       selectedCountry = CountryPickerUtils.getCountryByIsoCode('GB');
  //     } else if (cleanPhone.startsWith('91')) {
  //       selectedCountry = CountryPickerUtils.getCountryByIsoCode('IN');
  //     } else if (cleanPhone.length == 10) {
  //       selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
  //     } else {
  //       selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
  //     }
  //   });
  // }

  void _detectCountryFromCode(String code) {
  final cleanCode = code.replaceAll('+', '');
  
  setState(() {
    try {
      selectedCountry = CountryPickerUtils.getCountryByPhoneCode(cleanCode);
    } catch (e) {
      debugPrint('Invalid country code: $code');
      selectedCountry = CountryPickerUtils.getCountryByIsoCode('US'); // fallback
    }
  });
}


  // Helper method to populate form fields with user profile data
  void _populateFormFields(UserProfile userProfile) {
    // Only populate if fields haven't been populated yet to avoid cursor jumping
    if (_isFieldsPopulated) return;

    final nameParts = userProfile.name.split(' ');
    final firstName = nameParts.isNotEmpty ? nameParts.first : '';
    final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

    firstNameController.text = firstName;
    lastNameController.text = lastName;
    emailController.text = userProfile.email;
    contactController.text = userProfile.phone;
    companyNameController.text = userProfile.companyName ?? '';
    typesOfUsersController.text = userProfile.userType ?? '';
    selectedUserType = userProfile.userType;
    selectedUserTypeId = userProfile.userTypeId;

    // Parse DOB
    final dobComponents = Utils().extractDobComponents(userProfile.dob);
    if (dobComponents != null) {
      setState(() {
        selectedDay = dobComponents['day']?.toString().padLeft(2, '0');
        selectedMonth = dobComponents['month'] as String?;
        selectedYear = dobComponents['year']?.toString();
        dateOfBirthController.text =
            userProfile.dob; // Keep in YYYY-MM-DD format
      });
    }

    _detectCountryFromCode(userProfile.countryCode);

    setState(() {
      selectedGender = userProfile.gender.toLowerCase() == 'male'
          ? 1
          : userProfile.gender.toLowerCase() == 'female'
              ? 2
              : userProfile.gender.toLowerCase() == 'other'
                  ? 0
                  : 3;
      _isFieldsPopulated = true;
    });
  }

  // Method to update profile with personal details and image
  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      // Validate required fields
      if (firstNameController.text.trim().isEmpty) {
        Utils().showFlushbar(context,
            message: 'Please enter your first name', isError: true);
        return;
      }
      if (contactController.text.trim().isEmpty) {
        Utils().showFlushbar(context,
            message: 'Please enter your contact number', isError: true);
        return;
      }
      if (dateOfBirthController.text.trim().isEmpty) {
        Utils().showFlushbar(context,
            message: 'Please enter your date of birth', isError: true);
        return;
      }
      if (selectedUserTypeId == null) {
        Utils().showFlushbar(context,
            message: 'Please select a user type', isError: true);
        return;
      }

      // Create update profile request
      final request = UpdateProfileRequest(
        firstName: firstNameController.text.trim(),
        lastName: lastNameController.text.trim().isNotEmpty
            ? lastNameController.text.trim()
            : null,
        gender: selectedGender == 1 ? 'Male' :selectedGender == 2? 'Female':selectedGender == 0?'Other':'',
        dob: dateOfBirthController.text.trim(),
        countryCode: '+${selectedCountry?.phoneCode ?? '1'}',
        contact: contactController.text.trim(),
        companyName: companyNameController.text.trim().isNotEmpty
            ? companyNameController.text.trim()
            : null,
        userTypeId: selectedUserTypeId!,
        profilePic: selectedProfileImage,
      );

      // Call update profile API
      final success = await ref
          .read(userProfileNotifierProvider.notifier)
          .updateProfile(context: context, request: request);

      if (success && context.mounted) {
        // Fetch updated profile data
        await ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
        // Clear selected image
        setState(() {
          selectedProfileImage = null;
        });
      }
    } catch (e) {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Failed to update profile: $e', isError: true);
      }
    }
  }

  // Method to pick profile image
  void _pickProfileImage() async {
    final file = await MediaPickerService.pickSingleImage();
    if (file != null && mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ImageCropper(
            pickedImage: file,
            showCropPresets: true,
            showGridLines: true,
            useDelegate: true,
            enableFreeformCrop: true,
            onImageCropped: (File? croppedImageFile) {
              if (croppedImageFile != null) {
                setState(() {
                  selectedProfileImage = croppedImageFile;
                });
              }
            },
          ),
        ),
      );
    }
  }

  // Helper method to update the date controller when dropdowns change
  void _updateDateController() {
    if (selectedDay != null && selectedMonth != null && selectedYear != null) {
      // Convert short month name to number
      const monthNames = {
        'Jan': '01',
        'Feb': '02',
        'Mar': '03',
        'Apr': '04',
        'May': '05',
        'Jun': '06',
        'Jul': '07',
        'Aug': '08',
        'Sep': '09',
        'Oct': '10',
        'Nov': '11',
        'Dec': '12',
      };
      final monthNumber = monthNames[selectedMonth] ?? '01';
      // Format as YYYY-MM-DD for backend compatibility
      dateOfBirthController.text =
          '$selectedYear-$monthNumber-${selectedDay!.padLeft(2, '0')}';
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final isTablet = DeviceUtils().isTabletOrIpad(context);
    final userProfileState = ref.watch(userProfileNotifierProvider);

    // Populate form fields when state changes to success (only once)
    if (!_isFieldsPopulated &&
        userProfileState.status == AppStatus.success &&
        userProfileState.data?.userProfile != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _populateFormFields(userProfileState.data!.userProfile!);
      });
    }

    return SingleChildScrollView(
      padding: EdgeInsets.only(
        top: isTablet ? 40.0 : 30.0,
        bottom: isTablet ? 120.0 : 120.0,
        right: isTablet ? 20 : 50,
        left: isTablet ? 20 : 50,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: isTablet ? 2 : 1,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 16 : 60,
                vertical: isTablet ? 16 : 30,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.blackColor.withValues(alpha: 0.1),
                    spreadRadius: 0,
                    blurRadius: 24,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      text: 'Personal Details:',
                      size: isTablet ? 16 : 16,
                      weight: FontWeight.w600,
                      color: AppColors.primaryLightTextColor,
                    ),
                    SizedBox(height: isTablet ? 30 : 20),
                    _buildTwoColumnRow('Gender', _buildGenderRadioButtons()),
                    SizedBox(height: isTablet ? 25 : 15),
                    _buildTwoColumnRow(
                      'First Name',
                      CustomInputField(
                        hintText: 'First Name',
                        controller: firstNameController,
                        validator: Validator.validateFName,
                        isUpdateMode: true,
                         verticalPadding: 12,
                      ),
                    ),
                    SizedBox(height: isTablet ? 25 : 15),
                    _buildTwoColumnRow(
                      'Last Name',
                      CustomInputField(
                        hintText: 'Last Name',
                        controller: lastNameController,
                        validator: Validator.validateLName,
                        isUpdateMode: true,
                         verticalPadding: 12,
                      ),
                    ),
                    SizedBox(height: isTablet ? 25 : 15),
                    _buildTwoColumnRow('Date of birth', _buildDateDropdowns()),
                    SizedBox(height: isTablet ? 25 : 15),
                    _buildTwoColumnRow(
                      'Email',
                      CustomInputField(
                        hintText: 'Email',
                        editable: false,
                        controller: emailController,
                        validator: Validator.validateEmail,
                        isUpdateMode: true,
                         verticalPadding: 12,
                      ),
                    ),
                    SizedBox(height: isTablet ? 25 : 15),
                    _buildTwoColumnRow(
                      'Contact',
                      ContactTextField(
                        controller: contactController,
                        selectedCountry: selectedCountry!,
                        onCountryChanged: (country) {
                          setState(() {
                            selectedCountry = country;
                          });
                        },
                        validator: Validator.validatePhoneNumber,
                        isUpdateMode: true,
                        onClear: () {
                          contactController.clear();
                        },
                        borderRadius: 8,

                      ),
                    ),
                    SizedBox(height: isTablet ? 40 : 30),
                    CustomText(
                      text: 'Company Details:',
                      size: isTablet ? 16 : 16,
                      weight: FontWeight.w600,
                      color: AppColors.primaryLightTextColor,
                    ),
                    SizedBox(height: isTablet ? 30 : 20),
                    _buildTwoColumnRow(
                      'Company Name',
                      CustomInputField(
                        hintText: 'Company Name',
                        controller: companyNameController,
                        keyboardType: TextInputType.text,
                         verticalPadding: 12,
                      ),
                    ),
                    SizedBox(height: isTablet ? 25 : 15),
                    _buildTwoColumnRow(
                        'Types of Users', _buildUserTypesDropdownField()),
                    SizedBox(height: isTablet ? 40 : 30),
                    Center(
                      child: CustomButton(
                        fontSize: isTablet ? 16 : 18,
                        width: isTablet ? 250 : 200,
                        text: 'Save Changes',
                        onPressed: _updateProfile,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(width: isTablet ? 16 : 30),
          // Conditional rendering for the image upload section
          if (!isTablet) // This condition ensures it's only visible on non-tablet (desktop)
            Expanded(
              flex: 1,
              child: Column(
                children: [
                  Container(
                    height: isTablet ? 220 : 400,
                    width: isTablet ? 450 : 400,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(25),
                      image: (selectedProfileImage?.path == null &&
                              (userProfileState
                                      .data?.userProfile?.profilePic?.isEmpty ??
                                  true))
                          ? const DecorationImage(
                              image: AssetImage(AssetsManager.uploadImageBg),
                              fit: BoxFit.fill,
                            )
                          : null,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.blackColor.withValues(alpha: 0.1),
                          spreadRadius: 0,
                          blurRadius: 24,
                          offset: const Offset(0, 0),
                        ),
                      ],
                    ),
                    child: IconButton(
                      hoverColor: Colors.transparent,
                      onPressed: _pickProfileImage,
                      icon: ClipRRect(
                        borderRadius: BorderRadius.circular(25),
                        child: CommonImage(
                          width: (selectedProfileImage == null &&
                                  (userProfileState.data?.userProfile
                                          ?.profilePic?.isEmpty ??
                                      true))
                              ? (isTablet ? 90 : 60)
                              : (isTablet ? 450 : 400),
                          height: (selectedProfileImage == null &&
                                  (userProfileState.data?.userProfile
                                          ?.profilePic?.isEmpty ??
                                      true))
                              ? (isTablet ? 90 : 60)
                              : (isTablet ? 450 : 400),
                          imageSource: selectedProfileImage?.path ??
                              userProfileState.data?.userProfile?.profilePic,
                          placeholder: AssetsManager.addProfile,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: isTablet ? 35 : 20),
                  CustomButton(
                      fontSize: isTablet ? 16 : 18,
                      width: 215,
                      text: (selectedProfileImage?.path == null &&
                              (userProfileState
                                      .data?.userProfile?.profilePic?.isEmpty ??
                                  true))
                          ? 'Upload profile Picture'
                          : selectedProfileImage?.path != null
                              ? "Save Changes"
                              : 'Change profile Picture',
                      onPressed: () {
                        if (selectedProfileImage?.path != null) {
                          _updateProfile();
                        } else {
                          _pickProfileImage();
                        }
                      }),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Two-column layout helper method
  Widget _buildTwoColumnRow(String label, Widget field) {
    final isTablet = DeviceUtils().isTabletOrIpad(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 1,
          child: Padding(
            padding: EdgeInsets.only(top: isTablet ? 15.0 : 12.0),
            child: CustomText(
              text: label,
              size: isTablet ? 14 : 14,
              weight: FontWeight.w400,
              color: AppColors.primaryLightTextColor,
            ),
          ),
        ),
        SizedBox(width: isTablet ? 10 : 20),
        Expanded(
          flex: 3,
          child: field,
        ),
      ],
    );
  }

  // Gender radio buttons
  Widget _buildGenderRadioButtons() {
    final isTablet = DeviceUtils().isTabletOrIpad(context);
    return Row(
      children: [
        Expanded(
          child: RadioListTile<int>(
            title: CustomText(
              text: 'Male',
              size: isTablet ? 20 : 16,
              weight: FontWeight.w400,
              color: AppColors.primaryLightTextColor,
            ),
            value: 1,
            groupValue: selectedGender,
            activeColor: Theme.of(context).primaryColor,
            contentPadding: EdgeInsets.zero,
            onChanged: (int? value) {
              setState(() {
                selectedGender = value!;
              });
            },
          ),
        ),
        Expanded(
          child: RadioListTile<int>(
            title: CustomText(
              text: 'Female',
              size: isTablet ? 20 : 16,
              weight: FontWeight.w400,
              color: AppColors.primaryLightTextColor,
            ),
            value: 2,
            groupValue: selectedGender,
            activeColor: Theme.of(context).primaryColor,
            contentPadding: EdgeInsets.zero,
            onChanged: (int? value) {
              setState(() {
                selectedGender = value!;
              });
            },
          ),
        ),
        Expanded(
          child: RadioListTile<int>(
            title: CustomText(
              text: 'Other',
              size: isTablet ? 20 : 16,
              weight: FontWeight.w400,
              color: AppColors.primaryLightTextColor,
            ),
            value: 0,
            groupValue: selectedGender,
            activeColor: Theme.of(context).primaryColor,
            contentPadding: EdgeInsets.zero,
            onChanged: (int? value) {
              setState(() {
                selectedGender = value!;
              });
            },
          ),
        ),
      ],
    );
  }

  // Date dropdowns
  Widget _buildDateDropdowns() {
    final isTablet = DeviceUtils().isTabletOrIpad(context);
    return SizedBox(
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: SimpleDropdown<String>(
              items: List.generate(
                  31, (index) => (index + 1).toString().padLeft(2, '0')),
              hintText: 'Day',
              selectedValue: selectedDay,
              displayText: (day) => day,
              onChanged: (value) {
                setState(() {
                  selectedDay = value;
                  _updateDateController();
                });
              },
              validator: (value) => value == null ? 'Select Day' : null,
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: SimpleDropdown<String>(
              items: [
                'Jan',
                'Feb',
                'Mar',
                'Apr',
                'May',
                'Jun',
                'Jul',
                'Aug',
                'Sep',
                'Oct',
                'Nov',
                'Dec'
              ],
              hintText: 'Month',
              selectedValue: selectedMonth,
              displayText: (month) => month,
              onChanged: (value) {
                setState(() {
                  selectedMonth = value;
                  _updateDateController();
                });
              },
              validator: (value) => value == null ? 'Select Month' : null,
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: SimpleDropdown<String>(
              items: List.generate(
                  100, (index) => (DateTime.now().year - index).toString()),
              hintText: 'Year',
              selectedValue: selectedYear,
              displayText: (year) => year,
              onChanged: (value) {
                setState(() {
                  selectedYear = value;
                  _updateDateController();
                });
              },
              validator: (value) => value == null ? 'Select Year' : null,
            ),
          ),
        ],
      ),
    );
  }

  // User types dropdown field
  Widget _buildUserTypesDropdownField() {
    final isTablet = DeviceUtils().isTabletOrIpad(context);
    return Consumer(
      builder: (context, ref, child) {
        final userTypesState = ref.watch(userTypesNotifierProvider);
        if (userTypesState.status == AppStatus.success &&
            userTypesState.data != null) {
          final uniqueUserTypes =
              userTypesState.data!.userTypes.toSet().toList();
          return SimpleDropdown<UserType>(
            items: uniqueUserTypes,
            hintText: 'Select user type',
            selectedValue: uniqueUserTypes.firstWhere(
              (type) => type.type == selectedUserType,
              orElse: () => UserType(id: 0, type: ''),
            ),
            displayText: (userType) => userType.type,
            onChanged: (value) {
              setState(() {
                selectedUserType = value!.type;
                selectedUserTypeId = value.id;
                typesOfUsersController.text = value.type;
              });
            },
            idSelector: (userType) => userType.id,
            validator: (value) =>
                value == null ? 'Please select a user type' : null,
          );
        } else {
          return Container(
            height: isTablet ? 45 : 40,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: CustomText(
                text: 'No user types available',
                size: isTablet ? 15 : 14,
                weight: FontWeight.w400,
                color: Colors.grey,
              ),
            ),
          );
        }
      },
    );
  }
}
