import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/providers/profile/user_profile_notifier.dart';
import '../../../core/network/app_status.dart';
import '../../../core/widgets/custom_text_button.dart';

class StorageView extends ConsumerWidget {
  const StorageView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding:
            EdgeInsets.only(top: 30.0, bottom: 30.0, right:DeviceUtils().isTabletOrIpad(context)?20: 50, left:DeviceUtils().isTabletOrIpad(context)?20: 50),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left side - Storage overview with circular progress
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(50),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.blackColor.withValues(alpha: 0.1),
                    spreadRadius: 0,
                    blurRadius: 24,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Circular progress indicator without center text
                  Consumer(
                    builder: (context, ref, child) {
                      final userProfileState =
                          ref.watch(userProfileNotifierProvider);

                      if (userProfileState.status == AppStatus.success &&
                          userProfileState.data?.userProfile != null) {
                        final userProfile = userProfileState.data!.userProfile!;
                        final progressValue = userProfile.storageUsedInMB /
                            userProfile.storageQuotaInMB;

                        return SizedBox(
                          width: 220,
                          height: 220,
                          child: Stack(
                            children: [
                              SizedBox(
                                width: 220,
                                height: 220,
                                child: CircularProgressIndicator(
                                  value: progressValue,
                                  strokeWidth: 30,
                                  backgroundColor: Colors.grey[200],
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Color(0xFF007AFF)),
                                  //strokeCap: StrokeCap.round,
                                ),
                              ),
                              // Align(
                              //   alignment: Alignment.center,
                              //   child: MouseRegion(
                              //     cursor: SystemMouseCursors.click,
                              //     child: CustomTextButton(
                              //       text: 'Get More Storage',
                              //       size: DeviceUtils().isTabletOrIpad(context)
                              //           ? 14
                              //           : responsiveFont(18).sp,
                              //       onPressed: () {},
                              //       underline: true,
                              //       hoverColor:
                              //           AppColors.darkPrimaryBackgroundColor,
                              //       color: AppColors.primaryLightTextColor,
                              //     ),
                              //   ),
                              // )
                            ],
                          ),
                        );
                      }

                      // Fallback to static data (using actual API values)
                      return SizedBox(
                        width: 220,
                        height: 220,
                        child: CircularProgressIndicator(
                          value: 137 / 5120, // 137MB of 5120MB = ~3%
                          strokeWidth: 30,
                          backgroundColor: Colors.grey[200],
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Color(0xFF007AFF)),
                          //strokeCap: StrokeCap.round,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 40),
                  // Dynamic storage text using API data
                  Consumer(
                    builder: (context, ref, child) {
                      final userProfileState =
                          ref.watch(userProfileNotifierProvider);

                      if (userProfileState.status == AppStatus.success &&
                          userProfileState.data?.userProfile != null) {
                        final userProfile = userProfileState.data!.userProfile!;

                        // Format used storage - show MB if less than 1GB, otherwise show GB
                        String usedDisplay;
                        if (userProfile.storageUsedInMB < 1024) {
                          usedDisplay = '${userProfile.storageUsedInMB}MB';
                        } else {
                          final usedGB = (userProfile.storageUsedInMB / 1024)
                              .toStringAsFixed(0);
                          usedDisplay = '${usedGB}GB';
                        }

                        final totalGB = (userProfile.storageQuotaInMB / 1024)
                            .toStringAsFixed(0);

                        return RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: usedDisplay,
                                style: TextStyle(
                                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : responsiveFont(26).sp,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF007AFF),
                                  fontFamily: 'inter',
                                ),
                              ),
                              TextSpan(
                                text: ' used of ',
                                style: TextStyle(
                                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : responsiveFont(26).sp,
                                  color: Colors.grey[600],
                                  fontFamily: 'inter',
                                ),
                              ),
                              TextSpan(
                                text: '${totalGB}GB',
                                style: TextStyle(
                                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : responsiveFont(26).sp,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                  fontFamily: 'inter',
                                ),
                              ),
                            ],
                          ),
                        );
                      }

                      // Fallback to static data (using actual API values)
                      return RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: '0MB',
                              style: TextStyle(
                                fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : responsiveFont(26).sp,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF007AFF),
                                fontFamily: 'inter',
                              ),
                            ),
                            TextSpan(
                              text: ' used of ',
                              style: TextStyle(
                                fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : responsiveFont(26).sp,
                                color: Colors.grey[600],
                                fontFamily: 'inter',
                              ),
                            ),
                            TextSpan(
                              text: '5GB',
                              style: TextStyle(
                                fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : responsiveFont(26).sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                                fontFamily: 'inter',
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 15),
                  // Cloud icon with storage text
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        AssetsManager.cloud,
                        color: Color(0xFF007AFF),
                        width: 18,
                        height: 18,
                      ),
                      const SizedBox(width: 5),
                      Consumer(
                        builder: (context, ref, child) {
                          final userProfileState =
                              ref.watch(userProfileNotifierProvider);

                          if (userProfileState.status == AppStatus.success &&
                              userProfileState.data?.userProfile != null) {
                            final userProfile =
                                userProfileState.data!.userProfile!;
                            final percentage = ((userProfile.storageUsedInMB /
                                        userProfile.storageQuotaInMB) *
                                    100)
                                .toStringAsFixed(0);

                            return Text('Storage($percentage% Full)',
                                style: TextStyle(
                                    fontSize: DeviceUtils().isTabletOrIpad(context) ? 10 : responsiveFont(18).sp,
                                    color: Colors.grey[600],
                                    fontFamily: 'inter'));
                          }

                          // Fallback to static data (using actual API values)
                          return Text('Storage(0% Full)',
                              style: TextStyle(
                                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 10 : responsiveFont(18).sp,
                                  color: Colors.grey[600],
                                  fontFamily: 'inter'));
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 25),
                  // Storage breakdown legend with labels
                  Consumer(
                    builder: (context, ref, child) {
                      final userProfileState =
                          ref.watch(userProfileNotifierProvider);

                      if (userProfileState.status == AppStatus.success &&
                          userProfileState.data?.userProfile != null) {
                        final userProfile = userProfileState.data!.userProfile!;

                        // Format used storage - show MB if less than 1GB, otherwise show GB
                        String usedDisplay;
                        if (userProfile.storageUsedInMB < 1024) {
                          usedDisplay = '${userProfile.storageUsedInMB}MB';
                        } else {
                          final usedGB = (userProfile.storageUsedInMB / 1024)
                              .toStringAsFixed(0);
                          usedDisplay = '${usedGB}GB';
                        }

                        final totalGB = (userProfile.storageQuotaInMB / 1024)
                            .toStringAsFixed(0);
                        final remainingGB = ((userProfile.storageQuotaInMB -
                                    userProfile.storageUsedInMB) /
                                1024)
                            .toStringAsFixed(0);

                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildStorageLegendItemWithLabel(
                                Colors.red, '${totalGB}GB', 'Total', context),
                            _buildStorageLegendItemWithLabel(
                                Color(0xFF007AFF), usedDisplay, 'Used', context),
                            _buildStorageLegendItemWithLabel(Colors.grey[400]!,
                                '${remainingGB}GB', 'Remaining', context),
                          ],
                        );
                      }

                      // Fallback to static data (using actual API values)
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildStorageLegendItemWithLabel(
                              Colors.red, '0GB', 'Total', context),
                          _buildStorageLegendItemWithLabel(
                              Color(0xFF007AFF), '0MB', 'Used', context),
                          _buildStorageLegendItemWithLabel(
                              Colors.grey[400]!, '0GB', 'Remaining', context),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 30),
          // Right side - Storage breakdown by category
          Expanded(
            flex: DeviceUtils().isTabletOrIpad(context) ? 1 : 2,
            child: Container(
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.blackColor.withValues(alpha: 0.1),
                    spreadRadius: 0,
                    blurRadius: 24,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Consumer(
                builder: (context, ref, child) {
                  final userProfileState =
                      ref.watch(userProfileNotifierProvider);

                  if (userProfileState.status == AppStatus.success &&
                      userProfileState.data?.userProfile != null) {
                    final userProfile = userProfileState.data!.userProfile!;

                    // For now, use static data with dynamic values from API
                    // TODO: Uncomment when storageCategories field is available
                    // final storageCategories = userProfile.storageCategories;

                    // Static categories with API data from the console log
                    final staticCategories = [
                      {
                        'name': 'Cookbook',
                        'icon': AssetsManager.profileCookbook,
                        'color': Colors.green,
                        'usageMB': 2,
                        'storageMB': 500
                      },
                      {
                        'name': 'Recipe',
                        'icon': AssetsManager.profileReciepe,
                        'color': Colors.orange,
                        'usageMB': 17,
                        'storageMB': 300
                      },
                      {
                        'name': 'Meal Plan',
                        'icon': AssetsManager.profileMealplan,
                        'color': context.theme.primaryColor,
                        'usageMB': 0,
                        'storageMB': 200
                      },
                      {
                        'name': 'Videos',
                        'icon': AssetsManager.profileVideos,
                        'color': Colors.purple,
                        'usageMB': 117,
                        'storageMB': 500
                      },
                      {
                        'name': 'Images',
                        'icon': AssetsManager.profileImages,
                        'color': Colors.blue,
                        'usageMB': 10,
                        'storageMB': 500
                      },
                    ];

                    return Column(
                      children: staticCategories.asMap().entries.map((entry) {
                        final index = entry.key;
                        final category = entry.value;
                        final usageMB = category['usageMB'] as int;
                        final storageMB = category['storageMB'] as int;

                        return Column(
                          children: [
                            if (index > 0) const SizedBox(height: 30),
                            _buildStorageItem(
                                category['name'] as String,
                                category['icon'] as String,
                                usageMB,
                                storageMB,
                                category['color'] as Color, context),
                          ],
                        );
                      }).toList(),
                    );
                  }

                  // Fallback to static data (using actual MB values)
                  return Column(
                    children: [
                      _buildStorageItem(
                          'Cookbook',
                          AssetsManager.profileCookbook,
                          2,
                          500,
                          Colors.green, context), // 2MB/500MB
                      const SizedBox(height: 30),
                      _buildStorageItem('Recipe', AssetsManager.profileReciepe,
                          17, 300, Colors.orange, context), // 17MB/300MB
                      const SizedBox(height: 30),
                      _buildStorageItem(
                          'Meal Plan',
                          AssetsManager.profileMealplan,
                          0,
                          200,
                          context.theme.primaryColor, context), // 0MB/200MB
                      const SizedBox(height: 30),
                      _buildStorageItem('Videos', AssetsManager.profileVideos,
                          117, 500, Colors.purple, context), // 117MB/500MB
                      const SizedBox(height: 30),
                      _buildStorageItem('Images', AssetsManager.profileImages,
                          10, 500, Colors.blue, context), // 10MB/500MB
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper widget for storage legend items with labels
  Widget _buildStorageLegendItemWithLabel(
      Color color, String value, String label, BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Colored square indicator
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 8),
        // Text column with value and label
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Value text
            Text(
              value,
              style: TextStyle(
                fontSize: DeviceUtils().isTabletOrIpad(context) ? 10 : responsiveFont(20).sp,
                color: Colors.black87,
                fontFamily: 'inter',
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 0),
            // Label text
            Text(
              label,
              style: TextStyle(
                fontSize: DeviceUtils().isTabletOrIpad(context) ? 8 : responsiveFont(16).sp,
                color: Colors.grey[600],
                fontFamily: 'inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper widget for storage breakdown items
  Widget _buildStorageItem(String title, String iconPath, int usageMB,
      int storageMB, Color progressColor, BuildContext context) {
    // Calculate progress percentage
    double progressPercentage = storageMB > 0 ? (usageMB / storageMB) : 0.0;

    // Format size display - show MB if less than 1024MB, otherwise show GB
    String formattedSize;
    if (usageMB < 1024) {
      formattedSize = usageMB < 10 ? '0${usageMB}MB' : '${usageMB}MB';
    } else {
      int sizeGB = (usageMB / 1024).round();
      formattedSize = sizeGB < 10 ? '0${sizeGB}GB' : '${sizeGB}GB';
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Icon
        Image.asset(
          iconPath,
          width: 70,
          height: 70,
          fit: BoxFit.contain,
        ),
        const SizedBox(width: 25),
        // Title and progress bar section - centered vertically
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Title and size text row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : responsiveFont(22).sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                      fontFamily: 'inter',
                    ),
                  ),
                  Text(
                    formattedSize,
                    style: TextStyle(
                      fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : responsiveFont(22).sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                      fontFamily: 'inter',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Progress bar with visible gray background
              Container(
                height: 12,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    // Filled progress portion
                    FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: progressPercentage,
                      child: Container(
                        height: 12,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: progressColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
