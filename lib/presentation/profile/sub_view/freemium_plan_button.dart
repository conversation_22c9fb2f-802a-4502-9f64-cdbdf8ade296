import '../../../app/imports/core_imports.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/providers/profile/user_profile_notifier.dart';
import '../../../core/widgets/custom_text.dart';

class FreemiumPlanButton extends HookConsumerWidget {
  final int index; // Unique index for this button (e.g., 0, 1, 2)
   final String currentPlan;
  final String priceText;
  final String billingCycleText;
  final String planNameText;
  final VoidCallback? onPressed;
  final bool isPopular;
  final bool isCurrentPlan;

  const FreemiumPlanButton({
    super.key,
    required this.index, // Added to identify the button
     required this.currentPlan,
    required this.priceText,
    required this.billingCycleText,
    required this.planNameText,
    this.onPressed,
    this.isPopular = false,
    this.isCurrentPlan = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final currentPlanName = userProfileState.data?.userProfile?.currentPlan?.planName;

    // Determine if this button is selected
    final int selectedIndex =
    (currentPlanName ?? '') == 'Basic'
        ? 1
        : (currentPlanName ?? '') == 'Premium'
        ? 2
        : 0;

    final bool isSelected = index == selectedIndex;

    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? Colors.green : Colors.grey[200],
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6.0),
          side: BorderSide(
            color: isSelected ? Colors.green : Colors.grey[300]!,
            width: 1.0,
          ),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomText(
                text: priceText,
                size: 20,
                weight: FontWeight.w600,
                color: isSelected ? Colors.white : AppColors.primaryColor,
              ),
              CustomText(
                text: billingCycleText,
                size: 15,
                weight: FontWeight.w400,
                color: isSelected ? Colors.white : AppColors.textGreyColor,
              ),
            ],
          ),
          CustomText(
            text: currentPlan,
            size: 15,
            weight: FontWeight.w600,
            color: isSelected ? Colors.white : AppColors.primaryGreyColor,
            align: TextAlign.center,
          ),
        ],
      ),
    );
  }
}