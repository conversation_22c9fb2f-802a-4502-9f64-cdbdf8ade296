import 'dart:io';

import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/helpers/app_constant.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/no_data_widget.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/data/request_query/plan_request.dart';
import '../../../core/helpers/in_app_purchase_helper.dart';
import '../../../core/providers/profile/plans_notifier.dart';
import '../../../core/providers/profile/user_profile_notifier.dart';
import '../../../core/network/app_status.dart';
import '../../../core/data/models/plans_response.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_text.dart';
import 'freemium_plan_button.dart';

class SubscriptionView extends ConsumerStatefulWidget {
  const SubscriptionView({super.key});

  @override
  ConsumerState<SubscriptionView> createState() => _SubscriptionViewState();
}

class _SubscriptionViewState extends ConsumerState<SubscriptionView> {
  @override
  void initState() {
    super.initState();
    // Fetch plans when the widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(plansNotifierProvider.notifier).fetchPlans();
      // Optionally, ensure in-app purchase products are fetched

      if (DeviceType.mobile.name == getDeviceType(context).name ||
          DeviceType.tablet.name == getDeviceType(context).name ||
          DeviceType.mac.name == getDeviceType(context).name) {
        ref.read(inAppPurchaseProvider).getProducts();
      }
    });
  }

  // Helper method to check if a feature is available for a plan
  bool _isFeatureAvailable(Plan plan, String featureName) {
    return plan.features?.any((feature) =>
            feature.featureName
                ?.toLowerCase()
                .contains(featureName.toLowerCase()) ??
            false) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    final plansState = ref.watch(plansNotifierProvider);
    final inAppPurchaseState = ref.watch(inAppPurchaseProvider);
    print("dsfhjdshfh ${getDeviceType(context).name}");

    return Column(
      children: [
        // Show loading state for plans or products
        if (plansState.status == AppStatus.loading)
            Expanded(
            child: Center(
              child: LoadingAnimationWidget.fallingDot(
                color: Colors.grey,
                size: 50.0,
              ),
            ),
          )
        // Show error state
        else if (plansState.status == AppStatus.error)
          const Expanded(
            child: Center(
              child: NoDataWidget(
                title: 'Error loading plans or products',
              ),
            ),
          )
        // Show plans data
        else if (plansState.data?.plans != null &&
                plansState.data!.plans!.isNotEmpty &&
                (DeviceType.mobile.name == getDeviceType(context).name ||
                    DeviceType.tablet.name == getDeviceType(context).name ||
                    DeviceType.mac.name == getDeviceType(context).name)
            ? inAppPurchaseState.getProducts().isNotEmpty
            : true)
          ..._buildPlansContent(
            plansState.data?.plans!.toList()??[],
            inAppPurchaseState.getProducts(),
          )
        // Show empty state
        else
          const Expanded(
            child: Center(
              child: NoDataWidget(
                title: 'No plans or products available',
              ),
            ),
          ),
      ],
    );
  }

  List<Widget> _buildPlansContent(
      List<Plan> plans, List<ProductDetails> productList) {
    // Get current plan name from user profile
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final currentPlanName =
        userProfileState.data?.userProfile?.currentPlan?.planName;

    // Get all unique features from all plans
    final Set<String> allFeatures = <String>{};
    for (final plan in plans) {
      for (final feature in plan.features ?? []) {
        if (feature.featureName != null) {
          allFeatures.add(feature.featureName!);
        }
      }
    }
    final List<String> featuresList = allFeatures.toList();

    return [
      // Column headers
      Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 42, top: 20),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: CustomText(
                text: 'Features',
                weight: FontWeight.w500,
                size: 16,
                color: Colors.grey[700],
              ),
            ),
            for (int i = 0; i < plans.length; i++)
              Expanded(
                child: Container(
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      Stack(
                        alignment: Alignment.topCenter,
                        children: [
                          if (plans[i].planName == currentPlanName)
                            Positioned(
                              top: 0,
                              child: Text(
                                "", // Current Plan
                                style: TextStyle(
                                  fontSize: responsiveFont(20),
                                  fontWeight: FontWeight.w600,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          if (i == 2) // Popular Plan (Premium Plan)
                            Positioned(
                              top: 0,
                              child: Container(
                                color: Colors.red,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 15, vertical: 2),
                                child: CustomText(
                                  text: "POPULAR",
                                  size: 16,
                                  weight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          Padding(
                            padding: const EdgeInsets.only(top: 30),
                            child: CustomText(
                              text:
                                  '${plans[i].planName ?? 'Unknown Plan'} Plan',
                              align: TextAlign.center,
                              size: 16,
                              weight: FontWeight.w500,
                              color: AppColors.primaryGreyColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),

      // List of features (scrollable)
      Expanded(
        child: ListView.builder(
          padding: const EdgeInsets.only(bottom: 30),
          itemCount: featuresList.length,
          itemBuilder: (context, index) {
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 42),
              decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      featuresList[index],
                      style: TextStyle(
                        fontSize: DeviceUtils().isTabletOrIpad(context)
                            ? 14
                            : responsiveFont(21).sp,
                        fontFamily: 'inter',
                        fontWeight: FontWeight.w500,
                        color: AppColors.primaryGreyColor,
                      ),
                    ),
                  ),
                  for (int i = 0; i < plans.length; i++)
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.only(left: 36.w),
                        alignment: Alignment.center,
                        child: Image.asset(
                          _isFeatureAvailable(plans[i], featuresList[index])
                              ? AssetsManager.featureAvailable
                              : AssetsManager.featureNotAvailable,
                          width: 13,
                          height: 13,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),
      ),

      // Fixed bottom section with Update Plan buttons
      DeviceUtils().isTabletOrIpad(context)
          ? Container(
              color: Colors.white,
              padding: const EdgeInsets.only(
                  top: 10.0, right: 20.0, left: 20.0, bottom: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          text:
                              '* Payment will be charged to iTunes Account on purchase confirmation.\nSubscription will auto-renew through your iTunes Account unless you cancel it 24 hours prior.',
                          color: AppColors.primaryLightTextColor,
                          size: 13,
                          weight: FontWeight.w400,
                        ),
                        const SizedBox(height: 5),
                        Row(
                          children: [
                            MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                onTap: () {
                                  Utils().launchURL(
                                      url:
                                          'https://www.mastercook.com/privacy-policy');
                                },
                                child: CustomText(
                                  text: 'Privacy Policy',
                                  color: Colors.blue,
                                  underline: true,
                                  decorationThickness: 1,
                                ),
                              ),
                            ),
                            const SizedBox(width: 50),
                            MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                onTap: () {
                                  Utils().launchURL(
                                      url:
                                          'https://www.mastercook.com/conditions-of-use');
                                },
                                child: CustomText(
                                  text: 'Term and Conditions',
                                  color: Colors.blue,
                                  underline: true,
                                  decorationThickness: 1,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Container(width: 1, color: Colors.grey[200]),
                      SizedBox(
                        width: MediaQuery.of(context).size.width *
                            (DeviceUtils().isTabletOrIpad(context) ? 0.2 : 0.4),
                      ),
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            for (int i = 0; i < plans.length; i++) ...[
                              if (i > 0) const SizedBox(width: 20),
                              Expanded(
                                child: FreemiumPlanButton(
                                  currentPlan: i == 0
                                      ? 'Freemium Plan'
                                      : i == 1
                                          ? 'Basic Plan'
                                          : 'Premium Plan',
                                  priceText: plans[i].yearlyFee != null
                                      ? i == 1 && productList.isNotEmpty
                                          ? productList[0].price
                                          : i == 2 && productList.length > 1
                                              ? productList[1].price
                                              : '\$${plans[i].yearlyFee}' // Fallback if products unavailable
                                      : 'Free',
                                  billingCycleText: plans[i].yearlyFee != null
                                      ? '/Yearly'
                                      : '/Lifetime',
                                  planNameText:
                                      '${plans[i].planName ?? 'Unknown Plan'} Plan',
                                  isCurrentPlan:
                                      plans[i].planName == currentPlanName,
                                  onPressed: () async {
                                    if (DeviceType.mobile.name ==
                                            getDeviceType(context).name ||
                                        DeviceType.tablet.name ==
                                            getDeviceType(context).name ||
                                        DeviceType.mac.name ==
                                            getDeviceType(context).name) {
                                      final planNames = [
                                        'Freemium',
                                        'Basic',
                                        'Premium'
                                      ];
                                      final selectedPlan = planNames[i];
                                      if (selectedPlan != currentPlanName) {
                                         if (selectedPlan == 'Freemium' &&
                                            (currentPlanName == 'Basic' || currentPlanName == 'Premium')) {
                                          Utils().showFlushbar(
                                            context,
                                            message:
                                                'You cannot downgrade your plan.',
                                            isError: true,
                                          );
                                          return;
                                        }
                                        if (selectedPlan != 'Freemium') {
                                          if (i == 1 && productList.isEmpty || i == 2 && productList.length < 2) {
                                            // Utils().showFlushbar(
                                            //   context,
                                            //   message:
                                            //       'Product information not available. Please try again.',
                                            //   isError: true,
                                            // );
                                            return;
                                          }
                                          showLoader(); // Show loader before starting purchase
                                          try {
                                            final isSuccess = await ref
                                                .read(inAppPurchaseProvider)
                                                .buyProduct(
                                                  planId: plans[i].id,
                                                  context,
                                                  selectedPlan == 'Basic'
                                                      ? productList[0]
                                                      : productList[1],
                                                  ref,
                                                );
                                          //  if (!isSuccess && mounted) {
                                              hideLoader(); // Hide loader if purchase failed
                                              // Utils().showFlushbar(
                                              //   context,
                                              //   message: 'Purchase failed. Please try again.',
                                              //   isError: true,
                                              // );
                                           // }
                                            // Note: If successful, loader will be hidden by the purchase completion flow
                                          } catch (e) {
                                            if (mounted) {
                                              hideLoader(); // Ensure loader is hidden on error
                                              // Utils().showFlushbar(
                                              //   context,
                                              //   message: 'An error occurred during purchase.Please try again}',
                                              //   //message: 'An error occurred during purchase: ${e.toString()}',
                                              //   isError: true,
                                              // );
                                            }
                                          }
                                        } else {
                                          // Handle Freemium plan change
                                          showLoader(); // Show loader for Freemium plan change
                                          try {
                                            final parms = PlanRequest(
                                              planId: plans[i].id,
                                              store: Utils().getPlatformName() == 'iOS' || Utils().getPlatformName() == 'macOS'
                                                  ? 'apple'
                                                  : Utils().getPlatformName() == 'Android'
                                                  ? 'google'
                                                  : '',
                                              platform: Utils().getPlatformName(),
                                            );
                                            await ref.read(plansNotifierProvider.notifier).purchasePlan(parms, context);
                                          } catch (e) {
                                            if (mounted) {
                                              hideLoader(); // Ensure loader is hidden on error
                                              // Utils().showFlushbar(
                                              //   context,
                                              //   message: 'Failed to update plan. Please try again}',
                                              //   //message: 'Failed to update plan: ${e.toString()}',
                                              //   isError: true,
                                              // );
                                            }
                                          }
                                        }
                                      } else {
                                        Utils().showFlushbar(
                                          context,
                                          message:
                                              'You already have this plan.',
                                          isError: true,
                                        );
                                      }
                                    } else {
                                      Utils().launchURL(
                                          url: 'https://mastercook.ai/');
                                    }
                                  },
                                  index: i,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            )
          : Container(
              color: Colors.white,
              padding: const EdgeInsets.only(
                  top: 10.0, right: 20.0, left: 20.0, bottom: 16.0),
              child: Row(
                children: [
                  Container(width: 1, color: Colors.grey[200]),
                  SizedBox(
                    width: MediaQuery.of(context).size.width *
                        (DeviceUtils().isTabletOrIpad(context) ? 0.2 : 0.4),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Visibility(
                          visible: DeviceType.mobile.name ==
                              getDeviceType(context).name ||
                              DeviceType.tablet.name ==
                                  getDeviceType(context).name ||
                              DeviceType.mac.name ==
                                  getDeviceType(context).name,
                          child: CustomText(
                            text:
                                '* Payment will be charged to iTunes Account on purchase confirmation.\nSubscription will auto-renew through your iTunes Account unless you cancel it 24 hours prior.',
                            color: AppColors.primaryLightTextColor,
                            size: 13,
                            weight: FontWeight.w400,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Row(
                          children: [
                            MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                onTap: () {
                                  Utils().launchURL(
                                      url:
                                          'https://www.mastercook.com/privacy-policy');
                                },
                                child: CustomText(
                                  text: 'Privacy Policy',
                                  color: Colors.blue,
                                  underline: true,
                                  decorationThickness: 1,
                                ),
                              ),
                            ),
                            const SizedBox(width: 50),
                            MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                onTap: () {
                                  Utils().launchURL(
                                      url:
                                          'https://www.mastercook.com/conditions-of-use');
                                },
                                child: CustomText(
                                  text: 'Term and Conditions',
                                  color: Colors.blue,
                                  underline: true,
                                  decorationThickness: 1,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        for (int i = 0; i < plans.length; i++) ...[
                          if (i > 0) const SizedBox(width: 20),
                          Expanded(
                            child: FreemiumPlanButton(
                              currentPlan: i == 0
                                  ? 'Freemium Plan'
                                  : i == 1
                                      ? 'Basic Plan'
                                      : 'Premium Plan',
                              priceText: plans[i].yearlyFee != null
                                  ? i == 1 && productList.isNotEmpty
                                      ? productList[0].price
                                      : i == 2 && productList.length > 1
                                          ? productList[1].price
                                          : '\$${plans[i].yearlyFee}' // Fallback if products unavailable
                                  : 'Free',
                              billingCycleText: plans[i].yearlyFee != null
                                  ? '/Yearly'
                                  : '/Lifetime',
                              planNameText:
                                  '${plans[i].planName ?? 'Unknown Plan'} Plan',
                              isCurrentPlan:
                                  plans[i].planName == currentPlanName,
                              onPressed: () async {
                                if (DeviceType.mobile.name ==
                                        getDeviceType(context).name ||
                                    DeviceType.tablet.name ==
                                        getDeviceType(context).name ||
                                    DeviceType.mac.name ==
                                        getDeviceType(context).name) {
                                  final planNames = [
                                    'Freemium',
                                    'Basic',
                                    'Premium'
                                  ];
                                  final selectedPlan = planNames[i];
                                  if (selectedPlan != currentPlanName) {
                                  if (selectedPlan == 'Freemium' &&
                                        (currentPlanName == 'Basic' || currentPlanName == 'Premium')) {
                                      Utils().showFlushbar(
                                        context,
                                        message:
                                            'You cannot downgrade your plan.',
                                        isError: true,
                                      );
                                      return;
                                    }
                                    if (selectedPlan != 'Freemium') {
                                      if (i == 1 && productList.isEmpty ||
                                          i == 2 && productList.length < 2) {
                                        // Utils().showFlushbar(
                                        //   context,
                                        //   message:
                                        //       'Product information not available. Please try again.',
                                        //   isError: true,
                                        // );
                                        return;
                                      }
                                      showLoader(); // Show loader before starting purchase
                                      try {
                                        final isSuccess = await ref
                                            .read(inAppPurchaseProvider)
                                            .buyProduct(
                                              planId: plans[i].id,
                                              context,
                                              selectedPlan == 'Basic'
                                                  ? productList[0]
                                                  : productList[1],
                                              ref,
                                            );
                                        if (!isSuccess && mounted) {
                                          hideLoader(); // Hide loader if purchase failed
                                          // Utils().showFlushbar(
                                          //   context,
                                          //   message: 'Purchase failed. Please try again.',
                                          //   isError: true,
                                          // );
                                        }
                                        // Note: If successful, loader will be hidden by the purchase completion flow
                                      } catch (e) {
                                        if (mounted) {
                                          hideLoader(); // Ensure loader is hidden on error
                                          // Utils().showFlushbar(
                                          //   context,
                                          //    message: 'An error occurred during purchase please try again}',
                                          //   // message: 'An error occurred during purchase: ${e.toString()}',
                                          //   isError: true,
                                          // );
                                        }
                                      }
                                    } else {
                                      // Handle Freemium plan change
                                      showLoader(); // Show loader for Freemium plan change
                                      try {
                                        final parms = PlanRequest(
                                          planId: plans[i].id,
                                          store: Utils().getPlatformName() == 'iOS' || Utils().getPlatformName() == 'macOS'
                                              ? 'apple'
                                              : Utils().getPlatformName() == 'Android'
                                              ? 'google'
                                              : '',
                                          platform: Utils().getPlatformName(),
                                        );
                                        await ref.read(plansNotifierProvider.notifier).purchasePlan(parms, context);
                                      } catch (e) {
                                        if (mounted) {
                                          hideLoader(); // Ensure loader is hidden on error
                                        //   Utils().showFlushbar(
                                        //     context,
                                        //     message: 'Failed to update plan please try again)}',
                                        // //    message: 'Failed to update plan" ${e.toString()}',

                                        //     isError: true,
                                        //   );
                                        }
                                      }
                                    }
                                  } else {
                                    Utils().showFlushbar(
                                      context,
                                      message: 'You already have this plan.',
                                      isError: true,
                                    );
                                  }
                                } else {
                                  Utils()
                                      .launchURL(url: 'https://mastercook.ai/');
                                }
                              },
                              index: i,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
    ];
  }
}
