import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/core/helpers/local_storage_service.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/presentation/profile/sub_view/profile_body.dart';
import 'package:mastercookai/presentation/profile/sub_view/profile_header.dart';
import '../../app/assets_manager.dart';
import '../../core/helpers/in_app_purchase_helper.dart';
import '../../core/providers/profile/user_profile_notifier.dart';
import '../../core/providers/profile/user_types_notifier.dart';
import '../../core/widgets/custom_appbar.dart' show CustomAppBar;
import 'mobileui/my_account_mobile_ui.dart' hide ProfileHeader;

// Provider to track the current tab index in MyAccount screen
final myAccountTabProvider = StateProvider<int>((ref) => 0);

class Myaccount extends ConsumerStatefulWidget {
  final String? initialTabIndex;

  const Myaccount({super.key, this.initialTabIndex});

  @override
  ConsumerState<Myaccount> createState() => _myAccountState();
}

class _myAccountState extends ConsumerState<Myaccount>
    with SingleTickerProviderStateMixin {

  int currentTabIndex = 0;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();

    // Use provided tab index or fall back to the last remembered tab
    final rememberedTab = ref.read(myAccountTabProvider);
    currentTabIndex = int.tryParse(widget.initialTabIndex ?? '') ?? rememberedTab;

    _tabController = TabController(
      length: 4,
      initialIndex: currentTabIndex,
      vsync: this,
    );

    // Listen to tab changes and update the provider
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        ref.read(myAccountTabProvider.notifier).state = _tabController.index;
      }
    });

    // Update the provider with the current tab
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(myAccountTabProvider.notifier).state = currentTabIndex;
      ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
      ref.read(userTypesNotifierProvider.notifier).fetchUserTypes();
    });
  }

  @override
  void didUpdateWidget(covariant Myaccount oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Detect tab index change from navigation
    if (widget.initialTabIndex != oldWidget.initialTabIndex) {
      final rememberedTab = ref.read(myAccountTabProvider);
      final newTabIndex = int.tryParse(widget.initialTabIndex ?? '') ?? rememberedTab;
      setState(() {
        currentTabIndex = newTabIndex;
      });
      // Animate to the new tab and update provider
      _tabController.animateTo(newTabIndex);
      ref.read(myAccountTabProvider.notifier).state = newTabIndex;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final userProfileNotifier = ref.read(userProfileNotifierProvider.notifier);
    final purchaseHelper = ref.watch(inAppPurchaseProvider);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      purchaseHelper.initialize();
    });

    return getDeviceType(context).name == "mobile"
        ? MyAccountMobileUI()
        : Scaffold(
            appBar: CustomAppBar(
              title: 'My Account',
              actions: [
                GestureDetector(
                  onTap: () async {
                    final bool? confirmed =
                        await Utils().showCommonConfirmDialog(
                      context: context,
                      title: 'Logout',
                      subtitle: 'Are you sure you want to Logout?',
                      confirmText: 'Logout',
                      cancelText: 'Cancel',
                    );
                    if (confirmed == true && context.mounted) {
                      context.go('/splash');
                      final localStorage = ref.read(localStorageProvider);
                      await localStorage.clearLoginData();
                    }
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: Image.asset(
                      AssetsManager.logoutImage,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: 26)
              ],
            ),
            body: Stack(
              children: [
                Positioned.fill(
                  child: Image.asset(
                    AssetsManager.background_img,
                    fit: BoxFit.cover,
                  ),
                ),
                Column(
                  children: [
                    ProfileHeader(
                      userProfileNotifier: userProfileNotifier,
                      userProfileState: userProfileState,
                      productList: purchaseHelper.getProducts(),
                    ),
                    Expanded(
                      child: profileBody(
                        context: context,
                        userProfileState: userProfileState,
                        initialTabIndex: currentTabIndex,
                        tabController: _tabController,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
  }
}
