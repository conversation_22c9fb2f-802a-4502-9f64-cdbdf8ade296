import 'dart:io';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/data/request_query/plan_request.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import '../../../core/helpers/app_constant.dart';
import '../../../core/helpers/in_app_purchase_helper.dart';
import '../../../core/providers/profile/plans_notifier.dart';
import '../../../core/network/app_status.dart';
import '../../../core/data/models/plans_response.dart';
import '../../../core/providers/profile/user_profile_notifier.dart';
import '../../../core/utils/device_utils.dart';

class SubscriptionViewMobile extends ConsumerStatefulWidget {
  const SubscriptionViewMobile({super.key});

  @override
  ConsumerState<SubscriptionViewMobile> createState() =>
      _SubscriptionViewMobileState();
}

class _SubscriptionViewMobileState
    extends ConsumerState<SubscriptionViewMobile> {
  int selectedPlanIndex = 0;

  @override
  void initState() {
    super.initState();
    // Fetch plans when the widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(plansNotifierProvider.notifier).fetchPlans();
      final userProfile = ref.read(userProfileNotifierProvider).data?.userProfile;
      if (userProfile?.currentPlan?.planName != null) {
        final currentPlanName = userProfile!.currentPlan!.planName!;
        if (currentPlanName == 'Freemium') {
          setState(() {
            selectedPlanIndex = 0;
          });
        } else if (currentPlanName == 'Basic') {
          setState(() {
            selectedPlanIndex = 1;
          });
        } else if (currentPlanName == 'Premium') {
          setState(() {
            selectedPlanIndex = 2;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final plansState = ref.watch(plansNotifierProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: 'Subscription Plans',
        onPressed: () {
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          } else {
            // Go back to home page
            context.go(Routes.home);
          }
        },
      ),
      body: Column(
        children: [
          // Show loading state
          if (plansState.status == AppStatus.loading)
            SizedBox(
              height: MediaQuery.of(context).size.height - kToolbarHeight,
              child: Center(
                child: LoadingAnimationWidget.fallingDot(
                  color: AppColors.primaryColor,
                  size: 50.0,
                ),
              ),
            )
          // Show error state
          else if (plansState.status == AppStatus.error)
            SizedBox(
              height: MediaQuery.of(context).size.height - kToolbarHeight,
              child: Center(
                child: CustomText(
                  text: 'Error loading plans: ',
                  size: 18,
                  color: Colors.red,
                ),
              ),
            )
          // Show plans if available
          else if (plansState.data?.plans?.isNotEmpty == true)
              Expanded(
                child: _buildPlansContent(plansState.data!.plans!),
              )
            // Show empty state
            else
              SizedBox(
                height: MediaQuery.of(context).size.height - kToolbarHeight,
                child: Center(
                  child: CustomText(
                    text: 'No plans available',
                    size: 18,
                    color: Colors.grey,
                  ),
                ),
              ),
        ],
      ),
    );
  }

  bool _isFeatureAvailable(Plan plan, String featureName) {
    return plan.features?.any((feature) =>
    feature.featureName?.toLowerCase().contains(featureName.toLowerCase()) ??
        false) ??
        false;
  }

  Widget _buildPlansContent(List<Plan> plans) {
    final purchaseHelper = ref.watch(inAppPurchaseProvider);
    var productList = purchaseHelper.getProducts();
    final basicPrice = productList.isNotEmpty ? productList[0].price : 'N/A';
    final premiumPrice = productList.length > 1 ? productList[1].price : 'N/A';
    final Set<String> allFeatures = <String>{};
    for (final plan in plans) {
      for (final feature in plan.features ?? []) {
        if (feature.featureName != null) {
          allFeatures.add(feature.featureName!);
        }
      }
    }
    final List<String> featuresList = allFeatures.toList();

    return Column(
      children: [
        // Column headers
        Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.only(top: 28),
                  child: CustomText(
                    text: 'Features',
                    weight: FontWeight.w600,
                    size: 16,
                    color: Colors.grey[700],
                  ),
                ),
              ),
              // Freemium Plan
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    // Badge area - Current Plan
                    Container(
                      height: 20,
                      alignment: Alignment.center,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const CustomText(
                          text: "Current Plan",
                          size: 9,
                          weight: FontWeight.w600,
                          fontFamily: 'inter',
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    const CustomText(
                      text: 'Freemium',
                      align: TextAlign.center,
                      size: 14,
                      weight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    const CustomText(
                      text: 'Plan',
                      align: TextAlign.center,
                      size: 14,
                      weight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ],
                ),
              ),
              // Basic Plan
              Expanded(
                child: Column(
                  children: [
                    // Badge area - Empty space to align with other columns
                    const SizedBox(height: 20),
                    const SizedBox(height: 8),
                    const CustomText(
                      text: 'Basic',
                      align: TextAlign.center,
                      size: 14,
                      weight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    const CustomText(
                      text: 'Plan',
                      align: TextAlign.center,
                      size: 14,
                      weight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ],
                ),
              ),
              // Premium Plan
              Expanded(
                child: Column(
                  children: [
                    // Badge area - Popular
                    Container(
                      height: 20,
                      alignment: Alignment.center,
                      child: Transform.rotate(
                        angle: -0.1,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 3),
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor,
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: const CustomText(
                            text: "POPULAR",
                            size: 9,
                            weight: FontWeight.w700,
                            fontFamily: 'inter',
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    const CustomText(
                      text: 'Premium',
                      align: TextAlign.center,
                      size: 14,
                      weight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    const CustomText(
                      text: 'Plan',
                      align: TextAlign.center,
                      size: 14,
                      weight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        // Scrollable features list
        Expanded(
          child: ListView.builder(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.only(bottom: 30),
            itemCount: featuresList.length,
            itemBuilder: (context, index) {
              return Container(
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                decoration: BoxDecoration(
                  border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: CustomText(
                        text: featuresList[index],
                        weight: FontWeight.w500,
                        color: AppColors.primaryGreyColor,
                      ),
                    ),
                    for (int i = 0; i < plans.length; i++)
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.only(left: 36.w),
                          alignment: Alignment.center,
                          child: Image.asset(
                            _isFeatureAvailable(plans[i], featuresList[index])
                                ? AssetsManager.featureAvailable
                                : AssetsManager.featureNotAvailable,
                            width: 13,
                            height: 13,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
        // Fixed bottom section with plan pricing cards and Update Plan button
        Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 20.0),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 6),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      text:
                      '* Payment will be charged to iTunes Account on purchase confirmation.Subscription will auto-renew through your iTunes Account unless you cancel it 24 hours prior.',
                      color: AppColors.primaryLightTextColor,
                      size: 13,

                      weight: FontWeight.w400,
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () {
                              Utils().launchURL(
                                  url: 'https://www.mastercook.com/privacy-policy');
                            },
                            child: CustomText(
                              text: 'Privacy Policy',
                              color: Colors.blue,
                              underline: true,
                              decorationThickness: 1,
                            ),
                          ),
                        ),
                       Spacer(),
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () {
                              Utils().launchURL(
                                  url: 'https://www.mastercook.com/conditions-of-use');
                            },
                            child: CustomText(
                              text: 'Term and Conditions',
                              color: Colors.blue,
                              underline: true,
                              decorationThickness: 1,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              // Plan pricing cards row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Freemium Plan Card
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedPlanIndex = 0;
                        });
                      },
                      child: Container(
                        height: 90,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        padding: const EdgeInsets.symmetric(
                            vertical: 12, horizontal: 8),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: selectedPlanIndex == 0
                                ? Colors.blue.shade300
                                : Colors.grey.shade300,
                            width: selectedPlanIndex == 0 ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                          color: selectedPlanIndex == 0
                              ? Colors.blue.shade50
                              : Colors.white,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            RichText(
                              textAlign: TextAlign.center,
                              text: const TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'Free',
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.primaryColor,
                                      fontFamily: 'inter',
                                    ),
                                  ),
                                  TextSpan(
                                    text: '/Lifetime',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.grey,
                                      fontFamily: 'inter',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 4),
                            const CustomText(
                              text: 'Freemium Plan',
                              size: 11,
                              weight: FontWeight.w500,
                              color: Colors.black87,
                              align: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Basic Plan Card
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedPlanIndex = 1;
                        });
                      },
                      child: Container(
                        height: 90,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        padding: const EdgeInsets.symmetric(
                            vertical: 12, horizontal: 8),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: selectedPlanIndex == 1
                                ? Colors.blue.shade300
                                : Colors.grey.shade300,
                            width: selectedPlanIndex == 1 ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                          color: selectedPlanIndex == 1
                              ? Colors.blue.shade50
                              : Colors.white,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            RichText(
                              textAlign: TextAlign.center,
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: basicPrice,
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.primaryColor,
                                      fontFamily: 'inter',
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '/Yearly',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.grey,
                                      fontFamily: 'inter',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 4),
                            const CustomText(
                              text: 'Basic Plan',
                              size: 11,
                              weight: FontWeight.w500,
                              color: Colors.black87,
                              align: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Premium Plan Card
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedPlanIndex = 2;
                        });
                      },
                      child: Container(
                        height: 90,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        padding: const EdgeInsets.symmetric(
                            vertical: 12, horizontal: 8),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: selectedPlanIndex == 2
                                ? Colors.blue.shade300
                                : Colors.grey.shade300,
                            width: selectedPlanIndex == 2 ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                          color: selectedPlanIndex == 2
                              ? Colors.blue.shade50
                              : Colors.white,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            RichText(
                              textAlign: TextAlign.center,
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: premiumPrice,
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.primaryColor,
                                      fontFamily: 'inter',
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '/Yearly',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.grey,
                                      fontFamily: 'inter',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 4),
                            const CustomText(
                              text: 'Premium Plan',
                              size: 11,
                              weight: FontWeight.w500,
                              color: Colors.black87,
                              align: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 15),
              // Update Plan button
              SizedBox(
                width: double.infinity,
                height: 48,
                child: CustomButton(
                  text: _getUpdateButtonText(),
                  fontSize: 14,
                  onPressed: () {
                    _handlePlanUpdate();
                  },
                  color: AppColors.primaryColor,
                  textColor: Colors.white,
                  borderRadius: 8,
                ),
              ),
              const SizedBox(height: 15),
            ],
          ),
        ),
      ],
    );
  }

  String _getUpdateButtonText() {
    switch (selectedPlanIndex) {
      case 0:
        return 'Update to Freemium Plan';
      case 1:
        return 'Update to Basic Plan';
      case 2:
        return 'Update to Premium Plan';
      default:
        return 'Update Plan';
    }
  }

  Future<void> _handlePlanUpdate() async {
    final plansState = ref.watch(plansNotifierProvider);
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final currentPlanName = userProfileState.data?.userProfile?.currentPlan?.planName;
    final selectedPlan = ['Freemium', 'Basic', 'Premium'][selectedPlanIndex];
    final productList = ref.watch(inAppPurchaseProvider).getProducts();

    if (selectedPlan != currentPlanName) {
      if (selectedPlan != 'Freemium') {
        if (productList.isNotEmpty && productList.length > selectedPlanIndex - 1) {
          showLoader(); // Show loader before starting the purchase process
          try {
            final isSuccess = await ref.read(inAppPurchaseProvider).buyProduct(
              planId: plansState.data?.plans?[selectedPlanIndex].id,
              context,
              selectedPlan == 'Basic' ? productList[0] : productList[1],
              ref,
            );
            
            if (!isSuccess && mounted) {
              hideLoader(); // Hide loader if purchase failed
              Utils().showFlushbar(context,
                  message: 'Purchase failed. Please try again.', isError: true);
            }
            // Note: If successful, loader will be hidden by the purchase completion flow
          } catch (e) {
            if (mounted) {
              hideLoader(); // Ensure loader is hidden on error
              Utils().showFlushbar(context,
                  message: 'An error occurred during purchase: ${e.toString()}', isError: true);
            }
          }
        } else {
          Utils().showFlushbar(context,
              message: 'Product not available for $selectedPlan plan.', isError: true);
        }
      } else {
        showLoader(); // Show loader for Freemium plan change
        try {
          final parms = PlanRequest(
            planId: plansState.data?.plans?[selectedPlanIndex].id,
            store: Utils().getPlatformName() == 'iOS' || Utils().getPlatformName() == 'macOS'
                ? 'apple'
                : Utils().getPlatformName() == 'Android'
                ? 'google'
                : '',
            platform: Utils().getPlatformName(),
          );
          await ref.read(plansNotifierProvider.notifier).purchasePlan(parms, context);
        } catch (e) {
          if (mounted) {
            hideLoader(); // Ensure loader is hidden on error
            Utils().showFlushbar(context,
                message: 'Failed to update plan: ${e.toString()}', isError: true);
          }
        }
      }
    } else {
      Utils().showFlushbar(context,
          message: 'You already have this plan.', isError: true);
    }
  }
}
