import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:go_router/go_router.dart';

import '../../../core/widgets/custom_button.dart';

class ChangePasswordMobile extends ConsumerStatefulWidget {
  const ChangePasswordMobile({super.key});

  @override
  ConsumerState<ChangePasswordMobile> createState() =>
      _ChangePasswordMobileState();
}

class _ChangePasswordMobileState extends ConsumerState<ChangePasswordMobile> {
  // Password controllers
  final TextEditingController _currentPasswordController =
      TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmNewPasswordController =
      TextEditingController();

  // State for checkbox (newsletter)
  bool _subscribeNewsletter = true;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmNewPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleSavePassword() async {
    // Basic validation
    if (_currentPasswordController.text.trim().isEmpty) {
      Utils().showFlushbar(
        context,
        message: 'Please enter your current password',
        isError: true,
      );
      return;
    }
    if (_newPasswordController.text.trim().isEmpty) {
      Utils().showFlushbar(
        context,
        message: 'Please enter a new password',
        isError: true,
      );
      return;
    }
    if (_confirmNewPasswordController.text.trim().isEmpty) {
      Utils().showFlushbar(
        context,
        message: 'Please confirm your new password',
        isError: true,
      );
      return;
    }
    if (_newPasswordController.text.trim() !=
        _confirmNewPasswordController.text.trim()) {
      Utils().showFlushbar(
        context,
        message: 'Passwords do not match',
        isError: true,
      );
      return;
    }

    if (!mounted) return;

    // Call reset password API
    await ref.read(userProfileNotifierProvider.notifier).resetPassword(
      context,
      _currentPasswordController.text.trim(),
      _newPasswordController.text.trim(),
      _confirmNewPasswordController.text.trim(),
      onSuccess: () {
        // Clear fields on success
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmNewPasswordController.clear();
      },
    );
  }

  Future<void> _handleRemoveAccount() async {
    final bool? shouldDelete = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Remove Account',
      subtitle:
          'Are you sure you want to remove your account? This action cannot be undone.',
      confirmText: 'Remove',
      cancelText: 'Cancel',
    );

    if (shouldDelete == true && context.mounted) {
      await ref
          .read(userProfileNotifierProvider.notifier)
          .deleteAccount(context);
    }
  }

  Widget _buildPasswordField(
    String label,
    TextEditingController controller, {
    bool showDots = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 140,
          child: Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 13,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: SizedBox(
            height: 35,
            child: SizedBox(
              height: 30,
              child: CustomInputField(
                hintText: showDots ? '••••••••' : '',
                controller: controller,
                isPassword: true,
                passwordIconSize: 18,
                isUpdateMode: true,
                borderRadius: 8,
                fontSize: 14,
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final isLoading = userProfileState.status == AppStatus.loading ||
                     userProfileState.status == AppStatus.updating;

    return Scaffold(
      backgroundColor: AppColors.whiteColor,
      appBar: CustomAppBar(
        title: 'Settings',
        onPressed: () {
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          } else {
            // Go back to My Profile page instead of home
            context.go(Routes.myAccountMobile);
          }
        },
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Change Password Section
            const Text(
              'Change Password:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 20),

            // Current Password Field
            _buildPasswordField(
              'Current Password',
              _currentPasswordController,
              showDots: true,
            ),
            const SizedBox(height: 16),

            // New Password Field
            _buildPasswordField(
              'New Password',
              _newPasswordController,
              showDots: true,
            ),
            const SizedBox(height: 16),

            // Confirm New Password Field
            _buildPasswordField(
              'Confirm New Password',
              _confirmNewPasswordController,
              showDots: true,
            ),
            const SizedBox(height: 24),

          //  Save Password Button
            SizedBox(
              width: double.infinity,
              height: 40,
              child: ElevatedButton(
                onPressed: isLoading ? null : _handleSavePassword,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    :


                const Text(
                        'Save Password',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ),
            const SizedBox(height: 32),

            // Newsletter Section
            const Text(
              'Newsletter:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 16),

            // Subscribe Newsletter Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Subscribe Newsletter',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _subscribeNewsletter = !_subscribeNewsletter;
                    });
                  },
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: _subscribeNewsletter
                          ? AppColors.primaryColor
                          : Colors.white,
                      border: Border.all(
                        color: _subscribeNewsletter
                            ? AppColors.primaryColor
                            : Colors.grey[400]!,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: _subscribeNewsletter
                        ? const Icon(
                            Icons.check,
                            size: 14,
                            color: Colors.white,
                          )
                        : null,
                  ),
                ),
              ],
            ),

            // Spacer to push Remove Account to bottom
            const SizedBox(height: 200),
            // Remove Account Button
            Center(
              child: GestureDetector(
                onTap: _handleRemoveAccount,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color.fromARGB(223, 211, 211, 206),
                      width: 0,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x1A000000),
                        spreadRadius: 0,
                        blurRadius: 10,
                        offset: const Offset(0, 0),
                      ),
                    ],
                  ),
                  child: Center(
                    child: const Text(
                      'Remove Account',
                      style: TextStyle(
                        color: AppColors.primaryColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
