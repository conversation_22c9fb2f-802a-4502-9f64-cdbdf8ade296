import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/helpers/local_storage_service.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/presentation/profile/mobileui/change_password_mobile.dart';
import 'package:mastercookai/presentation/profile/mobileui/subscription_view_mobile.dart';
import 'package:mastercookai/presentation/profile/mobileui/edit_profile_mobile.dart';
import '../../../core/providers/menus/bottom_nav_notifier.dart';
import '../../../core/providers/profile/user_profile_notifier.dart';

class MyAccountMobileUI extends ConsumerWidget {
  const MyAccountMobileUI({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final userProfile = userProfileState.data?.userProfile;
    final bottomNavNotifier = ref.read(bottomNavProvider.notifier);

    return Scaffold(
      backgroundColor: AppColors.whiteColor,
      appBar: AppBar(
        title: Text(
          'My Account',
          style: TextStyle(
            color: AppColors.texGreyColor,
            fontWeight: FontWeight.w500,
            fontFamily: 'Inter',
            fontSize: 16,
          ),
        ),
        centerTitle: false,
        elevation: 2,
        automaticallyImplyLeading: false,
        actions: [
          Visibility(
            visible: false,
            child: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                onPressed: () {
                  Navigator.of(context, rootNavigator: true).push(
                    MaterialPageRoute(
                      builder: (context) => const EditProfileMobile(),
                    ),
                  );
                },
                icon: const Icon(
                  Icons.settings,
                  color: AppColors.primaryGreyColor,
                ),
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                  color: const Color(0x1AE0E0E0),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  border: Border.all(
                    color: const Color(0xFFE0E0E0),
                    width: 1,
                  )),
              child: Column(
                children: [
                  // Profile Picture with verified badge
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: Colors.grey.shade200,
                        backgroundImage:
                            const AssetImage(AssetsManager.profile_placeholder),
                        foregroundImage: userProfile?.profilePic != null
                            ? NetworkImage(userProfile!.profilePic!)
                            : const AssetImage(
                                AssetsManager.profile_placeholder),
                        child: userProfile?.profilePic == null
                            ? const Icon(
                                Icons.person,
                                size: 40,
                                color: Colors.grey,
                              )
                            : null,
                      ),
                      Visibility(
                        visible: false,
                        child: Positioned(
                          top: 0,
                          right: 0,
                          child: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: AppColors.primaryBorderColor,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                            child: const Image(
                              image: AssetImage(AssetsManager.addProfile),
                              width: 12,
                              height: 12,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // User Name
                  Center(
                    child: SizedBox(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            AssetsManager.personImage,
                            width: 16,
                            height: 16,
                            fit: BoxFit.contain,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: CustomText(
                              text: userProfile?.name.isNotEmpty == true
                                  ? userProfile!.name
                                  : '',
                              size: responsiveFont(18),
                              weight: FontWeight.bold,
                              color: AppColors.primaryGreyColor,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Email
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.mail, color: AppColors.primaryColor, size: 20),
                      const SizedBox(width: 8),
                      CustomText(
                        text: userProfile?.email ?? '',
                        size: 14,
                        color: AppColors.textGreyColor,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Phone
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.phone,
                          color: AppColors.primaryColor, size: 20),
                      const SizedBox(width: 8),
                      CustomText(
                        text: userProfile?.phone ?? '',
                        size: 14,
                        color: AppColors.textGreyColor,
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            Column(
              children: [
                CustomButton(
                  borderRadius: 8,
                  text: 'Edit Profile',
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).push(
                      MaterialPageRoute(
                        builder: (context) => const EditProfileMobile(),
                      ),
                    );
                  },
                  showTrailingArrow: true,
                ),
                SizedBox(
                  height: 10,
                ),
                CustomButton(
                  borderRadius: 8,
                  text: 'Settings',
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).push(
                      MaterialPageRoute(
                        builder: (context) => const ChangePasswordMobile(),
                      ),
                    );
                  },
                  showTrailingArrow: true,
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Current Plan Section
            Visibility(
              visible: true,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                    color: const Color(0x1AE0E0E0),
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.1),
                        spreadRadius: 1,
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                    border: Border.all(
                      color: const Color(0xFFE0E0E0),
                      width: 1,
                    )),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      text: 'Your Current plan',
                      size: 18,
                      weight: FontWeight.w600,
                      color: AppColors.blackColor,
                    ),
                    const SizedBox(height: 16),

                    // Plan Details Container
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.1),
                            spreadRadius: 1,
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // Plan Details
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CustomText(
                                text: userProfile?.currentPlan?.planName ?? '',
                                size: 18,
                                weight: FontWeight.w600,
                                color: AppColors.primaryGreyColor,
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  CustomText(
                                    text: userProfile
                                                ?.currentPlan?.planAmount !=
                                            null
                                        ? '\$${userProfile!.currentPlan!.planAmount}'
                                        : '\$0',
                                    size: 18,
                                    weight: FontWeight.w600,
                                    color: Colors.red,
                                  ),
                                  CustomText(
                                    text: userProfile?.currentPlan
                                                ?.billingInterval !=
                                            null
                                        ? userProfile!.currentPlan!
                                                    .billingInterval
                                                    .toLowerCase() ==
                                                'year'
                                            ? 'Yearly'
                                            : userProfile
                                                .currentPlan!.billingInterval
                                        : '/Yearly',
                                    size: 14,
                                    color: AppColors.textGreyColor,
                                  ),
                                  Visibility(
                                    visible: userProfile
                                            ?.currentPlan?.billingInterval !=
                                        null,
                                    child: Text.rich(
                                      TextSpan(
                                        children: [
                                          const TextSpan(
                                            text: 'Renew On ',
                                            style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontSize: 14,
                                                color: AppColors
                                                    .primaryLightTextColor),
                                          ),
                                          TextSpan(
                                            text: Utils().formatDateForDisplay(
                                                userProfile?.currentPlan
                                                        ?.nextPaymentDate ??
                                                    ''),
                                            style: TextStyle(
                                                fontWeight: FontWeight.w600,
                                                fontSize: 14,
                                                color: AppColors
                                                    .primaryLightTextColor),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Upgrade Button
                          CustomButton(
                            text: 'Upgrade Plan',
                            onPressed: () {
                              Navigator.of(context, rootNavigator: true).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const SubscriptionViewMobile(),
                                ),
                              );
                            },
                            color: AppColors.primaryColor,
                            textColor: Colors.white,
                            height: 48,
                            borderRadius: 8,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Logout Button
            TextButton(
              onPressed: () async {
                final bool? confirmed = await Utils().showCommonConfirmDialog(
                  context: context,
                  title: 'Logout',
                  subtitle: 'Are you sure you want to Logout?',
                  confirmText: 'Logout',
                  cancelText: 'Cancel',
                );
                if (confirmed == true && context.mounted) {
                  final localStorage = ref.read(localStorageProvider);
                  await localStorage.clearLoginData();
                  bottomNavNotifier.onTabSelected(0);
                  if (context.mounted) {
                    context.go('/splash');
                  }
                }
              },
              style: TextButton.styleFrom(
                backgroundColor: Colors.transparent,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const CustomText(
                text: 'Logout',
                color: AppColors.primaryColor,
                size: 16,
                decorationThickness: 1,
                underline: true,
                weight: FontWeight.w500,
              ),
            ),

            GestureDetector(
              onTap: () async {
                await InAppPurchase.instance.restorePurchases();
                if (context.mounted) {
                  Utils().showFlushbar(context,
                      message: 'Restoring purchases...', isError: false);
                }

                Future.delayed(Duration(seconds: 7), () {
                  ref
                      .read(userProfileNotifierProvider.notifier)
                      .fetchUserProfile();
                });
              },
              child: CustomText(
                text: 'Restore Purchase',
                size: 14,
                decorationThickness: 1,
                underline: true,
                color: Theme.of(context).primaryColor,
                weight: FontWeight.w500,
              ),
            ),
            SizedBox(
              height: 40,
            )
          ],
        ),
      ),
    );
  }
}
