import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/theme/colors.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/profile/user_types_notifier.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

import 'build_user_types_dopdown_field.dart';

class CompanyDetailsSection extends ConsumerWidget {
  final TextEditingController companyNameController;
  final TextEditingController typesOfUsersController;
  final String? selectedUserType;
  final int? selectedUserTypeId;
  final Function(String, int) onUserTypeChanged;

  const CompanyDetailsSection({
    super.key,
    required this.companyNameController,
    required this.typesOfUsersController,
    required this.selectedUserType,
    required this.selectedUserTypeId,
    required this.onUserTypeChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: 'Company Details:',
          size: 14,
          weight: FontWeight.w500,
          color: AppColors.primaryLightTextColor,
        ),
        const SizedBox(height: 20),

        // Company Name
        _buildLabeledField(
          'Company Name',
          SizedBox(
            child: CustomInputField(
              hintText: 'Mastercook',
              controller: companyNameController,
              keyboardType: TextInputType.text,
              borderRadius: 6,
              maxLength: 100,
              fontSize: 14,
            ),
          ),
        ),
        const SizedBox(height: 20),

        // Types of Users
        _buildLabeledField(
            'Types of Users',
            UserTypesDropdownField(
                selectedUserType: selectedUserType,
                onChanged: (value) {
                  if (value != null) {
                    onUserTypeChanged(value.type, value.id);
                    typesOfUsersController.text = value.type;
                  }
                })),
      ],
    );
  }

  // Helper method to create labeled fields
  Widget _buildLabeledField(String label, Widget field) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Padding(
            padding: const EdgeInsets.only(top: 5),
            child: CustomText(
              text: label,
              size: 13,
              weight: FontWeight.w400,
              color: AppColors.blackColor,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(child: field),
      ],
    );
  }

// User types dropdown field
}
