import 'dart:async';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_drawer.dart';
import 'package:mastercookai/core/widgets/custom_searchbar.dart';
import 'package:mastercookai/presentation/cookbook/widgets/cookbook_filter.dart';
import 'package:mastercookai/presentation/cookbook/widgets/paginated_gridview.dart';
import 'package:mastercookai/presentation/cookbook/widgets/update_cookbook_dialog.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_appbar.dart';
import '../../core/network/app_status.dart';
import '../../core/providers/categories_notifier.dart';
import '../../core/providers/cookbook_notifier.dart';
import '../../core/providers/recipe/metadata_provider.dart';
import '../../core/providers/recipe_notifier.dart';
import '../../core/providers/cuisines_notifier.dart';
import '../../core/data/models/cookbook.dart';
import '../../core/utils/device_utils.dart';
import '../../core/widgets/custom_button.dart';
import '../../core/widgets/no_data_widget.dart';
import '../shimer/recipe_shimmer.dart';
import '../shimer/cookbook_list_shimmer.dart';
import 'mobile/mobile_cookbook_details_screen.dart';
import 'widgets/cookbook_details_card.dart';
import 'widgets/drop_down_filter.dart';

class CookbookDetailScreen extends ConsumerStatefulWidget {
  const CookbookDetailScreen({super.key});

  @override
  ConsumerState<CookbookDetailScreen> createState() =>
      _CookbookDetailsScreenState();
}

class _CookbookDetailsScreenState extends ConsumerState<CookbookDetailScreen> {
  bool _isSearching = false;
  TextEditingController cookbookSearchController = TextEditingController();
  TextEditingController recipeSearchController = TextEditingController();
  int totalPages = 0;
  int _selectedCookbookIndex = 0;
  int cuisine = 0;
  int category = 0;
  int cookbookId = 0;
  String cookbookName = '';
  String sortOption = ''; // Default sort option
  final ScrollController _scrollController = ScrollController();
  Timer? _cookbookSearchDebounce;
  Timer? _recipeSearchDebounce;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // Track previous search text to avoid unnecessary API calls
  String _previousRecipeSearchText = '';
  String _previousCookbookSearchText = '';

  // When true, force-select the cookbook from route params on next state update
  bool _forceSelect = false;

  final Map<String, List<String>> _filterOptions = {
    "Cuisine": [],
    "Category": [],
    "Sort": ['Name (A-Z)', 'Name (Z-A)', 'Most Recent', 'Oldest First'],
    // Added new sort options
  };

  Map<String, String> _selectedFilters = {
    "Cuisine": "",
    "Category": "",
    "Sort": "Sort By", // Default selected sort option
  };

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    recipeSearchController.addListener(_onSearchChanged);
    cookbookSearchController.addListener(_onCookbookSearchChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final id = GoRouterState.of(context).uri.queryParameters['id'];
      final name = GoRouterState.of(context).uri.queryParameters['name'];
      if (id == null || name == null) {
        debugPrint(
            'Invalid id or name in route parameters: id=$id, name=$name');
        return;
      }

      cookbookId = int.parse(id);
      cookbookName = Uri.decodeComponent(name);
      // Optional: if focus=1 is present, force-select the target cookbook
      final focus = GoRouterState.of(context).uri.queryParameters['focus'];
      _forceSelect = focus == '1';

      // Ensure we have fresh cookbook data and find the target cookbook
      if (mounted) {
        // First try to fetch all cookbooks
        await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
              context: context,
              loadMore: false,
            );

        final cookbookState = ref.read(cookbookNotifierProvider);
        final targetIndex = cookbookState.data?.indexWhere((cb) => cb.id == cookbookId) ?? -1;

        if (targetIndex != -1) {
          // Found the target cookbook in the current list
          // Move the selected cookbook to the top of the list
          _reorderCookbookListWithSelectedFirst(targetIndex);
          _forceSelect = false;
          debugPrint('Found target cookbook in initial fetch: index=$targetIndex, cookbookId=$cookbookId, cookbookName=$cookbookName');
          debugPrint('Moved selected cookbook to top of list');
          fetchRecipe();
        } else {
          // Target cookbook not found, incrementally load more pages to find it
          debugPrint('Target cookbook not found in initial fetch (list length: ${cookbookState.data?.length}), trying to load more pages to find it');
          await _findTargetCookbookAcrossPages();
        }

        if (mounted) {
          ref
              .read(categoriesNotifierProvider.notifier)
              .fetchCategories(context: context);
          ref
              .read(cuisinesNotifierProvider.notifier)
              .fetchCuisines(context: context);
        }
      }

      final cuisines = ref.read(cuisinesNotifierProvider).data ?? [];
      _filterOptions['Cuisine'] = cuisines.map((c) => c.name ?? '').toList();

      final categories = ref.read(categoriesNotifierProvider).data ?? [];
      _filterOptions['Category'] = categories.map((c) => c.name ?? '').toList();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    cookbookSearchController.dispose();
    recipeSearchController.dispose();
    _scrollController.dispose();
    _recipeSearchDebounce?.cancel();
    _cookbookSearchDebounce?.cancel();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final cookbookState = ref.read(cookbookNotifierProvider);
      if (cookbookState.hasMore &&
          cookbookState.status != AppStatus.loadingMore) {
        ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
              loadMore: true,
              context: context,
            );
      }
    }
  }

  void _onSearchChanged() {
    final text = recipeSearchController.text.trim();

    // Only trigger search if:
    // 1. Text has at least 3 characters, OR
    // 2. Text is empty AND previous text was not empty (clearing search)
    final shouldSearch = text.length >= 3 || (text.isEmpty && _previousRecipeSearchText.isNotEmpty);

    if (shouldSearch) {
      _debounceSearch(text);
    }

    // Update previous text for next comparison
    _previousRecipeSearchText = text;
  }

  void _onCookbookSearchChanged() {
    final text = cookbookSearchController.text.trim();

    // Only trigger search if:
    // 1. Text has at least 3 characters, OR
    // 2. Text is empty AND previous text was not empty (clearing search)
    final shouldSearch = text.length >= 3 || (text.isEmpty && _previousCookbookSearchText.isNotEmpty);

    if (shouldSearch) {
      _debounceCookbookSearch(text);
    }

    // Update previous text for next comparison
    _previousCookbookSearchText = text;
  }

  void _debounceCookbookSearch(String query) {
    if (_cookbookSearchDebounce?.isActive ?? false) {
      _cookbookSearchDebounce!.cancel();
    }
    _cookbookSearchDebounce =
        Timer(const Duration(milliseconds: 500), () async {
      bool isSuccess =
          await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                context: context,
                loadMore: false,
                search: query,
              );
      if (isSuccess) {
        final cookbookState = ref.read(cookbookNotifierProvider);
        if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
          // Try to find the target cookbook first, otherwise use first cookbook
          final targetIndex = cookbookState.data!.indexWhere((cb) => cb.id == cookbookId);
          if (targetIndex != -1) {
            _selectedCookbookIndex = targetIndex;
          } else {
            _selectedCookbookIndex = 0;
            // Only update cookbookId if we don't have a target cookbook
            if (cookbookId == 0) {
              cookbookId = cookbookState.data![0].id;
              cookbookName = cookbookState.data![0].name;
            }
          }
          setState(() {});
          fetchRecipe();
        }
      }
    });
  }

  void _debounceSearch(String query) {
    if (_recipeSearchDebounce?.isActive ?? false) {
      _recipeSearchDebounce!.cancel();
    }
    _recipeSearchDebounce = Timer(const Duration(milliseconds: 500), () {
      fetchRecipe();
    });
  }

  // Check if any filters are applied
  // bool get _isAnyFilterApplied {
  //   return _selectedFilters.values.any((value) => value.isNotEmpty) ||
  //       recipeSearchController.text.trim().isNotEmpty ||
  //       cookbookSearchController.text.trim().isNotEmpty;
  // }

  bool get _isAnyFilterApplied {
    final hasCuisineFilter = _selectedFilters['Cuisine']?.isNotEmpty ?? false;
    final hasCategoryFilter = _selectedFilters['Category']?.isNotEmpty ?? false;
    final hasSortFilter = (_selectedFilters['Sort'] != null &&
        _selectedFilters['Sort'] != 'Sort By');
    return hasCuisineFilter || hasCategoryFilter || hasSortFilter;
  }

  @override
  Widget build(BuildContext context) {
    final cookbookState = ref.watch(cookbookNotifierProvider);
    final recipeState = ref.watch(recipeNotifierProvider);

    // Listen for changes in cookbook state and update selected index
    ref.listen<AppState<List<Cookbook>>>(cookbookNotifierProvider, (previous, next) {
      if (next.data != null && next.data!.isNotEmpty) {
        final newIndex = next.data!.indexWhere((cb) => cb.id == cookbookId);
        final shouldUpdate = newIndex != -1 && (_forceSelect || newIndex != _selectedCookbookIndex);
        if (shouldUpdate) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // Move the selected cookbook to the top of the list
            _reorderCookbookListWithSelectedFirst(newIndex);
            _forceSelect = false;
            fetchRecipe();
          });
        }
      }
    });

    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;
    final isSmallScreen =
        screenSize.width <= 1100; // Define small screen breakpoint
    final crossAxisCount = (DeviceUtils().isTabletOrIpad(context)
            ? 3
            : isHighRes
                ? 4
                : 4)
        .toInt();

    return getDeviceType(context).name == 'mobile'
        ? MobileCookbookDetailsScreen()
        : Scaffold(
            key: _scaffoldKey,
            onDrawerChanged: (isOpened) {
              if (!isOpened) {
                if (cookbookSearchController.text.isNotEmpty) {
                  onClearFiltersPressed(context);
                }
              }
            },
            appBar: CustomAppBar(
              title: cookbookName,
              showDrawerIcon: DeviceUtils().isTabletOrIpad(context),
              onPressed: () {
                if (cookbookSearchController.text.isNotEmpty) {
                  onClearFiltersPressed(context);
                }
                if (Navigator.of(context).canPop()) {
                  Navigator.of(context).pop();
                } else {
                  context.go(Routes.home);
                }
              },
              actions: [
                if (!isSmallScreen)
                  Wrap(
                    spacing: 8,
                    children: _filterOptions.keys.map((filterName) {
                      return DropdownFilter(
                        filterName: filterName,
                        selectedFilters: _selectedFilters,
                        filterOptions: _filterOptions,
                        onFilterChanged: (filterName, selectedValue) {
                          setState(() {
                            _selectedFilters[filterName] = selectedValue;
                            switch (filterName) {
                              case 'Cuisine':
                                final cuisineModel = ref
                                    .read(cuisinesNotifierProvider)
                                    .data
                                    ?.firstWhere(
                                        (c) => c.name == selectedValue);
                                if (cuisineModel != null) {
                                  cuisine = cuisineModel.id!;
                                  ref
                                      .read(recipeMetadataProvider.notifier)
                                      .updateCuisineId(cuisineModel.id!);
                                  fetchRecipe();
                                }
                                break;
                              case 'Category':
                                final categoryModel = ref
                                    .read(categoriesNotifierProvider)
                                    .data
                                    ?.firstWhere(
                                        (c) => c.name == selectedValue);
                                if (categoryModel != null) {
                                  category = categoryModel.id!;
                                  ref
                                      .read(recipeMetadataProvider.notifier)
                                      .updateCategoryId(categoryModel.id);
                                  fetchRecipe();
                                }
                                break;
                              case 'Sort':
                                sortOption = selectedValue;

                                fetchRecipe();
                                break;
                            }
                          });
                        },
                      );
                    }).toList(),
                  ),
                SizedBox(width: 12.w),
                if (isSmallScreen)
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      transitionBuilder:
                          (Widget child, Animation<double> animation) {
                        return ScaleTransition(scale: animation, child: child);
                      },
                      child: _isSearching
                          ? CustomSearchBar(
                              width: 200,
                              height: 60.h,
                              controller: recipeSearchController,
                              onClear: () {
                                fetchRecipe(); // Refresh recipes when search is cleared
                                setState(() {
                                  _isSearching = false;
                                });
                              },
                            )
                          : IconButton(
                              icon: Icon(
                                Icons.search,
                                size: 24,
                                color: const Color.fromARGB(255, 147, 147, 147),
                              ),
                              onPressed: () {
                                setState(() {
                                  _isSearching = true;
                                });
                              },
                              tooltip: 'Search Recipes',
                            ),
                    ),
                  ),
                if (!isSmallScreen)
                  CustomSearchBar(
                    width: 440.w,
                    height: 60.h,
                    controller: recipeSearchController,
                    onClear: () {
                      fetchRecipe(); // Refresh recipes when search is cleared
                    },
                  ),
                if (isSmallScreen)
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: IconButton(
                      icon: Icon(
                        Icons.filter_list,
                        size: 24,
                        color: const Color.fromARGB(
                            255, 147, 147, 147), // set the color if needed
                      ),
                      onPressed: () async {
                        final selectedFilters =
                            await showModalBottomSheet<Map<String, String>>(
                          context: context,
                          isScrollControlled: false,
                          constraints: BoxConstraints(
                            maxWidth: MediaQuery.of(context).size.width > 800
                                ? 800
                                : double.infinity,
                          ),
                          shape: const RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.vertical(top: Radius.circular(16)),
                          ),
                          builder: (context) => Padding(
                            padding: EdgeInsets.only(
                              bottom: MediaQuery.of(context).viewInsets.bottom,
                            ),
                            child: CookbookFilter(
                                initialFilters: _selectedFilters),
                          ),
                        );

                        if (selectedFilters != null) {
                          setState(() {
                            _selectedFilters = selectedFilters;
                            try {
                              final cuisineModel = ref
                                  .read(cuisinesNotifierProvider)
                                  .data
                                  ?.firstWhere((c) =>
                                      c.name == _selectedFilters['Cuisine']);
                              if (cuisineModel != null) {
                                cuisine = cuisineModel.id!;
                                ref
                                    .read(recipeMetadataProvider.notifier)
                                    .updateCuisineId(cuisineModel.id!);
                              }
                            } catch (e) {
                              // Handle case where no cuisine is found
                            }

                            try {
                              final categoryModel = ref
                                  .read(categoriesNotifierProvider)
                                  .data
                                  ?.firstWhere((c) =>
                                      c.name == _selectedFilters['Category']);
                              if (categoryModel != null) {
                                category = categoryModel.id!;
                                ref
                                    .read(recipeMetadataProvider.notifier)
                                    .updateCategoryId(categoryModel.id);
                              }
                            } catch (e) {
                              // Handle case where no category is found
                            }
                            sortOption = _selectedFilters['Sort'] ?? '';
                          });
                          fetchRecipe();
                        }
                      },
                      tooltip: 'Cookbooks',
                    ),
                  ),
                if (_isAnyFilterApplied)
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CustomButton(
                      text: "Clear Filter",
                      onPressed: () => onClearFiltersPressed(context),
                      fontSize:
                          DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
                      width: DeviceUtils().isTabletOrIpad(context) ? 90 : 200.w,
                    ),
                  ),
              ],
            ),
            drawer: isSmallScreen
                ? CustomDrawer(
                    title: 'Cookbooks',
                    state: cookbookState,
                    buildContent: _buildCookbookList,
                  )
                : null,
            body: Column(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      if (!isSmallScreen)
                        Expanded(
                          flex: 2,
                          child: _buildCookbookList(cookbookState),
                        ),
                      Expanded(
                        flex: isSmallScreen ? 1 : 6,
                        child: Stack(
                          fit: StackFit.expand,
                          children: [
                            Image.asset(
                              AssetsManager.background_img,
                              fit: BoxFit.cover,
                            ),
                            cookbookState.status == AppStatus.loading
                                ? RecipeShimmer(
                                    crossAxisCount: crossAxisCount,
                                    itemsPerPage:
                                        DeviceUtils().isTabletOrIpad(context)
                                            ? 9
                                            : 8,
                                  )
                                : cookbookState.status == AppStatus.error
                                    ? Center(
                                        child: Text(
                                            'Error: ${cookbookState.errorMessage}'))
                                    : cookbookState.data == null ||
                                            cookbookState.data!.isEmpty ||
                                            _selectedCookbookIndex >=
                                                cookbookState.data!.length
                                        ? const Center(
                                            child: NoDataWidget(
                                            title: "No Cookbook Found Details",
                                            subtitle:
                                                "Try searching again or select a different cookbook to view the details.",
                                            width: 250,
                                            height: 250,
                                          ))
                                        : recipeState.status ==
                                                AppStatus.loading
                                            ? RecipeShimmer(
                                                crossAxisCount: crossAxisCount,
                                                itemsPerPage: DeviceUtils()
                                                        .isTabletOrIpad(context)
                                                    ? 9
                                                    : 8,
                                              )
                                            : recipeState.status ==
                                                    AppStatus.error
                                                ? Center(
                                                    child: Text(
                                                        'Error: ${recipeState.errorMessage}'))
                                                : PaginatedGridView(
                                                    crossAxisCount:
                                                        crossAxisCount,
                                                    aspectRatio: 0.78,
                                                    selectedCookbook: cookbookState
                                                            .data![
                                                        _selectedCookbookIndex],
                                                    selectedCookbookIndex:
                                                        _selectedCookbookIndex,
                                                    cuisineId: cuisine,
                                                    categoryId: category,
                                                    searchQuery:
                                                        recipeSearchController
                                                            .text
                                                            .trim(),
                                                    showAddRecipeCard: true,
                                                    currentPage: 1,
                                                    onPageChanged:
                                                        (pageNumber) {},
                                                  ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
  }

  Future<void> onClearFiltersPressed(BuildContext context) async {
    setState(() {
      cuisine = 0;
      category = 0;
      _selectedFilters['Cuisine'] = '';
      _selectedFilters['Category'] = '';
      _selectedFilters['Sort'] = 'Sort By';
    });

    recipeSearchController.clear();
    cookbookSearchController.clear();

    ref.read(recipeMetadataProvider.notifier).resetFilters();
    bool isSuccess =
        await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
              context: context,
              loadMore: false,
            );

    if (isSuccess) {
      final cookbookState = ref.read(cookbookNotifierProvider);
      if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
        // Reset to first cookbook when clearing filters
        cookbookId = cookbookState.data![0].id;
        _updateSelectedCookbookIndex(cookbookState);
        fetchRecipe();
      } else {
        cookbookId = 0;
      }
    } else {
      cookbookId = 0;
    }
  }

  void _updateSelectedCookbookIndex(dynamic cookbookState) {
    setState(() {
      if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
        final index =
            cookbookState.data!.indexWhere((cb) => cb.id == cookbookId);
        if (index != -1) {
          // Found the target cookbook, update the index and preserve the cookbook info
          _selectedCookbookIndex = index;
          // Only update cookbookName if it wasn't already set from route parameters
          if (cookbookName.isEmpty) {
            cookbookName = cookbookState.data![_selectedCookbookIndex].name;
          }
          debugPrint(
              'Found target cookbook: index=$_selectedCookbookIndex, cookbookId=$cookbookId, cookbookName=$cookbookName');
          // Move the selected cookbook to the top of the list
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _reorderCookbookListWithSelectedFirst(_selectedCookbookIndex);
          });
        } else {
          // Target cookbook not found, search for it by name
          _searchForTargetCookbook();
        }
      } else {
        debugPrint('No cookbooks found in state');
        _selectedCookbookIndex = 0;
      }
    });
  }

  Future<void> _searchForTargetCookbookSync() async {
    debugPrint(
        'Searching for target cookbook (id: $cookbookId, name: $cookbookName)...');

    if (cookbookName.isNotEmpty && mounted) {
      try {
        // Strategy: Try to load multiple pages to find the target cookbook
        // If not found after 3 pages (30 cookbooks), fall back to search
        bool foundInPages = false;
        int maxPagesToCheck = 3;

        // First, load the first page
        bool firstPageSuccess = await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
              context: context,
              loadMore: false,
              search: '', // Get all cookbooks, not search results
            );

        if (firstPageSuccess && mounted) {
          final cookbookState = ref.read(cookbookNotifierProvider);
          int targetIndex = cookbookState.data?.indexWhere((cb) => cb.id == cookbookId) ?? -1;

          if (targetIndex != -1) {
            // Found in first page
            foundInPages = true;
            setState(() {
              _selectedCookbookIndex = targetIndex;
            });
            debugPrint('Found target cookbook in first page: index=$targetIndex');
            fetchRecipe();
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _reorderCookbookListWithSelectedFirst(targetIndex);
            });
          } else if (cookbookState.hasMore) {
            // Try loading more pages
            debugPrint('Target cookbook not in first page, loading more pages...');

            for (int page = 2; page <= maxPagesToCheck && cookbookState.hasMore && !foundInPages && mounted; page++) {
              bool loadMoreSuccess = await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                    context: context,
                    loadMore: true,
                  );

              if (loadMoreSuccess && mounted) {
                final updatedState = ref.read(cookbookNotifierProvider);
                targetIndex = updatedState.data?.indexWhere((cb) => cb.id == cookbookId) ?? -1;

                if (targetIndex != -1) {
                  foundInPages = true;
                  setState(() {
                    _selectedCookbookIndex = targetIndex;
                  });
                  debugPrint('Found target cookbook in page $page: index=$targetIndex');
                  fetchRecipe();
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _reorderCookbookListWithSelectedFirst(targetIndex);
                  });
                  break;
                }
              }
            }
          }

          // If still not found in pages, fall back to search
          if (!foundInPages && mounted) {
            debugPrint('Target cookbook not found in first $maxPagesToCheck pages, falling back to search...');

            bool searchSuccess = await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                  context: context,
                  loadMore: false,
                  search: cookbookName,
                );

            if (searchSuccess && mounted) {
              final searchState = ref.read(cookbookNotifierProvider);
              final searchIndex = searchState.data?.indexWhere((cb) => cb.id == cookbookId) ?? -1;

              if (searchIndex != -1) {
                setState(() {
                  _selectedCookbookIndex = searchIndex;
                });
                debugPrint('Found target cookbook in search results: index=$searchIndex');
                fetchRecipe();
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _reorderCookbookListWithSelectedFirst(searchIndex);
                });
              } else {
                // Still not found, default to first cookbook
                setState(() {
                  _selectedCookbookIndex = 0;
                });
                debugPrint('Target cookbook not found even in search results, defaulting to first cookbook');
                if (searchState.data != null && searchState.data!.isNotEmpty) {
                  fetchRecipe();
                }
              }
            }
          }
        }
      } catch (e) {
        debugPrint('Error searching for target cookbook: $e');
        setState(() {
          _selectedCookbookIndex = 0;
        });
      }
    } else {
      setState(() {
        _selectedCookbookIndex = 0;
      });
    }
  }

  Future<void> _findTargetCookbookAcrossPages() async {
    if (!mounted) return;
    try {
      final initialState = ref.read(cookbookNotifierProvider);
      int targetIndex = initialState.data?.indexWhere((cb) => cb.id == cookbookId) ?? -1;
      if (targetIndex != -1) {
        setState(() {
          _selectedCookbookIndex = targetIndex;
        });
        fetchRecipe();
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _reorderCookbookListWithSelectedFirst(targetIndex);
        });
        return;
      }

      // Try loading more pages up to a limit
      int pagesTried = 0;
      while (mounted) {
        var currentState = ref.read(cookbookNotifierProvider);
        if (!currentState.hasMore || pagesTried >= 4) {
          break;
        }
        pagesTried++;
        bool loadMoreSuccess = await ref
            .read(cookbookNotifierProvider.notifier)
            .fetchCookbooks(context: context, loadMore: true);
        if (loadMoreSuccess && mounted) {
          currentState = ref.read(cookbookNotifierProvider);
          targetIndex = currentState.data?.indexWhere((cb) => cb.id == cookbookId) ?? -1;
          if (targetIndex != -1) {
            setState(() {
              _selectedCookbookIndex = targetIndex;
            });
            fetchRecipe();
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _reorderCookbookListWithSelectedFirst(targetIndex);
            });
            return;
          }
        } else {
          break;
        }
      }

      // If still not found, perform search as a fallback
      await _searchForTargetCookbookSync();
    } catch (e) {
      debugPrint('Error while finding target cookbook across pages: $e');
    }
  }



  void _searchForTargetCookbook() async {
    await _searchForTargetCookbookSync();
  }

  void fetchRecipe() {
    ref.read(recipeNotifierProvider.notifier).fetchRecipes(
          cookbookId: cookbookId,
          cuisineId: cuisine,
          categoryId: category,
          reset: true,
          context: context,
          search: recipeSearchController.text.trim(),
          sort: Utils().convertSortToApi(_selectedFilters['Sort']!),
          currentPage: 1,
        );
  }

  /// Reorder the cookbook list to put the selected cookbook at the first position
  void _reorderCookbookListWithSelectedFirst(int selectedIndex) {
    // Use the notifier method to reorder the list
    ref.read(cookbookNotifierProvider.notifier).reorderWithSelectedFirst(selectedIndex);

    // Update the selected index to 0 since the selected cookbook is now at the top
    setState(() {
      _selectedCookbookIndex = 0;
    });
  }

  Widget _buildCookbookList(dynamic cookbookState) {
    return Column(
      children: [
        SizedBox(height: 10),
        CustomSearchBar(
          width: DeviceUtils().isTabletOrIpad(context)
              ? 368
              : MediaQuery.of(context).size.width,
          controller: cookbookSearchController,
          onClear: () async {
            bool isSuccess = await ref
                .read(cookbookNotifierProvider.notifier)
                .fetchCookbooks(
                  context: context,
                  loadMore: false,
                  search: '',
                );
            if (isSuccess) {
              final cookbookState = ref.read(cookbookNotifierProvider);
              if (cookbookState.data != null &&
                  cookbookState.data!.isNotEmpty) {
                // Try to find the target cookbook first, otherwise use first cookbook
                final targetIndex = cookbookState.data!.indexWhere((cb) => cb.id == cookbookId);
                if (targetIndex != -1) {
                  _selectedCookbookIndex = targetIndex;
                } else {
                  _selectedCookbookIndex = 0;
                  // Only update cookbookId if we don't have a target cookbook
                  if (cookbookId == 0) {
                    cookbookId = cookbookState.data![0].id;
                    cookbookName = cookbookState.data![0].name;
                  }
                }
                setState(() {});
                fetchRecipe();
              }
            }
          },
        ),
        Expanded(
          child: cookbookState.status == AppStatus.loading
              ? const CookbookListShimmer(
                  itemCount: 20,
                )
              : cookbookState.status == AppStatus.error
                  ? Center(child: Text('Error: ${cookbookState.errorMessage}'))
                  : cookbookState.data == null || cookbookState.data!.isEmpty
                      ? const Center(
                          child: NoDataWidget(
                          title: "No Cookbook Found",
                          subtitle:
                              "Try adjusting your search terms or create a new cookbook list",
                          width: 250,
                          height: 250,
                        ))
                      : ListView.separated(
                          controller: _scrollController,
                          padding: const EdgeInsets.all(12),
                          itemCount: cookbookState.data!.length +
                              (cookbookState.hasMore ? 1 : 0),
                          separatorBuilder: (_, __) =>
                              const SizedBox(height: 12),
                          itemBuilder: (context, index) {
                            if (index >= cookbookState.data!.length) {
                              return const Center(
                                  child: CircularProgressIndicator());
                            }
                            final cookbook = cookbookState.data![index];
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectedCookbookIndex = index;
                                  cookbookId = cookbook.id;
                                  cookbookName = cookbookState
                                      .data![_selectedCookbookIndex].name;
                                });
                                fetchRecipe();
                                if (MediaQuery.of(context).size.width <= 1100) {
                                  Navigator.of(context).pop();
                                }
                              },
                              child: CookbookDetailCard(
                                cookbook: cookbook,
                                isSelected: _selectedCookbookIndex == index,
                                onMenuItemSelected: (value) async {
                                  if (value == 'Edit') {
                                    ref
                                        .read(cookbookNotifierProvider.notifier)
                                        .resetToIdle();
                                    showUpdateCookbookDialog(
                                      context,
                                      ref,
                                      cookbookId: cookbook.id.toString(),
                                      cookbookName: cookbook.name,
                                    );
                                  } else if (value == 'Delete') {
                                    final cookbookNotifier = ref.read(
                                        cookbookNotifierProvider.notifier);
                                    final isDeleting = cookbookState.status ==
                                        AppStatus.loading;
                                    if (!isDeleting) {
                                      var isSuccess =
                                          await cookbookNotifier.deleteCookbook(
                                              context, cookbook.id.toString());

                                      if (isSuccess) {
                                        setState(() {
                                          if (cookbookState.data!.isNotEmpty) {
                                            cookbookId =
                                                cookbookState.data![0].id;
                                            cookbookName =
                                                cookbookState.data![0].name;
                                            _selectedCookbookIndex = 0;
                                          } else {
                                            cookbookId = 0;
                                            cookbookName = '';
                                            _selectedCookbookIndex = 0;
                                          }
                                        });
                                        fetchRecipe();
                                      }
                                    }
                                  }
                                },
                              ),
                            );
                          },
                        ),
        ),
      ],
    );
  }
}
