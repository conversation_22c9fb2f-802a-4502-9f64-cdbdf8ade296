import 'dart:async';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/helpers/Mz2Parser.dart';
import 'package:mastercookai/core/providers/recipe_notifier.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/widgets/custom_searchbar.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/quota_exhausted_dialog.dart';
import 'package:mastercookai/core/widgets/quota_exhausted_dialog.dart';
import 'package:mastercookai/core/widgets/recipe_quota_exhausted_dialog.dart';
import 'package:mastercookai/presentation/cookbook/mobile/mobile_cookbook_screen.dart';
import 'package:mastercookai/presentation/cookbook/widgets/CookbookDialog.dart';
import 'package:mastercookai/presentation/cookbook/widgets/cookbook_card.dart';
import 'package:mastercookai/presentation/cookbook/widgets/import_create_card.dart';
import 'package:mastercookai/presentation/cookbook/widgets/update_cookbook_dialog.dart';
import '../../core/network/app_status.dart';
import '../../core/providers/cookbook_notifier.dart';
import '../../core/providers/categories_notifier.dart';
import '../../core/providers/cuisines_notifier.dart';
import '../../core/services/file_picker_service.dart';
import '../../core/utils/device_utils.dart';
import '../../core/widgets/common_paginated_grid_view.dart';
import '../../core/widgets/pagination_indicator.dart';
import '../shimer/cookbook_shimmer.dart';
import '../../core/data/partner_api.dart';
import 'package:path/path.dart' as p;
import 'package:mastercookai/core/utils/Utils.dart';

class CookbookScreen extends ConsumerStatefulWidget {
  const CookbookScreen({super.key});

  @override
  ConsumerState<CookbookScreen> createState() => _CookbookScreenState();
}

class _CookbookScreenState extends ConsumerState<CookbookScreen> {
  final PageController _pageController = PageController();
  final TextEditingController searchController = TextEditingController();
  int _currentPage = 0;
  bool _showLoader = false;

  // Search functionality variables
  Timer? _debounceTimer;
  String _currentSearchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startLoadingDelayed();
      Future.delayed(const Duration(milliseconds: 100), () {
        ref
            .read(cookbookNotifierProvider.notifier)
            .fetchCookbooks(context: context, sort: 'Newest');
      });

      ref
          .read(categoriesNotifierProvider.notifier)
          .fetchCategories(context: context);
      ref
          .read(cuisinesNotifierProvider.notifier)
          .fetchCuisines(context: context);
    });
  }

  void _startLoadingDelayed() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted &&
          ref.read(cookbookNotifierProvider).status == AppStatus.loading) {
        setState(() {
          _showLoader = true;
        });
      }
    });
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query != _currentSearchQuery) {
        setState(() {
          _currentSearchQuery = query;
        });
        _performSearch(query);
      }
    });
  }

  void _performSearch(String query) {
    ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
        context: context, search: query.isEmpty ? null : query, sort: 'Newest');
  }

  @override
  void dispose() {
    _pageController.dispose();
    searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;
    final cookbookState = ref.watch(cookbookNotifierProvider);

    // Standardized crossAxisCount for all cases
    final crossAxisCount = DeviceUtils().isTabletOrIpad(context)
        ? 3
        : isHighRes
            ? 5
            : screenSize.width > 600
                ? 5
                : 2;

    return getDeviceType(context).name == 'mobile'
        ? MobileCookbookScreen()
        : WillPopScope(
            onWillPop: () async {
              if (_currentPage > 0) {
                _pageController.previousPage(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
                return false;
              }
              return false;
            },
            child: Scaffold(
              body: Stack(
                fit: StackFit.expand,
                children: [
                  Image.asset(
                    AssetsManager.background_img,
                    fit: BoxFit.cover,
                  ),
                  if (cookbookState.status == AppStatus.loading)
                    CookbookShimmer(
                      crossAxisCount: crossAxisCount,
                      itemsPerPage: crossAxisCount * 2,
                    )
                  else
                    Column(
                      children: [
                        SizedBox(height: 40.h),
                        if ((cookbookState.data?.isNotEmpty ?? false) ||
                            _currentSearchQuery.isNotEmpty)
                          CustomSearchBar(
                            controller: searchController,
                            width: DeviceUtils().isTabletOrIpad(context)
                                ? 250
                                : 500.w,
                            onChanged: _onSearchChanged,
                          ),
                        if ((cookbookState.data?.isNotEmpty ?? false))
                          SizedBox(height: 80.h),
                        Expanded(
                          child: Container(
                            margin: EdgeInsets.symmetric(
                                horizontal: screenSize.width * 0.05),
                            child: _buildMainContent(cookbookState, screenSize,
                                isHighRes, crossAxisCount),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          );
  }

  Widget _buildMainContent(AppState<List<Cookbook>> state, Size screenSize,
      bool isHighRes, int crossAxisCount) {
    if (state.status == AppStatus.loading && (state.data?.isEmpty ?? true)) {
      return const SizedBox.shrink();
    }

    if (state.status == AppStatus.error && (state.data?.isEmpty ?? true)) {
      return Center(
          child: Text('Error: ${state.errorMessage ?? "An error occurred"}'));
    }

    final itemsPerPage = crossAxisCount * 2;
    final totalItems = (state.data?.length ?? 0) + 1; // +1 for create card
    final totalPages = (totalItems / itemsPerPage).ceil();

    return NotificationListener<ScrollNotification>(
      onNotification: (scrollNotification) {
        if (scrollNotification is ScrollEndNotification) {
          final metrics = scrollNotification.metrics;
          if (metrics.pixels >= metrics.maxScrollExtent - 200 &&
              state.hasMore &&
              state.status != AppStatus.loadingMore) {
            ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                context: context,
                loadMore: true,
                search:
                    _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
                sort: 'Newest');
          }
        }
        return false;
      },
      child: Stack(
        children: [
          CommonPaginatedGridView(
            pageController: _pageController,
            state: state,
            totalPages: totalPages,
            totalItems: totalItems + (state.hasMore ? 1 : 0),
            // Add 1 for loader if hasMore
            itemsPerPage: itemsPerPage,
            crossAxisCount: crossAxisCount,
            currentSearchQuery: searchController.text.trim(),
            isHighRes: isHighRes,
            ref: ref,
            context: context,

            onPageChanged: (int page) {
              setState(() {
                _currentPage = page;
              });
              if (page == totalPages - 1 && state.hasMore) {
                ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                    loadMore: true,
                    context: context,
                    search: _currentSearchQuery.isEmpty
                        ? null
                        : _currentSearchQuery,
                    sort: 'Newest');
              }
            },
            itemBuilder: (BuildContext context, int index, int itemIndex) {
              // If index is beyond data length + create card, show loader only once
              if (itemIndex == totalItems &&
                  state.hasMore &&
                  state.status == AppStatus.loadingMore) {
                return Container(
                  height: 200.0, // Match CookbookCard height
                  alignment: Alignment.center,
                  child: LoadingAnimationWidget.fallingDot(
                    color: Colors.white,
                    size: 50.0,
                  ),
                );
              }

              // If index is beyond valid items, return empty widget
              if (itemIndex >= totalItems) {
                return const SizedBox.shrink();
              }
              // Create New Cookbook card
              if (itemIndex == 0) {
                return ImportCreateCard(
                  title: "Create New Cookbook",
                  importText: "Import Mz2 File",
                  isRecipe: false,
                  onImport: () async {
                    final pickFile =
                        ref.read(filePickerServiceProvider.notifier);
                    final file = await pickFile.pickMz2File();
                    if (file != null && context.mounted) {
                      // Get user profile for validations
                      final userProfileNotifier =
                          ref.read(userProfileNotifierProvider.notifier);

                      // 1. Check file size validation
                      final fileSizeInBytes = await file.length();
                      final fileSizeInMB = fileSizeInBytes / (1024 * 1024);
                      final recipeStorageInMB =
                          userProfileNotifier.recipeStorageInMB;
                      // 2. Read bytes and parse mz2 archive to check recipe count
                      final bytes = await file.readAsBytes();
                      final result = await parseMz2Bytes(bytes);
                      final totalRecipesInFile = result.recipes.length;
                      final recipeQuota = userProfileNotifier.recipeQuota;
                      if (fileSizeInMB > recipeStorageInMB) {
                        if (context.mounted) {
                          showQuotaExhaustedDialog(
                            context,
                            recipeStorageInMB: '$recipeStorageInMB MB',
                            fileSizeInMB: fileSizeInMB.toStringAsFixed(1),
                          );
                        }
                        return;
                      }
                      // 3. Check recipe quota validation
                      if (totalRecipesInFile > recipeQuota) {
                        if (context.mounted) {
                          final result = await showRecipeQuotaExhaustedDialog(
                            context,
                            totalRecipes: totalRecipesInFile,
                            remainingRecipes: recipeQuota,
                          );
                          if (result == "proceed") {
                            // User chose to proceed with limited recipes - continue with the upload process
                          } else if (result == "skip") {
                            return;
                          } else {
                            return;
                          }
                        } else {
                          return;
                        }
                      }

                      // 4. Create a new cookbook first using the picked file's name
                      final repo = ref.read(partnerApiProvider);
                      final cookbookName =
                          p.basenameWithoutExtension(file.path);
                      final createCbResp =
                          await repo.createCookbook(cookbookName);
                      final createdIdNum = createCbResp.data?.cookbookId;
                      if (createCbResp.success != true ||
                          createdIdNum == null) {
                        if (context.mounted) {
                          Utils().showFlushbar(
                            context,
                            message: createCbResp.message?.error?.first ??
                                'Failed to create cookbook before importing recipes',
                            isError: true,
                          );
                        }
                        return;
                      }
                      final cookbookId = createdIdNum.toInt();

                      // 5. Upload recipes (limited by quota if necessary)
                      final recipesToUpload = totalRecipesInFile > recipeQuota
                          ? result.recipes.take(recipeQuota).toList()
                          : result.recipes;

                      for (final recipe in recipesToUpload) {
                        final req = await recipeZToRequest(recipe);

                        //debugPrint("req.directionsJson: ${req.directionsJson}");
                        if (!context.mounted) break;
                        final success = await ref
                            .read(recipeNotifierProvider.notifier)
                            .createRecipe(
                                context: context,
                                request: req,
                                cookbookId: cookbookId,
                                goBack: false);

                        debugPrint(
                            "Recipe '${recipe.name}' upload status: $success");
                      }

                      if (!context.mounted) return;
                      await ref
                          .read(cookbookNotifierProvider.notifier)
                          .fetchCookbooks(context: context, sort: 'Newest');
                    }
                  },
                  //     async {
                  //   final pickFile =
                  //       ref.read(filePickerServiceProvider.notifier);
                  //   final file = await pickFile.pickMz2File();
                  //   if (file != null) {
                  //     var isSuccess = await ref
                  //         .read(cookbookNotifierProvider.notifier)
                  //         .importCookBook(context, file, 0, '');
                  //     if (isSuccess) {
                  //       await ref
                  //           .read(cookbookNotifierProvider.notifier)
                  //           .fetchCookbooks(context: context, sort: 'Newest');
                  //     }
                  //   }
                  // },
                  onCreate: () {
                    showCookbookDialog(context, ref);
                  },
                );
              }

              // Cookbook card
              final cookbookIndex = itemIndex - 1;
              if (cookbookIndex < (state.data?.length ?? 0)) {
                return CookbookCard(
                  cookbook: state.data![cookbookIndex],
                  isHighRes: isHighRes,
                  onTap: () {
                    ref.read(cookbookNotifierProvider.notifier).resetToIdle();
                    showUpdateCookbookDialog(
                      context,
                      ref,
                      cookbookId: state.data![cookbookIndex].id.toString(),
                      cookbookName: state.data![cookbookIndex].name,
                    );
                  },
                );
              }

              return const SizedBox.shrink();
            },
          ),
          SizedBox(height: 5),
          if (totalPages > 1)
            PaginationIndicator(
              totalPages: totalPages,
              currentPage: _currentPage,
              hasMore: state.hasMore,
              screenSize: screenSize,
              context: context,
            ),
        ],
      ),
    );
  }
}

void showCookbookDialog(BuildContext context, WidgetRef ref) {
  ref.read(cookbookNotifierProvider.notifier).resetToIdle();
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => CookbookDialog(
      title: "Create new cookbook",
      hintText: "Cookbook name",
      successText: "Cookbook created\nsuccessfully.",
      buttonText: "Create now",
      successButtonText: "Go to Cookbooks",
    ),
  );
}
