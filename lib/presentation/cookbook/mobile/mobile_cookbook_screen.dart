import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/data/models/view_type.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/no_data_widget.dart';
import 'package:mastercookai/core/widgets/paginated_responsive_grid_list.dart';
import 'package:mastercookai/presentation/cookbook/mobile/sub_views/cookbook_gridview_items.dart';
import 'package:mastercookai/presentation/cookbook/mobile/sub_views/cookbook_listview_items.dart';
import 'package:mastercookai/presentation/cookbook/mobile/sub_views/import_create_card_mobile.dart';
import 'package:mastercookai/presentation/cookbook/mobile/sub_views/import_create_card_mobile_list.dart';
import 'package:mastercookai/presentation/cookbook/widgets/update_cookbook_dialog.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../core/helpers/mz2_validation_helper.dart';
import '../../../core/helpers/local_storage_service.dart';
import '../../../core/helpers/permission_helper.dart';
import '../../../core/providers/categories_notifier.dart';
import '../../../core/providers/cookbook_notifier.dart';
import '../../../core/providers/cuisines_notifier.dart';
import '../../../core/services/file_picker_service.dart';
import '../../../core/widgets/custom_appbar_mobile.dart';
import '../../shimer/cookbook_shimmer.dart';
import '../../../core/helpers/Mz2Parser.dart';
import '../../../core/data/partner_api.dart';
import '../../../core/providers/recipe_notifier.dart';
import 'package:path/path.dart' as p;

// Define the ViewType provider
final viewTypeProvider = StateProvider<ViewType>((ref) => ViewType.grid);

class MobileCookbookScreen extends ConsumerStatefulWidget {
  const MobileCookbookScreen({super.key});

  @override
  ConsumerState<MobileCookbookScreen> createState() => _CookbookScreenState();
}

class _CookbookScreenState extends ConsumerState<MobileCookbookScreen> {
  final PageController _pageController = PageController();
  final TextEditingController searchController = TextEditingController();
  final int _currentPage = 0;

  // Search functionality variables
  Timer? _debounceTimer;
  String _currentSearchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      _startLoadingDelayed();
      Future.delayed(const Duration(milliseconds: 100), () {
        ref
            .read(cookbookNotifierProvider.notifier)
            .fetchCookbooks(context: context, sort: 'Newest');
      });

      ref
          .read(categoriesNotifierProvider.notifier)
          .fetchCategories(context: context);
      ref
          .read(cuisinesNotifierProvider.notifier)
          .fetchCuisines(context: context);
    });
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final localStorage = ref.watch(localStorageProvider);

      var callFrom = await localStorage.getcallFromAdd();
      if (callFrom == 'cookbooks') {
        // ref
        //     .read(cookbookNotifierProvider.notifier)
        //     .showCookbookBottomSheet(
        //   context: context,
        //   ref: ref,
        // );
      }
    });
  }

  void _startLoadingDelayed() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted &&
          ref.read(cookbookNotifierProvider).status == AppStatus.loading) {}
    });
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query != _currentSearchQuery) {
        setState(() {
          _currentSearchQuery = query;
        });
        _performSearch(query);
      }
    });
  }

  void _performSearch(String query) {
    ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
        context: context, search: query.isEmpty ? null : query, sort: 'Newest');
  }

  @override
  void dispose() {
    _pageController.dispose();
    searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cookbookState = ref.watch(cookbookNotifierProvider);
    final currentViewType = ref.watch(viewTypeProvider);
    final Size screenSize = MediaQuery.of(context).size;
    final bool isHighRes = screenSize.width > 600;

    return WillPopScope(
      onWillPop: () async {
        if (_currentPage > 0) {
          _pageController.previousPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          return false;
        }
        return true; // Allow back navigation if on first page
      },
      child: Scaffold(
        appBar: CustomAppbarMobile(
          title: 'Cookbooks',
          isLeading: false,
          onSearchChanged: (val) {
            _onSearchChanged(val);
          },
          onViewTypePressed: () {
            ref.read(viewTypeProvider.notifier).state =
                currentViewType == ViewType.grid
                    ? ViewType.list
                    : ViewType.grid;
          },
        ),
        body: Column(
          children: [
            Expanded(
              child: cookbookState.status == AppStatus.loading &&
                      (cookbookState.data?.isEmpty ?? true)
                  ? CookbookShimmer(
                      crossAxisCount: 2,
                      itemsPerPage: 10, // Match API page size
                    )
                  : _buildMainContent(cookbookState, isHighRes),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent(
    AppState<List<Cookbook>> state,
    bool isHighRes,
  ) {
    if (state.status == AppStatus.loading && (state.data?.isEmpty ?? true)) {
      return const SizedBox.shrink();
    }

    if (state.status == AppStatus.error && (state.data?.isEmpty ?? true)) {
      return const SizedBox.shrink();
    }

    final itemsPerPage = 10; // Fixed to match API's 10 items per page
    final currentViewType = ref.watch(viewTypeProvider);

    return NotificationListener<ScrollNotification>(
      onNotification: (scrollNotification) {
        if (scrollNotification is ScrollEndNotification) {
          final metrics = scrollNotification.metrics;
          if (metrics.pixels >= metrics.maxScrollExtent - 200 &&
              state.hasMore &&
              state.status != AppStatus.loadingMore) {
            ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                context: context,
                loadMore: true,
                search:
                    _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
                sort: 'Newest');
          }
        }
        return false;
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 16.0, bottom: 0.0),
        child: PaginatedResponsiveGridList<dynamic>(
          items: [
            // Add a placeholder for "Create New Cookbook" card at index 0
            'create_new_cookbook',
            // Add all actual cookbooks after the placeholder
            ...(state.data ?? [])
          ],
          itemBuilder: (item, index) {
            // Create New Cookbook card at index 0
            if (index == 0) {
              return currentViewType == ViewType.grid
                  ? ImportCreateCardMobile(
                      title: "Create New Cookbook",
                      importText: "Import Mz2 File",
                      isRecipe: false,
                      onImport: () async {
                        bool ok = await PermissionHelper.requestMultiple([
                          Permission.storage,
                        ]);
                        //  if(ok){
                        final pickFile =
                            ref.read(filePickerServiceProvider.notifier);
                        final file = await pickFile.pickMz2File();
                        if (file != null && context.mounted) {
                          // Validate file size and recipe count
                          final validationResult =
                              await Mz2ValidationHelper.validateMz2File(
                            file: file,
                            context: context,
                            ref: ref,
                          );

                          if (!validationResult.shouldProceed) {
                            return; // Stop if validation failed or user cancelled
                          }

                          // 3. Create a new cookbook first using the picked file's name
                          final repo = ref.read(partnerApiProvider);
                          final cookbookName =
                              p.basenameWithoutExtension(file.path);
                          final createCbResp =
                              await repo.createCookbook(cookbookName);
                          final createdIdNum = createCbResp.data?.cookbookId;
                          if (createCbResp.success != true ||
                              createdIdNum == null) {
                            if (context.mounted) {
                              Utils().showFlushbar(
                                context,
                                message: createCbResp.message?.error?.first ??
                                    'Failed to create cookbook before importing recipes',
                                isError: true,
                              );
                            }
                            return;
                          }
                          final cookbookId = createdIdNum.toInt();

                          // 4. Upload recipes (limited by quota if necessary)
                          final recipesToUpload =
                              validationResult.getRecipesToUpload();
                          for (final recipe in recipesToUpload) {
                            final req = await recipeZToRequest(recipe);

                            //debugPrint("req.directionsJson: ${req.directionsJson}");
                            if (!context.mounted) break;
                            final success = await ref
                                .read(recipeNotifierProvider.notifier)
                                .createRecipe(
                                    context: context,
                                    request: req,
                                    cookbookId: cookbookId,
                                    goBack: false);

                            debugPrint(
                                "Recipe '${recipe.name}' upload status: $success");
                          }

                          // 5. Show success message
                          if (context.mounted) {
                            Utils().showFlushbar(
                              context,
                              message: validationResult.getSuccessMessage(),
                              isError: false,
                            );
                          }

                          // 6. Refresh cookbooks list
                          if (!context.mounted) return;
                          await ref
                              .read(cookbookNotifierProvider.notifier)
                              .fetchCookbooks(context: context, sort: 'Newest');
                        }
                        // }else{
                        //   Utils().showFlushbar(context, message: '⚠️ Storage  permissions Missing!',isError: true);
                        //   print("⚠️ Missing permissions, user redirected to settings if blocked");
                        // }
                      },
                      onCreate: () {
                        ref
                            .read(cookbookNotifierProvider.notifier)
                            .showCookbookBottomSheet(
                              context: context,
                              ref: ref,
                            );
                      },
                    )
                  : ImportCreateCardMobileList(
                      title: "Create New Cookbook",
                      importText: "Import Mz2 File",
                      isRecipe: false,
                      onImport: () async {
                        final pickFile =
                            ref.read(filePickerServiceProvider.notifier);
                        final file = await pickFile.pickMz2File();
                        if (file != null && context.mounted) {
                          // Validate file size and recipe count
                          final validationResult =
                              await Mz2ValidationHelper.validateMz2File(
                            file: file,
                            context: context,
                            ref: ref,
                          );

                          if (!validationResult.shouldProceed) {
                            return; // Stop if validation failed or user cancelled
                          }

                          // 3. Create a new cookbook first using the picked file's name
                          final repo = ref.read(partnerApiProvider);
                          final cookbookName =
                              p.basenameWithoutExtension(file.path);
                          final createCbResp =
                              await repo.createCookbook(cookbookName);
                          final createdIdNum = createCbResp.data?.cookbookId;
                          if (createCbResp.success != true ||
                              createdIdNum == null) {
                            if (context.mounted) {
                              Utils().showFlushbar(
                                context,
                                message: createCbResp.message?.error?.first ??
                                    'Failed to create cookbook before importing recipes',
                                isError: true,
                              );
                            }
                            return;
                          }
                          final cookbookId = createdIdNum.toInt();

                          // 4. Upload recipes (limited by quota if necessary)
                          final recipesToUpload =
                              validationResult.getRecipesToUpload();
                          for (final recipe in recipesToUpload) {
                            final req = await recipeZToRequest(recipe);

                            //debugPrint("req.directionsJson: ${req.directionsJson}");
                            if (!context.mounted) break;
                            final success = await ref
                                .read(recipeNotifierProvider.notifier)
                                .createRecipe(
                                    context: context,
                                    request: req,
                                    cookbookId: cookbookId,
                                    goBack: false);

                            debugPrint(
                                "Recipe '${recipe.name}' upload status: $success");
                          }

                          // 5. Show success message
                          if (context.mounted) {
                            Utils().showFlushbar(
                              context,
                              message: validationResult.getSuccessMessage(),
                              isError: false,
                            );
                          }

                          // 6. Refresh cookbooks list
                          if (!context.mounted) return;
                          await ref
                              .read(cookbookNotifierProvider.notifier)
                              .fetchCookbooks(context: context, sort: 'Newest');
                        }
                      },
                      onCreate: () {
                        ref
                            .read(cookbookNotifierProvider.notifier)
                            .showCookbookBottomSheet(
                              context: context,
                              ref: ref,
                            );
                      },
                    );
            }

            // Cookbook cards (index 1 and onwards)
            final cookbook = item as Cookbook;
            return currentViewType == ViewType.grid
                ? CookbookGridviewItems(
                    cookbook: cookbook,
                    isHighRes: isHighRes,
                    onTap: () {
                      ref.read(cookbookNotifierProvider.notifier).resetToIdle();
                      ref
                          .read(cookbookNotifierProvider.notifier)
                          .showCookbookBottomSheet(
                            context: context,
                            ref: ref,
                            callFromUpdate: true,
                            cookbookId: cookbook.id.toString(),
                            cookbookName: cookbook.name,
                          );
                    },
                  )
                : CookbookListviewItems(
                    cookbook: cookbook,
                    isHighRes: isHighRes,
                    onTap: () {
                      ref.read(cookbookNotifierProvider.notifier).resetToIdle();
                      showUpdateCookbookDialog(
                        context,
                        ref,
                        cookbookId: cookbook.id.toString(),
                        cookbookName: cookbook.name,
                      );
                    },
                  );
          },
          desiredItemWidth: 164,
          minSpacing: 10,
          pageSize: itemsPerPage,
          viewType: currentViewType,
          onLoadMore: () {
            if (state.hasMore && state.status != AppStatus.loadingMore) {
              ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                  context: context,
                  loadMore: true,
                  search:
                      _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
                  sort: 'Newest');
            }
          },
        ),
      ),
    );
  }
}
