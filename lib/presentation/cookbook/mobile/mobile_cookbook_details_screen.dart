import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/data/models/view_type.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_drawer.dart';
import 'package:mastercookai/core/widgets/custom_searchbar.dart';
import 'package:mastercookai/presentation/cookbook/mobile/sub_views/import_create_card_mobile.dart';
import 'package:mastercookai/presentation/cookbook/mobile/sub_views/recipe_grid_item.dart';
import 'package:mastercookai/presentation/cookbook/widgets/cookbook_filter.dart';
import 'package:mastercookai/presentation/cookbook/widgets/update_cookbook_dialog.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_appbar.dart';
import '../../../core/data/models/recipe_response.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/categories_notifier.dart';
import '../../../core/providers/cookbook_notifier.dart';
import '../../../core/providers/cuisines_notifier.dart';
import '../../../core/providers/menus/bottom_nav_notifier.dart';
import '../../../core/providers/recipe/directions_provider.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/providers/recipe/step_Provider.dart';
import '../../../core/providers/recipe_notifier.dart';
import '../../../core/services/file_picker_service.dart';
import '../../../core/widgets/no_data_widget.dart';
import '../../../core/widgets/paginated_responsive_grid_list.dart';
import '../../shimer/cookbook_list_shimmer.dart';
import '../../shimer/recipe_shimmer_mobile.dart';
import '../widgets/cookbook_details_card.dart';
import '../../../core/helpers/Mz2Parser.dart';
import '../../../core/helpers/mz2_validation_helper.dart';

class MobileCookbookDetailsScreen extends ConsumerStatefulWidget {
  final int? selectedCookbookId; // Optional cookbook ID from route
  final String? selectedCookbookName; // Optional cookbook name from route
  final int? cuisineId; // Optional cuisine ID for filtering
  final int? categoryId; // Optional category ID for filtering
  final String? searchQuery; // Optional search query

  const MobileCookbookDetailsScreen({
    super.key,
    this.selectedCookbookId,
    this.selectedCookbookName,
    this.cuisineId,
    this.categoryId,
    this.searchQuery,
  });

  @override
  ConsumerState<MobileCookbookDetailsScreen> createState() =>
      _CookbookDetailsScreenState();
}

class _CookbookDetailsScreenState
    extends ConsumerState<MobileCookbookDetailsScreen> {
  bool _isSearching = false;
  TextEditingController cookbookSearchController = TextEditingController();
  TextEditingController recipeSearchController = TextEditingController();
  int totalPages = 0;
  int _selectedCookbookIndex = 0;
  int cuisine = 0;
  int category = 0;
  int cookbookId = 0;
  String cookbookName = '';
  String sortOption = ''; // Default sort option
  String _currentSearchQuery = ''; // Track current recipe search query
  final ScrollController _scrollController = ScrollController();
  Timer? _cookbookSearchDebounce;
  Timer? _recipeSearchDebounce;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  final Map<String, List<String>> _filterOptions = {
    "Cuisine": [],
    "Category": [],
    "Sort": ['Name (A-Z)', 'Name (Z-A)', 'Most Recent', 'Oldest First'],
  };

  Map<String, String> _selectedFilters = {
    "Cuisine": "",
    "Category": "",
    "Sort": "Sort By",
  };

  @override
  void initState() {
    super.initState();
    // Set bottom navigation to Cookbooks tab (index 1)

    _scrollController.addListener(_scrollListener);
    recipeSearchController.addListener(_onSearchChanged);
    cookbookSearchController.addListener(_onCookbookSearchChanged);

    // Initialize from widget parameters or route
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref.read(bottomNavProvider.notifier).onTabSelected(1);
      final id = widget.selectedCookbookId?.toString() ??
          GoRouterState.of(context).uri.queryParameters['id'];
      final name = widget.selectedCookbookName ??
          GoRouterState.of(context).uri.queryParameters['name'];

      if (id == null || name == null) {
        debugPrint(
            'Invalid id or name in route parameters: id=$id, name=$name');
        return;
      }

      cookbookId = int.parse(id);
      cookbookName = name;

      final cookbookState = ref.read(cookbookNotifierProvider);
      if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
        final index =
            cookbookState.data!.indexWhere((cb) => cb.id == cookbookId);
        setState(() {
          _selectedCookbookIndex = index != -1 ? index : 0;
          cookbookId = cookbookState.data![_selectedCookbookIndex].id;
          cookbookName = cookbookState.data![_selectedCookbookIndex].name;
        });
        fetchRecipe();
      } else {
        debugPrint('No cookbooks found in state');
      }

      // Fetch filter options
      ref
          .read(categoriesNotifierProvider.notifier)
          .fetchCategories(context: context);
      ref
          .read(cuisinesNotifierProvider.notifier)
          .fetchCuisines(context: context);

      final cuisines = ref.read(cuisinesNotifierProvider).data ?? [];
      _filterOptions['Cuisine'] = cuisines.map((c) => c.name ?? '').toList();

      final categories = ref.read(categoriesNotifierProvider).data ?? [];
      _filterOptions['Category'] = categories.map((c) => c.name ?? '').toList();

      // Initialize search query if provided
      if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty) {
        recipeSearchController.text = widget.searchQuery!;
        _currentSearchQuery = widget.searchQuery!;
        fetchRecipe();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    cookbookSearchController.dispose();
    recipeSearchController.dispose();
    _scrollController.dispose();
    _recipeSearchDebounce?.cancel();
    _cookbookSearchDebounce?.cancel();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final cookbookState = ref.read(cookbookNotifierProvider);
      if (cookbookState.hasMore &&
          cookbookState.status != AppStatus.loadingMore) {
        ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
              loadMore: true,
              context: context,
              search: cookbookSearchController.text.trim().isEmpty
                  ? null
                  : cookbookSearchController.text.trim(),
            );
      }
    }
  }

  void _onSearchChanged() {
    final text = recipeSearchController.text.trim();
    _currentSearchQuery = text;
    // Always trigger search for any text change (including character deletion)
    if (text.length >= 3 ||
        text.isEmpty ||
        text.length < _currentSearchQuery.length) {
      _debounceSearch(text);
    }
  }

  void _onCookbookSearchChanged() {
    final text = cookbookSearchController.text.trim();
    // Always trigger search for any text change (including character deletion)
    if (text.length >= 3 ||
        text.isEmpty ||
        text.length < cookbookSearchController.text.length) {
      _debounceCookbookSearch(text);
    }
  }

  void _debounceCookbookSearch(String query) {
    if (_cookbookSearchDebounce?.isActive ?? false) {
      _cookbookSearchDebounce!.cancel();
    }
    _cookbookSearchDebounce =
        Timer(const Duration(milliseconds: 500), () async {
      bool isSuccess =
          await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                context: context,
                loadMore: false,
                search: query.isEmpty ? null : query,
              );
      if (isSuccess) {
        final cookbookState = ref.read(cookbookNotifierProvider);
        if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
          setState(() {
            cookbookId = cookbookState.data![0].id;
            cookbookName = cookbookState.data![0].name;
            _selectedCookbookIndex = 0;
          });
          fetchRecipe();
        } else {
          setState(() {
            cookbookId = 0;
            cookbookName = '';
          });
        }
      } else {
        setState(() {
          cookbookId = 0;
          cookbookName = '';
        });
      }
    });
  }

  void _debounceSearch(String query) {
    if (_recipeSearchDebounce?.isActive ?? false) {
      _recipeSearchDebounce!.cancel();
    }
    _recipeSearchDebounce = Timer(const Duration(milliseconds: 500), () {
      fetchRecipe();
    });
  }

  bool get _isAnyFilterApplied {
    final hasCuisineFilter = _selectedFilters['Cuisine']?.isNotEmpty ?? false;
    final hasCategoryFilter = _selectedFilters['Category']?.isNotEmpty ?? false;
    final hasSortFilter = _selectedFilters['Sort'] != 'Sort By';
    return hasCuisineFilter || hasCategoryFilter || hasSortFilter;
  }

  void fetchRecipe() {
    ref.read(recipeNotifierProvider.notifier).fetchRecipes(
          cookbookId: cookbookId,
          cuisineId: widget.cuisineId ?? cuisine,
          categoryId: widget.categoryId ?? category,
          reset: true,
          context: context,
          sort: Utils().convertSortToApi(_selectedFilters['Sort']!),
          search: _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
          currentPage: 1,
        );
  }

  Future<void> onClearFiltersPressed(BuildContext context) async {
    setState(() {
      cuisine = 0;
      category = 0;
      _selectedFilters['Cuisine'] = '';
      _selectedFilters['Category'] = '';
      _selectedFilters['Sort'] = 'Sort By';
      _currentSearchQuery = '';
      recipeSearchController.clear();
      cookbookSearchController.clear();
    });

    ref.read(recipeMetadataProvider.notifier).resetFilters();
    bool isSuccess =
        await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
              context: context,
              loadMore: false,
            );

    if (isSuccess) {
      final cookbookState = ref.read(cookbookNotifierProvider);
      if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
        setState(() {
          cookbookId = cookbookState.data![0].id;
          cookbookName = cookbookState.data![0].name;
          _selectedCookbookIndex = 0;
        });
        fetchRecipe();
      } else {
        setState(() {
          cookbookId = 0;
          cookbookName = '';
        });
      }
    } else {
      setState(() {
        cookbookId = 0;
        cookbookName = '';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final cookbookState = ref.watch(cookbookNotifierProvider);
    final recipeState = ref.watch(recipeNotifierProvider);

    return Scaffold(
        key: _scaffoldKey,
        onDrawerChanged: (isOpened) {
          if (!isOpened && cookbookSearchController.text.isNotEmpty) {
            onClearFiltersPressed(context);
          }
        },
        appBar: CustomAppBar(
          title: cookbookName.isEmpty ? 'Cookbooks' : cookbookName,
          showDrawerIcon: true,
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return ScaleTransition(scale: animation, child: child);
                },
                child: _isSearching
                    ? CustomSearchBar(
                        width: 190,
                        height: 72.h,
                        controller: recipeSearchController,
                        onClear: () {
                          setState(() {
                            _isSearching = false;
                            _currentSearchQuery = '';
                          });
                          fetchRecipe();
                        },
                      )
                    : IconButton(
                        icon: const Icon(
                          Icons.search,
                          size: 24,
                          color: Color.fromARGB(255, 147, 147, 147),
                        ),
                        onPressed: () {
                          setState(() {
                            _isSearching = true;
                          });
                        },
                        tooltip: 'Search Recipes',
                      ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: IconButton(
                icon: const Icon(
                  Icons.filter_list,
                  size: 24,
                  color: Color.fromARGB(255, 147, 147, 147),
                ),
                onPressed: () async {
                  final selectedFilters =
                      await showModalBottomSheet<Map<String, String>>(
                    context: context,
                    isScrollControlled: false,
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width > 800
                          ? 800
                          : double.infinity,
                    ),
                    shape: const RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16)),
                    ),
                    builder: (context) => Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom,
                      ),
                      child: CookbookFilter(initialFilters: _selectedFilters),
                    ),
                  );

                  if (selectedFilters != null) {
                    setState(() {
                      _selectedFilters = selectedFilters;
                      try {
                        final cuisineModel = ref
                            .read(cuisinesNotifierProvider)
                            .data
                            ?.firstWhere(
                                (c) => c.name == _selectedFilters['Cuisine']);
                        if (cuisineModel != null) {
                          cuisine = cuisineModel.id!;
                          ref
                              .read(recipeMetadataProvider.notifier)
                              .updateCuisineId(cuisineModel.id!);
                        }
                      } catch (e) {
                        // Handle case where no cuisine is found
                      }

                      try {
                        final categoryModel = ref
                            .read(categoriesNotifierProvider)
                            .data
                            ?.firstWhere(
                                (c) => c.name == _selectedFilters['Category']);
                        if (categoryModel != null) {
                          category = categoryModel.id!;
                          ref
                              .read(recipeMetadataProvider.notifier)
                              .updateCategoryId(categoryModel.id);
                        }
                      } catch (e) {
                        // Handle case where no category is found
                      }
                      sortOption = _selectedFilters['Sort'] ?? '';
                    });
                    fetchRecipe();
                  }
                },
                tooltip: 'Filter Recipes',
              ),
            ),
            if (_isAnyFilterApplied)
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: IconButton(
                  icon: const Icon(
                    Icons.filter_alt_sharp,
                    size: 24,
                    color: Color.fromARGB(255, 147, 147, 147),
                  ),
                  onPressed: () => onClearFiltersPressed(context),
                  tooltip: 'Filter Recipes',
                ),
              ),
          ],
        ),
        drawer: CustomDrawer(
          title: 'Cookbooks',
          state: cookbookState,
          buildContent: _buildCookbookList,
        ),
        body: recipeState.status == AppStatus.loading
            ? RecipeShimmerMobile(
                crossAxisCount: 3,
                itemsPerPage: 10,
              )
            : _buildMainContent(recipeState, cookbookState));
  }

  Widget _buildCookbookList(dynamic cookbookState) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: CustomSearchBar(
            height: 72.h,
            width: MediaQuery.of(context).size.width,
            controller: cookbookSearchController,
            onClear: () async {
              bool isSuccess = await ref
                  .read(cookbookNotifierProvider.notifier)
                  .fetchCookbooks(
                    context: context,
                    loadMore: false,
                    search: '',
                  );
              if (isSuccess) {
                final cookbookState = ref.read(cookbookNotifierProvider);
                if (cookbookState.data != null &&
                    cookbookState.data!.isNotEmpty) {
                  setState(() {
                    cookbookId = cookbookState.data![0].id;
                    cookbookName = cookbookState.data![0].name;
                    _selectedCookbookIndex = 0;
                  });
                  fetchRecipe();
                }
              }
            },
          ),
        ),
        Expanded(
          child: cookbookState.status == AppStatus.loading
              ? const CookbookListShimmer(
                  itemCount: 20,
                )
              : cookbookState.data == null ||
                      cookbookState.data!.isEmpty ||
                      cookbookState.status == AppStatus.error
                  ? const Center(
                      child: NoDataWidget(
                        title: "No Cookbook Found",
                        subtitle:
                            "Try adjusting your search terms or create a new cookbook",
                        width: 250,
                        height: 250,
                      ),
                    )
                  : ListView.separated(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(12),
                      itemCount: cookbookState.data!.length +
                          (cookbookState.hasMore ? 1 : 0),
                      separatorBuilder: (_, __) => const SizedBox(height: 12),
                      itemBuilder: (context, index) {
                        if (index >= cookbookState.data!.length) {
                          return const Center(
                              child: CircularProgressIndicator());
                        }
                        final cookbook = cookbookState.data![index];
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedCookbookIndex = index;
                              cookbookId = cookbook.id;
                              cookbookName = cookbook.name;
                            });
                            fetchRecipe();
                            if (MediaQuery.of(context).size.width <= 1100) {
                              Navigator.of(context).pop();
                            }
                          },
                          child: CookbookDetailCard(
                            cookbook: cookbook,
                            isSelected: _selectedCookbookIndex == index,
                            onMenuItemSelected: (value) async {
                              if (value == 'Edit') {
                                ref
                                    .read(cookbookNotifierProvider.notifier)
                                    .resetToIdle();
                                showUpdateCookbookDialog(
                                  context,
                                  ref,
                                  cookbookId: cookbook.id.toString(),
                                  cookbookName: cookbook.name,
                                );
                              } else if (value == 'Delete') {
                                final cookbookNotifier =
                                    ref.read(cookbookNotifierProvider.notifier);
                                final isDeleting =
                                    cookbookState.status == AppStatus.loading;
                                if (!isDeleting) {
                                  final isSuccess =
                                      await cookbookNotifier.deleteCookbook(
                                          context, cookbook.id.toString());
                                  if (isSuccess &&
                                      cookbookState.data!.isNotEmpty) {
                                    setState(() {
                                      cookbookId = cookbookState.data![0].id;
                                      cookbookName =
                                          cookbookState.data![0].name;
                                      _selectedCookbookIndex = 0;
                                    });
                                    fetchRecipe();
                                  }
                                }
                              }
                            },
                          ),
                        );
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildMainContent(
    AppState<List<Recipe>> state,
    AppState<List<Cookbook>> cookbookState,
  ) {
    // Create a new list with a placeholder for ImportCreateCardMobile
    final List<Recipe?> modifiedItems = [null, ...state.data ?? []];

    return NotificationListener<ScrollNotification>(
      onNotification: (scrollNotification) {
        if (scrollNotification is ScrollEndNotification) {
          final metrics = scrollNotification.metrics;
          if (metrics.pixels >= metrics.maxScrollExtent - 200 &&
              state.hasMore &&
              state.status != AppStatus.loadingMore) {
            ref.read(recipeNotifierProvider.notifier).fetchRecipes(
                  cookbookId: cookbookId,
                  cuisineId: widget.cuisineId ?? cuisine,
                  categoryId: widget.categoryId ?? category,
                  reset: false,
                  context: context,
                  sort: Utils().convertSortToApi(_selectedFilters['Sort']!),
                  search:
                      _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
                  currentPage: (state.data!.length ~/ 10) + 1,
                );
          }
        }
        return false;
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 16.0, bottom: 0.0),
        child: PaginatedResponsiveGridList<Recipe?>(
          items: modifiedItems,
          // Use modified list with placeholder
          itemBuilder: (item, index) {
            if (index == 0) {
              return ImportCreateCardMobile(
                height: 220,
                title: "Add New Recipe",
                importText: "Import Mz2 File",
                isAddRecipe: true,
                isRecipe: true,
                onAddFromUrl: () {
                  context.push('/cookbook/cookbookDetail/recipeClipper');
                },
                onImport: () async {
                  final cookbookNotifier =
                      ref.read(cookbookNotifierProvider.notifier);
                  final recipeNotifier =
                      ref.read(recipeNotifierProvider.notifier);
                  final filePicker =
                      ref.read(filePickerServiceProvider.notifier);
                  final file = await filePicker.pickMz2File();
                  if (file != null && context.mounted) {
                    // Validate file size and recipe count
                    final validationResult =
                        await Mz2ValidationHelper.validateMz2File(
                      file: file,
                      context: context,
                      ref: ref,
                    );

                    if (!validationResult.shouldProceed) {
                      return; // Stop if validation failed or user cancelled
                    }

                    // 3. Skip cookbook creation since we already have cookbook ID and name
                    if (cookbookId == 0) {
                      if (context.mounted) {
                        Utils().showFlushbar(
                          context,
                          message: 'No cookbook selected for importing recipes',
                          isError: true,
                        );
                      }
                      return;
                    }

                    // 4. Upload recipes (limited by quota if necessary)
                    final recipesToUpload =
                        validationResult.getRecipesToUpload();
                    final totalRecipes = recipesToUpload.length;
                    final fileName = file.path.split('/').last;

                    // Show initial progress
                    Mz2ValidationHelper.showUploadProgress(
                      fileName: fileName,
                      cookBookName: cookbookName ?? '',
                      totalRecipes: totalRecipes,
                      uploadedRecipes: 0,
                    );

                    int uploadedCount = 0;
                    for (final recipe in recipesToUpload) {
                      final req = await recipeZToRequest(recipe);

                      //debugPrint("req.directionsJson: ${req.directionsJson}");
                      if (!context.mounted) break;

                      // Create recipe without showing default loader
                      final success = await recipeNotifier.createRecipe(
                          context: context,
                          request: req,
                          cookbookId: cookbookId,
                          goBack: false,
                          hideLoader: true);

                      if (success) {
                        uploadedCount++;
                        // Update progress without recreating dialog
                        Mz2ValidationHelper.updateUploadProgress(
                          fileName: fileName,
                          cookBookName: cookbookName,
                          totalRecipes: totalRecipes,
                          uploadedRecipes: uploadedCount,
                        );
                      }

                      debugPrint(
                          "Recipe '${recipe.name}' upload status: $success");
                    }

                    // Hide progress dialog
                    Mz2ValidationHelper.hideUploadProgress();

                    // 5. Refresh cookbook details and recipes after all uploads are complete
                    if (!context.mounted) return;
                    final isCookBookSuccess =
                        await cookbookNotifier.fetchCookbooks(context: context);
                    if (isCookBookSuccess && context.mounted) {
                      fetchRecipe(); // Refresh the current cookbook's recipes
                    }
                  }
                },
                onCreate: () async {
                  if (cookbookId == 0) {
                    Utils().showFlushbar(context,
                        message: 'No cookbook selected', isError: true);
                    return;
                  }

                  final result = await context.push(
                    '/cookbook/cookbookDetail/addRecipe',
                    extra: cookbookState.data![_selectedCookbookIndex],
                  );
                  if (result != null && context.mounted) {
                    ref.read(recipeNotifierProvider.notifier).fetchRecipes(
                          cookbookId: cookbookId,
                          cuisineId: widget.cuisineId ?? cuisine,
                          categoryId: widget.categoryId ?? category,
                          search: _currentSearchQuery.isEmpty
                              ? null
                              : _currentSearchQuery,
                          currentPage: 1,
                          reset: true,
                          context: context,
                        );
                  }
                },
              );
            }

            // Adjust index to map to state.data
            final recipeIndex = index - 1;
            if (recipeIndex < (state.data?.length ?? 0)) {
              final recipe = state.data![recipeIndex];
              return RecipeGridItem(
                recipe: recipe,
                cookbookId: cookbookId,
                onDeleted: () {
                  // Refresh the recipe list after deletion
                  fetchRecipe();
                  ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                        context: context,
                        loadMore: false,
                      );
                },
                onPressed: () async {
                  final result = await context.push(
                    '/cookbook/cookbookDetail/recipeDetail/${recipe.id}',
                    extra: {
                      'id': recipe.id,
                      'recipeList': state.data!,
                      'recipeName': recipe.name,
                      'cookbookId':
                          cookbookState.data![_selectedCookbookIndex].id,
                      'cookBookName':
                          cookbookState.data![_selectedCookbookIndex].name,
                    },
                  );
                  if (result != null && context.mounted) {
                    ref.read(recipeNotifierProvider.notifier).fetchRecipes(
                          cookbookId:
                              cookbookState.data![_selectedCookbookIndex].id ??
                                  0,
                          cuisineId: widget.cuisineId,
                          categoryId: widget.categoryId,
                          search: widget.searchQuery,
                          currentPage: 1,
                          reset: true,
                          context: context,
                        );
                    ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                          context: context,
                          loadMore: false,
                        );
                  }
                  debugPrint("Recipe Detail Result: $result");
                },
              );
            }
            return const SizedBox.shrink();
          },
          desiredItemWidth: 164,
          minSpacing: 10,
          pageSize: 10,
          viewType: ViewType.grid,
          onLoadMore: () {
            if (state.hasMore && state.status != AppStatus.loadingMore) {
              ref.read(recipeNotifierProvider.notifier).fetchRecipes(
                    cookbookId: cookbookId,
                    cuisineId: widget.cuisineId ?? cuisine,
                    categoryId: widget.categoryId ?? category,
                    reset: false,
                    context: context,
                    sort: Utils().convertSortToApi(_selectedFilters['Sort']!),
                    search: _currentSearchQuery.isEmpty
                        ? null
                        : _currentSearchQuery,
                    currentPage: (state.data!.length ~/ 10) + 1,
                  );
            }
          },
        ),
      ),
    );
  }
}
