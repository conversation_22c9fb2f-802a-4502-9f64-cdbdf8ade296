 
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_text_medium.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/cookbook_notifier.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:mastercookai/core/widgets/custom_hover_menu.dart';

import 'cookbook_gridview_items.dart' show selectedCookbookIdProvider;

class CookbookListviewItems extends ConsumerStatefulWidget {
  final Cookbook cookbook;
  final bool isHighRes;
  final VoidCallback onTap;

  const CookbookListviewItems({
    super.key,
    required this.cookbook,
    required this.isHighRes,
    required this.onTap,
  });

  @override
  ConsumerState<CookbookListviewItems> createState() =>
      _CookbookDetailCardState();
}

class _CookbookDetailCardState extends ConsumerState<CookbookListviewItems> {
  @override
  Widget build(BuildContext context) {
    final cookbookNotifier = ref.read(cookbookNotifierProvider.notifier);
    final cookbookState = ref.watch(cookbookNotifierProvider);
    final selectedCookbookId = ref.watch(selectedCookbookIdProvider);

    final isSelected = selectedCookbookId == widget.cookbook.id.toString();

    // Show loading indicator when deleting
    final isDeleting = cookbookState.status == AppStatus.loading;

    return GestureDetector(
      onTap: () {

        if (!isDeleting) {
          ref.read(selectedCookbookIdProvider.notifier).state = widget.cookbook.id.toString();
          // Delay navigation slightly to allow the border to render
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) {
              context.go('/cookbook/cookbookDetail?id=${widget.cookbook.id}&name=${Uri.encodeComponent(widget.cookbook.name)}');
            }
          });
        }
      },
      child: SizedBox(
          height: 100,
          child: Card(
            elevation: 3,
            color: context.theme.cardColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: isSelected
                  ? const BorderSide(color: Colors.blue, width: 2.0)
                  : BorderSide.none,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 5, vertical: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Image Section
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CommonImage(
                      imageSource: widget.cookbook.coverImageUrl,
                      placeholder: AssetsManager.cb_place_holder,
                      height: 90,
                      width: 90,
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(width: 12),
                  // Text and Action Section
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Title and icons row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: Text(
                                widget.cookbook.name,
                                style: context.theme.textTheme.bodyMedium!
                                    .copyWith(
                                  color: AppColors.primaryGreyColor,
                                  fontSize: 16, //responsiveFont(28).sp,
                                  fontWeight: FontWeight.w400,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            CustomHoverMenu(
                              items: ['Edit', 'Delete'],
                              itemIcons: [Icons.edit, Icons.delete],
                              onItemSelected: (value) {
                                if (value == 'Edit') {
                                  widget.onTap();
                                } else if (value == 'Delete') {
                                  if (!isDeleting) {
                                    cookbookNotifier.deleteCookbook(
                                        context, widget.cookbook.id.toString());
                                  }
                                }
                              },
                              menuWidth: 140.0,
                              menuTitle: 'Show Menu',
                              triggerIcon: Icons.more_vert_outlined,
                            ),
                          ],
                        ),
                        SizedBox(height: 30.h),
                        // Footer: recipes count and creation date
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            CustomTextMedium(
                              title: "${widget.cookbook.recipeCount} recipes",
                              size: 14, //responsiveFont(22).sp,
                              textColor: AppColors.textGreyColor,
                            ),
                            CustomTextMedium(
                              title:
                                  "Created: ${timeago.format(widget.cookbook.dateAdded, locale: 'en_short').replaceAll('just now ago', 'just now')}",
                              size: 14, //responsiveFont(22).sp,
                              textColor: AppColors.textGreyColor,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )),
    );
  }
}
