import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../../app/assets_manager.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/data/models/cookbook.dart';
import '../../../../core/network/app_status.dart';
import '../../../../core/providers/cookbook_notifier.dart';
import '../../../../core/widgets/camera_upload_image.dart';
import '../../../../core/widgets/common_image.dart';
import '../../../../core/widgets/custom_hover_menu.dart';
import '../../../../core/widgets/custom_text.dart';

// Define a StateProvider to manage the selected cookbook ID
final selectedCookbookIdProvider = StateProvider<String?>((ref) => null);

class CookbookGridviewItems extends ConsumerStatefulWidget {
  final Cookbook cookbook;
  final bool isHighRes;
  final VoidCallback onTap;

  const CookbookGridviewItems({
    super.key,
    required this.cookbook,
    required this.isHighRes,
    required this.onTap,
  });

  @override
  ConsumerState<CookbookGridviewItems> createState() =>
      _CookbookGridviewItemsState();
}

class _CookbookGridviewItemsState extends ConsumerState<CookbookGridviewItems> {
  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final cookbookNotifier = ref.read(cookbookNotifierProvider.notifier);
    final cookbookState = ref.watch(cookbookNotifierProvider);
    final selectedCookbookId = ref.watch(selectedCookbookIdProvider);

    // Show loading indicator when deleting
    final isDeleting = cookbookState.status == AppStatus.loading;
    final isSelected = selectedCookbookId == widget.cookbook.id.toString();

    return GestureDetector(
      onTap: () {
        if (!isDeleting) {
          ref.read(selectedCookbookIdProvider.notifier).state =
              widget.cookbook.id.toString();
          // Delay navigation slightly to allow the border to render
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) {
              context.go(
                  '/cookbook/cookbookDetail?id=${widget.cookbook.id}&name=${Uri.encodeComponent(widget.cookbook.name)}');
            }
          });
        }
      },
      child: SizedBox(
        height: 226,
        child: Card(
          color: context.theme.cardColor,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
            side: isSelected
                ? const BorderSide(color: Colors.blue, width: 2.0)
                : BorderSide.none,
          ),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(15),
                        bottom: Radius.circular(15),
                      ),
                      child: CommonImage(
                        imageSource: widget.cookbook.coverImageUrl,
                        placeholder: AssetsManager.cb_place_holder,
                        height: 133.0,
                        width: screenSize.width,
                        fit: BoxFit.cover,
                      ),
                    ),
                    cameraUploadImage(onTap: () {
                      cookbookNotifier.pickImage(context, ref,
                          widget.cookbook.id.toString(), widget.cookbook.name);
                    })
                  ],
                ),
                //const SizedBox(height: 10.0),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: CustomText(
                            text: widget.cookbook.name,
                            color: AppColors.primaryGreyColor,
                            size: 14,
                            weight: FontWeight.w500,
                            maxLine: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 15.0),

                        CustomHoverMenu(
                          items: ['Edit', 'Delete'],
                          itemIcons: [Icons.edit, Icons.delete],
                          onItemSelected: (value) {
                            if (value == 'Edit') {
                              widget.onTap();
                            } else if (value == 'Delete') {
                              if (!isDeleting) {
                                cookbookNotifier.deleteCookbook(
                                    context, widget.cookbook.id.toString());
                              }
                            }
                          },
                          menuWidth: 140.0,
                          menuTitle: 'Show Menu',
                          triggerIcon: Icons.more_vert_outlined,
                        ),
                      ],
                    ),
                   // const SizedBox(height: 6.0),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: '${widget.cookbook.recipeCount} recipes',
                          color: AppColors.textGreyColor,
                          size: 12.0,
                          weight: FontWeight.w400,
                        ),
                        CustomText(
                          text:
                              ' ${timeago.format(widget.cookbook.dateAdded, locale: 'en_short').replaceAll('just now ago', 'just now')}',
                          color: AppColors.textGreyColor,
                          size: 12.0,
                          weight: FontWeight.w400,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
