import 'dart:ui';

import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../recipe/subview/custom_sub_media_widget.dart';

class ImportCreateCardMobile extends StatelessWidget {
  final VoidCallback onImport;
  final VoidCallback? onAddFromUrl;
  final VoidCallback onCreate;
  final bool isRecipe;
  final double? height;
  final String title;
  final bool? isAddRecipe;
  final String importText;

  const ImportCreateCardMobile({
    super.key,
    required this.onImport,
    this.onAddFromUrl,
    this.height,
    required this.onCreate,
    required this.isRecipe,
    required this.title,
    this.isAddRecipe = false,
    required this.importText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 226,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Card(
        elevation: 5,
        color: context.theme.cardColor,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: Padding(
          padding: const EdgeInsets.all(6.0),
          child: CustomPaint(
            painter: DottedBorderPainter(),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: onImport,
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 3.0, vertical: 12.0),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.0),
                        border: Border.all(
                          color: AppColors.lightestGreyColor,
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(
                            AssetsManager.mynaui_file,
                            width: 24,
                            colorFilter:
                                ColorFilter.mode(Colors.red, BlendMode.srcIn),
                          ),
                          const SizedBox(width: 4.0),
                          CustomText(
                            text: importText,
                            color: AppColors.primaryGreyColor,
                            size: 12,
                            weight: FontWeight.w400,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16.0),
                  Visibility(
                    visible: isAddRecipe ?? false,
                    child: Column(
                      children: [
                        GestureDetector(
                          onTap: onAddFromUrl,
                          child: Container(
                            width: MediaQuery.of(context).size.width,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 2.0, vertical: 12.0),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10.0),
                              border: Border.all(
                                color: AppColors.lightestGreyColor,
                                width: 1.5,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SvgPicture.asset(
                                  AssetsManager.add_link,
                                  width: 24,
                                  colorFilter: ColorFilter.mode(
                                      Colors.red, BlendMode.srcIn),
                                ),
                                const SizedBox(width: 2.0),
                                CustomText(
                                  text: 'Import From URL',
                                  color: AppColors.primaryGreyColor,
                                  size: 12,
                                  weight: FontWeight.w400,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16.0),
                  CustomButton(
                    text: title,
                    fontSize: 12.0,
                    onPressed: onCreate,
                    borderRadius: 5.0,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
