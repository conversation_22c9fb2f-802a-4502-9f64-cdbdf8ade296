import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_popup_menu.dart';
import '../../../../../core/utils/device_utils.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_doted_lines.dart';
import '../../../../../core/widgets/custom_text_medium.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/data/models/recipe_response.dart';
import '../../../../core/helpers/date_formatter.dart';
import '../../../../core/widgets/custom_loading.dart';
import '../../../../main.dart';
import '../../../core/helpers/share_utils.dart';
import '../../../core/providers/recipe_notifier.dart';
import '../../../core/utils/screen_sizer.dart';
import '../../../core/widgets/common_image.dart';
import '../../../core/widgets/custom_hover_menu.dart';

class RecipeCard extends ConsumerWidget {
  final Recipe recipe;
  final VoidCallback onPressed;
  final int? cookbookId;

  const RecipeCard(
      {super.key,
      required this.recipe,
      required this.onPressed,
      this.cookbookId});

  /// Helper method to safely build recipe image with proper index handling
  Widget _buildRecipeImage(BuildContext context) {
    // Check if recipe media exists and is not empty
    if (recipe.recipeMedia == null || recipe.recipeMedia!.isEmpty) {
      return CommonImage(
        imageSource: AssetsManager.recipe_place_holder,
        width: double.infinity,
        height: _calculateMobileFriendlyImageHeight(context),
        fit: BoxFit.cover,
      );
    }

    // Calculate the safe index using the original logic
    int safeIndex;

    // Apply the original logic: if first media is IMAGE, subtract 1 from coverMediaIndex
    if (recipe.recipeMedia![0].mediaType == "IMAGE") {
      safeIndex = recipe.coverMediaIndex - 1;
    } else {
      safeIndex = recipe.coverMediaIndex;
    }

    // Ensure the calculated index is within bounds
    if (safeIndex < 0 || safeIndex >= recipe.recipeMedia!.length) {
      // If calculated index is out of bounds, use first available media
      safeIndex = 0;
    }

    return CommonImage(
      imageSource: recipe.recipeMedia![safeIndex].mediaUrl,
      placeholder: AssetsManager.recipe_place_holder,
      width: double.infinity,
      height: _calculateMobileFriendlyImageHeight(context),
      fit: BoxFit.cover,
    );
  }

  /// Helper method to calculate mobile-friendly image height
  double _calculateMobileFriendlyImageHeight(BuildContext context) {
    final size = MediaQuery.of(context).size;

    // Check if it's a mobile device (phone)
    if (size.width < 600) {
      // Mobile phones
      return size.height * 0.25; // 25% of screen height for mobile
    } else if (size.width < 1024) {
      // Tablets
      return size.height * 0.12; // 30% of screen height for tablets
    } else {
      // Desktop - use the existing ScreenSizer logic
      return ScreenSizer().calculateRecipeImageHeight(context);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenSize = MediaQuery.of(context).size;

    return Stack(
      children: [
        Card(
          color: context.theme.cardColor,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          clipBehavior: Clip.hardEdge,
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: screenSize.width * 0.0045,
                vertical: screenSize.width * 0.0045),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                /// Image with badge and bottom overlay
                Stack(
                  children: [
                    /// Recipe Image
                    ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        // child: CommonImage(
                        //   imageSource: recipe.recipeMedia![recipe.coverMediaIndex].mediaUrl ?? '',
                        //   placeholder: AssetsManager.recipe_place_holder,
                        //   // or File or asset path
                        //   width: double.infinity,
                        //   height:
                        //       ScreenSizer().calculateRecipeImageHeight(context),
                        //   fit: BoxFit.cover,
                        // )

                        // child: recipe.recipeMedia != null && recipe.recipeMedia!.isNotEmpty && recipe.coverMediaIndex != null && recipe.coverMediaIndex! < recipe.recipeMedia!.length
                        // ? CommonImage(
                        //     imageSource: recipe.recipeMedia![recipe.coverMediaIndex!].mediaUrl ?? '',
                        //     placeholder: AssetsManager.recipe_place_holder,
                        //     width: double.infinity,
                        //     height: ScreenSizer().calculateRecipeImageHeight(context),
                        //     fit: BoxFit.cover,
                        //   )
                        // : CommonImage(
                        //     imageSource: AssetsManager.recipe_place_holder,
                        //     width: double.infinity,
                        //     height: ScreenSizer().calculateRecipeImageHeight(context),
                        //     fit: BoxFit.cover,
                        //   ),

                        child: _buildRecipeImage(context),
                      ),

                    /// Bottom Overlay: Time & Persons
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.5),
                          borderRadius: const BorderRadius.vertical(
                              bottom: Radius.circular(12)),
                        ),
                        padding: EdgeInsets.symmetric(
                            horizontal: 14.w, vertical: 15.h),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            /// Time Section
                            Row(
                              children: [
                                SvgPicture.asset(
                                  AssetsManager.ic_time,
                                  height: 30.h,
                                  width: 30.w,
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  recipe.totalTime ?? '',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 18.sp,
                                    fontWeight: FontWeight.w300,
                                  ),
                                ),
                              ],
                            ),

                            /// Vertical Divider
                            Container(
                              width: 1.w,
                              height: 20.h,
                              color: Colors.white,
                            ),

                            /// Person Section
                            Row(
                              children: [
                                SvgPicture.asset(
                                  AssetsManager.dining,
                                  height: 30.h,
                                  width: 30.w,
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  "${recipe.servings} persons",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 18.sp,
                                    fontWeight: FontWeight.w300,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                /// Below image content
                SizedBox(height: 10.h),

                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        recipe.name,
                        style: context.theme.textTheme.bodyMedium!.copyWith(
                          color: AppColors.primaryGreyColor,
                          fontSize: 16,//responsiveFont(28).sp,
                          fontWeight: FontWeight.w400,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    CustomHoverMenu(
                      items: ['Delete'],
                      itemIcons: [Icons.delete],
                      // Icons for each item
                      onItemSelected: (value) async {
                        if (value == 'Delete') {
                          final loading = ref.read(loadingProvider.notifier);
                          final recipeNotifier =
                              ref.read(recipeNotifierProvider.notifier);

                          await recipeNotifier.deleteRecipe(
                              context: context,
                              cookbookId: cookbookId ?? 0,
                              recipeId: recipe.id);
                        }
                      },
                      menuWidth: 140.0,
                      // Matches previous menu width
                      menuTitle: 'Show Menu',
                      // Tooltip on hover
                      triggerIcon:
                          Icons.more_vert_outlined, // Custom trigger icon
                    ),
                  ],
                ),

                SizedBox(height: 1.h),

                DeviceUtils().isTabletOrIpad(context) ? CustomTextMedium(
                  title: recipe.description ?? '',
                  maxLines: 1,
                  size: 16,
                ) : Container(
                  height: 76.h, // Fixed height to accommodate 2 lines
                  child: CustomTextMedium(
                    title: recipe.description ?? '',
                    maxLines: 2,
                    size: 16,
                  ),
                ),

                SizedBox(height: 12.h),
                // Spacer(),
                CustomPaint(
                  painter: DottedLinePainter(
                    strokeWidth: 1,
                    dashWidth: 6,
                    color: AppColors.lightestGreyColor,
                  ),
                  size: Size(double.infinity, 2),
                ),
                Spacer(),
                Row(
                  children: [
                    SvgPicture.asset(
                      AssetsManager.rating,
                      height: 24.h,
                      width: 24.w,
                    ),
                    SizedBox(width: 4),
                    CustomTextMedium(
                        title: "${recipe.reviewsCount} ( reviews)",
                        size: 14,),
                    Spacer(),
                    CustomTextMedium(
                        title: DateFormatter.timeAgo(recipe.dateAdded!),
                        size: 14,),
                  ],
                ),

                //  SizedBox(height: 20.h),

                Spacer(),
                CustomButton(
                    fontSize: 14, text: 'View Recipe', onPressed: onPressed),
                Spacer(),
              ],
            ),
          ),
        ),

        /// Top Right SVG Badge
        // Positioned(
        //   top: 8.h,
        //   right: DeviceUtils().isTabletOrIpad(context) ? 14 : 20.w,
        //   child: SvgPicture.asset(
        //     AssetsManager.rewards, // your reward.svg asset path
        //     width: 50.w,
        //     height: 50.h,
        //   ),
        // ),
      ],
    );
  }
}
