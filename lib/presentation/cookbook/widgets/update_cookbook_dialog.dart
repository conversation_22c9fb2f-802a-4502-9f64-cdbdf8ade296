import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/presentation/cookbook/widgets/CookbookDialog.dart';

import '../../../app/imports/core_imports.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_loading.dart';

void showUpdateCookbookDialog(BuildContext context, WidgetRef ref,{String? cookbookId, String? cookbookName}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => CookbookDialog(
      title: "Edit Cookbook",
      hintText: "Edit Cookbook Name",
      successText: "Cookbook Updated \nSuccessfully",
      buttonText: "Update",
      successButtonText: "Done",
      cookbookId: cookbookId,
      cookbookName: cookbookName,
      callFromUpdate: true,
    ),
  ).then((_) {});
}



