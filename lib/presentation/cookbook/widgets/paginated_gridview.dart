import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/presentation/cookbook/widgets/import_create_card.dart';
import 'package:mastercookai/presentation/cookbook/widgets/recipe_cards.dart';
import 'package:mastercookai/presentation/cookbook/widgets/recipe_clipper_dialog.dart';
import 'package:paged_grid_view/paged_grid_view.dart';
import '../../../core/data/models/cookbook.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/cookbook_notifier.dart';
import '../../../core/providers/recipe_notifier.dart';
import '../../../core/services/file_picker_service.dart';
import '../../../core/helpers/Mz2Parser.dart';
import '../../../core/helpers/mz2_validation_helper.dart';

class PaginatedGridView extends ConsumerStatefulWidget {
  final int crossAxisCount;
  final double aspectRatio;
  final Cookbook? selectedCookbook;
  final int selectedCookbookIndex;
  final bool showAddRecipeCard;
  final int cuisineId;
  final int categoryId;
  final String? searchQuery;
  final int currentPage;
  final Function(int) onPageChanged;

  const PaginatedGridView({
    super.key,
    required this.crossAxisCount,
    required this.aspectRatio,
    required this.selectedCookbook,
    required this.selectedCookbookIndex,
    required this.showAddRecipeCard,
    required this.cuisineId,
    required this.categoryId,
    this.searchQuery,
    required this.currentPage,
    required this.onPageChanged,
  });

  @override
  _PaginatedGridViewState createState() => _PaginatedGridViewState();
}

class _PaginatedGridViewState extends ConsumerState<PaginatedGridView>
    with AutomaticKeepAliveClientMixin {
  bool _isLoading = false;
  int _currentPage = 1;
  final ScrollController _scrollController = ScrollController();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final recipeState = ref.read(recipeNotifierProvider);
      if (recipeState.data == null || recipeState.currentPage != _currentPage) {
        ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: widget.selectedCookbook?.id ?? 0,
            cuisineId: widget.cuisineId,
            categoryId: widget.categoryId,
            search: widget.searchQuery,
            currentPage: _currentPage,
            reset: true,
            context: context);
      } else {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
        }
      }
    });
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent * 0.9 &&
          !_isLoading) {
        _loadMoreItems();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant PaginatedGridView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.cuisineId != widget.cuisineId ||
        oldWidget.categoryId != widget.categoryId ||
        oldWidget.searchQuery != widget.searchQuery ||
        oldWidget.selectedCookbook?.id != widget.selectedCookbook?.id) {
      setState(() {
        _currentPage = 1;
      });
      ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: widget.selectedCookbook?.id ?? 0,
            cuisineId: widget.cuisineId,
            categoryId: widget.categoryId,
            search: widget.searchQuery,
            currentPage: 1,
            reset: true,
            context: context,
          );
    }
  }

  Future<void> _loadMoreItems() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    final recipeState = ref.read(recipeNotifierProvider);
    if (recipeState.hasMore && recipeState.status != AppStatus.loadingMore) {
      final nextPage = _currentPage + 1;
      debugPrint(
          'Fetching page: $nextPage, cookbookId: ${widget.selectedCookbook?.id}, cuisineId: ${widget.cuisineId}, categoryId: ${widget.categoryId}, search: ${widget.searchQuery}');
      await ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: widget.selectedCookbook?.id ?? 0,
            cuisineId: widget.cuisineId,
            categoryId: widget.categoryId,
            search: widget.searchQuery,
            currentPage: nextPage,
            reset: false,
            context: context,
          );
      setState(() {
        _currentPage = nextPage;
      });
    } else {
      debugPrint('No more pages to fetch: hasMore=${recipeState.hasMore}');
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final size = MediaQuery.of(context).size;
    final recipeState = ref.watch(recipeNotifierProvider);
    final recipeList = recipeState.data ?? [];
    final gridItemCount =
        recipeList.length + (widget.showAddRecipeCard ? 1 : 0);

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: PagedGridView.builder(
        controller: _scrollController,
        physics: const ClampingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics()),
        itemCount: gridItemCount + (_isLoading && recipeState.hasMore ? 1 : 0),
        onPageChanged: widget.onPageChanged,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.crossAxisCount,
          mainAxisSpacing: size.height * 0.002,
          crossAxisSpacing: size.width * 0.002,
          childAspectRatio: widget.aspectRatio,
        ),
        scrollDirection: Axis.vertical,
        itemBuilder: (context, index) {
          if (_isLoading && index == gridItemCount && recipeState.hasMore) {
            return const Center(child: CircularProgressIndicator());
          }

          if (widget.showAddRecipeCard && index == 0) {
            return ImportCreateCard(
              title: "Add Recipe Manually",
              importText: "Import Mz2 File",
              isAddRecipe: true,
              isRecipe: true,
              onAddFromUrl: () {
                showDialog(
                  context: context,
                  useRootNavigator: true,
                  barrierDismissible: false,
                  builder: (_) => RecipeClipperDialog(),
                );
              },
              onImport: () async {
                // Store notifier before async operation
                final cookbookNotifier =
                    ref.read(cookbookNotifierProvider.notifier);
                final recipeNotifier =
                    ref.read(recipeNotifierProvider.notifier);
                final filePicker = ref.read(filePickerServiceProvider.notifier);
                final file = await filePicker.pickMz2File();
                if (file != null && context.mounted) {
                  // Validate file size and recipe count
                  final validationResult =
                      await Mz2ValidationHelper.validateMz2File(
                    file: file,
                    context: context,
                    ref: ref,
                  );

                  if (!validationResult.shouldProceed) {
                    return; // Stop if validation failed or user cancelled
                  }

                  // 3. Skip cookbook creation since we already have cookbook ID and name
                  final cookbookId = widget.selectedCookbook?.id ?? 0;
                  if (cookbookId == 0) {
                    if (context.mounted) {
                      Utils().showFlushbar(
                        context,
                        message: 'No cookbook selected for importing recipes',
                        isError: true,
                      );
                    }
                    return;
                  }

                  // 4. Upload recipes (limited by quota if necessary)
                  final recipesToUpload = validationResult.getRecipesToUpload();
                  final totalRecipes = recipesToUpload.length;
                  final fileName = file.path.split('/').last;
                  final cookbookName = widget.selectedCookbook?.name ?? '';

                  // Show initial progress
                  Mz2ValidationHelper.showUploadProgress(
                    fileName: fileName,
                    cookBookName: cookbookName,
                    totalRecipes: totalRecipes,
                    uploadedRecipes: 0,
                  );

                  int uploadedCount = 0;
                  for (final recipe in recipesToUpload) {
                    final req = await recipeZToRequest(recipe);
                    //debugPrint("req.directionsJson: ${req.directionsJson}");
                    if (!context.mounted) break;

                    // Create recipe without showing default loader
                    final success =
                        await recipeNotifier.createRecipeWithoutLoader(
                            context: context,
                            request: req,
                            cookbookId: cookbookId,
                            goBack: false);

                    if (success) {
                      uploadedCount++;
                      // Update progress without recreating dialog
                      Mz2ValidationHelper.updateUploadProgress(
                        fileName: fileName,
                        cookBookName: cookbookName,
                        totalRecipes: totalRecipes,
                        uploadedRecipes: uploadedCount,
                      );
                    }

                    debugPrint(
                        "Recipe '${recipe.name}' upload status: $success");
                  }

                  // Hide progress dialog
                  Mz2ValidationHelper.hideUploadProgress();

                  // 5. Refresh cookbooks and recipes list
                  if (!context.mounted) return;
                  final isCookBookSuccess =
                      await cookbookNotifier.fetchCookbooks(context: context);
                  debugPrint("Cookbook fetch success: $isCookBookSuccess");
                  if (isCookBookSuccess && context.mounted) {
                    debugPrint(
                        "Cookbook fetch success: ${widget.selectedCookbook!.id} ${widget.cuisineId} ${widget.categoryId}");
                    await recipeNotifier.fetchRecipes(
                      cookbookId: widget.selectedCookbook!.id,
                      cuisineId: widget.cuisineId,
                      categoryId: widget.categoryId,
                      reset: true,
                      context: context,
                      currentPage: 1,
                    );
                  }
                }
              },
              onCreate: () async {
                if (widget.selectedCookbook == null) {
                  Utils().showFlushbar(context,
                      message: 'No cookbook selected', isError: true);
                  return;
                }
                final result = await context.push(
                  '/cookbook/cookbookDetail/addRecipe',
                  extra: widget.selectedCookbook,
                );
                if (result != null && context.mounted) {
                  ref.read(recipeNotifierProvider.notifier).fetchRecipes(
                        cookbookId: widget.selectedCookbook?.id ?? 0,
                        cuisineId: widget.cuisineId,
                        categoryId: widget.categoryId,
                        search: widget.searchQuery,
                        currentPage: 1,
                        reset: true,
                        context: context,
                      );
                }
                debugPrint("Add Recipe Result: $result");
              },
            );
          }

          final adjustedIndex = widget.showAddRecipeCard ? index - 1 : index;
          if (adjustedIndex >= recipeList.length) {
            return const SizedBox.shrink();
          }

          final recipe = recipeList[adjustedIndex];
          return RecipeCard(
            recipe: recipe,
            cookbookId: widget.selectedCookbook?.id,
            onPressed: () async {
              if (widget.selectedCookbook == null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('No cookbook selected')),
                );
                return;
              }
              final result = await context.push(
                '/cookbook/cookbookDetail/recipeDetail/${recipe.id}',
                extra: {
                  'id': recipe.id,
                  'recipeList': recipeList,
                  'recipeName': recipe.name,
                  'cookbookId': widget.selectedCookbook!.id,
                  'cookBookName': widget.selectedCookbook?.name,
                },
              );
              if (result != null && context.mounted) {
                ref.read(recipeNotifierProvider.notifier).fetchRecipes(
                      cookbookId: widget.selectedCookbook?.id ?? 0,
                      cuisineId: widget.cuisineId,
                      categoryId: widget.categoryId,
                      search: widget.searchQuery,
                      currentPage: 1,
                      reset: true,
                      context: context,
                    );
              }
              debugPrint("Recipe Detail Result: $result");
            },
          );
        },
      ),
    );
  }
}
