import 'package:mastercookai/app/imports/core_imports.dart';

class CustomDescText extends StatelessWidget {
  final String desc;
  final double? size;
  final Color? textColor;
  const CustomDescText({super.key, required this.desc, this.size , this.textColor});

  @override
  Widget build(BuildContext context) {
    return Text(desc,
        style: context.theme.textTheme.displaySmall!.copyWith(
          color: textColor ?? AppColors.primaryLightTextColor,
          fontWeight: FontWeight.w400,
          fontSize: size ?? headingThreeFontSize * MediaQuery.textScaleFactorOf(context),
        ));
  }
}
