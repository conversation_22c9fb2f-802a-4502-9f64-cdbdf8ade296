import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/text_styles.dart';
import 'package:mastercookai/app/theme/colors.dart';
import 'package:mastercookai/core/providers/categories_notifier.dart';
import 'package:mastercookai/core/providers/cuisines_notifier.dart';
import 'package:mastercookai/presentation/cookbook/widgets/expandable_filter.dart';

import '../../../app/assets_manager.dart';

class CookbookFilter extends ConsumerStatefulWidget {
  final Map<String, String> initialFilters;
  final bool showSort;

  const CookbookFilter(
      {super.key, required this.initialFilters, this.showSort = true});

  @override
  ConsumerState<CookbookFilter> createState() => _CookbookFilterState();
}

class _CookbookFilterState extends ConsumerState<CookbookFilter> {
  final Map<String, List<String>> _filterOptions = {
    "Sort": ['Name (A-Z)', 'Name (Z-A)', 'Most Recent', 'Oldest First'],
    "Cuisine": [],
    "Category": [],
  };

  late Map<String, String> _selectedFilters;

  @override
  void initState() {
    super.initState();
    _selectedFilters = Map.from(widget.initialFilters);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(categoriesNotifierProvider.notifier).fetchCategories(context: context);
      ref.read(cuisinesNotifierProvider.notifier).fetchCuisines(context: context);
    });
  }

  @override
  Widget build(BuildContext context) {
    final cuisines = ref.watch(cuisinesNotifierProvider).data ?? [];
    final categories = ref.watch(categoriesNotifierProvider).data ?? [];

    _filterOptions['Cuisine'] = [...cuisines.map((c) => c.name ?? '')];
    _filterOptions['Category'] = [...categories.map((c) => c.name ?? '')];

    final filterKeys = _filterOptions.keys.where((key) {
      if (key == 'Sort' && !widget.showSort) {
        return false;
      }
      return true;
    }).toList();

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
          child: Container(
            width: 60,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
        ),
        const SizedBox(height: 16), 
          Stack(
            children: [
              Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.filter_list,
                      size: 24,
                      color: Color.fromARGB(255, 110, 110, 110),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Filter',
                      style: globalTextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: Color.fromARGB(255, 110, 110, 110),
                      ),
                    ),

                  ],
                ),
              ),

              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  icon: SvgPicture.asset(
                    AssetsManager.cross,
                    height: 20,
                    width: 20,
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: filterKeys.map((filterName) {
                return ExpandableFilter(
                  filterName: filterName,
                  options: _filterOptions[filterName]!,
                  selectedOption: _selectedFilters[filterName] ?? '',
                  isInitiallyExpanded: filterName == 'Sort',
                  onOptionSelected: (option) {
                    setState(() {
                      _selectedFilters[filterName] = option;
                    });
                  },
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 24),
          Center(
            child: ElevatedButton(
              onPressed: () {
                // Apply filter logic here
                Navigator.pop(context, _selectedFilters);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Apply',
                style: globalTextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
