import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text_field.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/cookbook_notifier.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_input_field.dart';
import '../../../core/widgets/custom_text.dart';

// Assuming AppState and AppStatus are defined in a shared file
//final cookbookNameProvider = StateProvider<String>((ref) => '');

class CookbookDialog extends HookConsumerWidget {
  final String title;
  final String hintText;
  final String successText;
  final String buttonText;
  final String successButtonText;
  final bool callFromUpdate; // Default to false, can be set to true if needed
  final VoidCallback? onSuccess;
  final Function(String, BuildContext, WidgetRef)? onCreate;
  final String? cookbookId;
  final String? cookbookName;

  const CookbookDialog({
    super.key,
    required this.title,
    required this.hintText,
    required this.successText,
    required this.buttonText,
    required this.successButtonText,
    this.onSuccess,
    this.onCreate,
    this.cookbookId,
    this.cookbookName,
    this.callFromUpdate = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cookbookNotifier = ref.read(cookbookNotifierProvider.notifier);
    final appState = ref.watch(cookbookNotifierProvider);
    final nameController = useTextEditingController(text: cookbookName ?? '');
    final formKey = useMemoized(() => GlobalKey<FormState>());

    // Show SnackBar for errors
    ref.listen(cookbookNotifierProvider, (previous, next) {
      if (next.status == AppStatus.error && next.errorMessage != null) {
        Utils().showSnackBar(context, next.errorMessage!);
      }
    });

    return Dialog(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(26)),
      child: Container(
        padding: EdgeInsets.only(left: 25, right: 25, top: 20, bottom: 15),
        width: 500,
        child: appState.status ==
                (callFromUpdate
                    ? AppStatus.updateSuccess
                    : AppStatus.createSuccess)
            ? _buildSuccessContent(context, ref, successText, successButtonText)
            : buildFormContent(
                context, ref, nameController, formKey, cookbookNotifier),
      ),
    );
  }

  Widget buildFormContent(
    BuildContext context,
    WidgetRef ref,
    TextEditingController controller,
    GlobalKey<FormState> formKey,
    CookbookNotifier cookbookNotifier,
  ) {
    final appState = ref.watch(cookbookNotifierProvider);

    return Form(
      key: formKey,
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _closeButton(context, ref),
              CustomText(
                text: title,
                color: AppColors.blackTextColor,
                size: DeviceUtils().isTabletOrIpad(context) ? 24 : 24,
                weight: FontWeight.w700,
              ),
              SizedBox(height: 50),
              Stack(
                children: [
                  SizedBox(
                    width: DeviceUtils().isTabletOrIpad(context) ? 500 : 300,
                    child: Container(
                      padding: EdgeInsets.only(left: 20, right: 20),
                      child: CustomInputField(
                          hintText: hintText,
                          controller: controller,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter cookbook name';
                            }
                            if (value.trim().length < 3) {
                              return 'Cookbook name must be at least 3 characters';
                            }
                            if (value.trim().length > 100) {
                              return 'Cookbook name must not exceed 100 characters';
                            }
                            if (RegExp(r'\s{2,}').hasMatch(value)) {
                              return 'Cookbook name should not contain multiple spaces in a row';
                            }
                            if (value != value.trim()) {
                              return 'Cookbook name should not start or end with a space';
                            }
                            return null;
                          },
                          onChanged:
                              (val) {} //ref.read(cookbookNameProvider.notifier).state = val,
                          ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 50),
              CustomButton(
                width: DeviceUtils().isTabletOrIpad(context) ? 180 : 200,
                fontSize: DeviceUtils().isTabletOrIpad(context) ? 18 : 22,
                text: buttonText,
                isLoading: appState.status ==
                    (callFromUpdate
                        ? AppStatus.updating
                        : AppStatus.creating), // Bind to AppStatus.loading
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    if (onCreate != null) {
                      await onCreate!(controller.text.trim(), context, ref);
                    } else {
                      if (callFromUpdate) {
                        await cookbookNotifier.updateCookbook(
                            id: cookbookId!,
                            name: controller.text.trim(),
                            filePath: File(''),
                            context,
                            callFromUpdate: false);
                      } else {
                        await cookbookNotifier.createCookbook(
                            context, controller.text.trim());
                      }
                    }
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessContent(
    BuildContext context,
    WidgetRef ref,
    String successResponse,
    String buttonText,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
          alignment: Alignment.topRight,
          child: IconButton(
              icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
              onPressed: () {
                Navigator.of(context).pop();
                ref
                    .read(cookbookNotifierProvider.notifier)
                    .fetchCookbooks(context: context);
              }),
        ),
        //  _closeButton(context, ref),
        SvgPicture.asset(
          AssetsManager.success,
          height: 80,
          width: 80,
        ),
        SizedBox(height: 40),
        Center(
          child: CustomText(
            text: successResponse,
            color: AppColors.primaryGreyColor,
            size: DeviceUtils().isTabletOrIpad(context) ? 24 : 24,
            weight: FontWeight.w400,
            align: TextAlign.center,
          ),
        ),
        SizedBox(height: 40),
        CustomButton(
          width: DeviceUtils().isTabletOrIpad(context) ? 180 : 250,
          fontSize: DeviceUtils().isTabletOrIpad(context) ? 18 : 22,
          isLoading:
              ref.watch(cookbookNotifierProvider).status == AppStatus.loading,
          text: buttonText,
          onPressed: () async {
            Navigator.of(context).pop();
            ref
                .read(cookbookNotifierProvider.notifier)
                .fetchCookbooks(context: context);
          },
        ),
      ],
    );
  }

  Widget _closeButton(BuildContext context, WidgetRef ref) {
    return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
        onPressed: () {
          // Reset the cookbook notifier state when closing
          ref.read(cookbookNotifierProvider.notifier).resetToIdle();
          Navigator.of(context).pop();
        },
      ),
    );
  }
}
