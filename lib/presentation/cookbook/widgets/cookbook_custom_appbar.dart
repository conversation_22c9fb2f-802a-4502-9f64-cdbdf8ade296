import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_searchbar.dart';

class CookbookAppBar extends StatefulWidget {
  const CookbookAppBar({super.key});

  @override
  State<CookbookAppBar> createState() => _CookbookAppBarState();
}

class _CookbookAppBarState extends State<CookbookAppBar> {
  final Map<String, String> _selectedFilters = {
    "Cuisine": "Item One",
    "Ratings": "Item One",
    "Category": "Item One",
    "Preference": "Healthy",
  };

  final Map<String, List<String>> _filterOptions = {
    "Cuisine": [
      "Item One",
      "Item Two",
      "Item Three",
      "Item Four",
      "Item Five",
      "Item Six"
    ],
    "Ratings": [
      "Item One",
      "Item Two",
      "Item Three",
      "Item Four",
      "Item Five",
      "Item Six"
    ],
    "Category": [
      "Item One",
      "Item Two",
      "Item Three",
      "Item Four",
      "Item Five",
      "Item Six"
    ],
    "Preference": [
      "Healthy",
      "Veg",
      "Non-Veg",
      "High protein",
      "High calorie",
      "High fats"
    ],
  };

  @override
  Widget build(BuildContext context) {
    final TextEditingController searchController = TextEditingController();
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          GestureDetector(
              onTap: () {
                context.pop();
              },
              child: SvgPicture.asset(
                AssetsManager.back,
                height: 70.h,
                width: 70.w,
              )),
          SizedBox(width: 10.w),
          Text(
            'Cookbooks',
            style: context.theme.textTheme.bodyMedium!.copyWith(
              color: context.theme.hintColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Wrap(
            spacing: 8,
            children: _filterOptions.keys.map((filterName) {
              return _buildDropdownFilter(filterName);
            }).toList(),
          ),
          SizedBox(width: 12.w),
          CustomSearchBar(
            controller: searchController,
            width: 440.w,
            height: 50.h,
          )
        ],
      ),
    );
  }

  Widget _buildDropdownFilter(String filterKey) {
    final selected = _selectedFilters[filterKey]!;
    return PopupMenuButton<String>(
      color: Colors.white,
      onSelected: (value) {
        setState(() {
          _selectedFilters[filterKey] = value;
        });
      },
      itemBuilder: (context) {
        return _filterOptions[filterKey]!.map((option) {
          return PopupMenuItem<String>(
            value: option,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
              decoration: option == selected
                  ? BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(6),
                    )
                  : null,
              child: Text(
                option,
                style: TextStyle(
                  color: option == selected ? Colors.white : Colors.black,
                  fontWeight:
                      option == selected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          );
        }).toList();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        margin: EdgeInsets.only(left: 20.w, right: 20.w),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black38.withOpacity(.2)),
          color: AppColors.secondaryColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            Text(
              filterKey,
              style: context.theme.textTheme.labelSmall!.copyWith(
                color: context.theme.hintColor,
              ),
            ),
            SizedBox(
              width: 5.w,
            ),
            Container(
                margin: EdgeInsets.symmetric(horizontal: 5.w),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.black38.withOpacity(.2)),
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Icon(
                  Icons.keyboard_arrow_down,
                  size: 20.w,
                  color: AppColors.secondaryColor,
                )),
          ],
        ),
      ),
    );
  }
}
