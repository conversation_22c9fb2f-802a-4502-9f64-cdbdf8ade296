import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/utils/device_utils.dart';

import '../../../app/imports/core_imports.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../app/imports/core_imports.dart';

class DropdownFilter extends StatelessWidget {
  final String filterName;
  final Map<String, String> selectedFilters;
  final Map<String, List<String>> filterOptions;
  final Function(String, String) onFilterChanged;

  const DropdownFilter({
    Key? key,
    required this.filterName,
    required this.selectedFilters,
    required this.filterOptions,
    required this.onFilterChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final selectedValue = selectedFilters[filterName] ?? '';
    final options = filterOptions[filterName] ?? [];

    return PopupMenuButton<String>(
      color: Colors.white,
      onSelected: (value) {
        onFilterChanged(filterName, value);
      },
      itemBuilder: (context) {
        return options.map((option) {
          return PopupMenuItem<String>(
            value: option,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
              decoration: option == selectedValue
                  ? BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(6),
              )
                  : null,
              child: Text(
                option,
                style: TextStyle(
                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : context.theme.textTheme.titleMedium!.fontSize,
                  fontWeight: option == selectedValue ? FontWeight.w400 : FontWeight.w400,
                  color: option == selectedValue
                      ? Colors.white
                      : AppColors.primaryGreyColor,
                ),
              ),
            ),
          );
        }).toList();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        margin: EdgeInsets.only(left: 20.w, right: 20.w),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black38.withOpacity(.2)),
          color: AppColors.secondaryColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            Text(
              selectedValue.isNotEmpty ? selectedValue : filterName,
              style: TextStyle(
                fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : context.theme.textTheme.titleMedium!.fontSize,
                fontWeight: FontWeight.w400,
                color: selectedValue.isNotEmpty
                    ? Colors.black
                    : AppColors.primaryGreyColor,
              ),
            ),
            SizedBox(width: 5.w),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 5.w),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black38.withOpacity(.2)),
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(2),
              ),
              child: Icon(
                Icons.keyboard_arrow_down,
                size: DeviceUtils().isTabletOrIpad(context) ? 14 : 20.w,
                color: AppColors.secondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
