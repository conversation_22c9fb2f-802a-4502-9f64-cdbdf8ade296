import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../../core/widgets/custom_button.dart';

class ImportCreateCardEmptyList extends StatelessWidget {
  final VoidCallback onImport;
  final VoidCallback onCreate;
  final bool isRecipe;
  final String title;
  final String importText;

  const ImportCreateCardEmptyList(
      {super.key,
      required this.onImport,
      required this.onCreate,
      required this.isRecipe,
      required this.title,
      required this.importText});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Card(
        elevation: 0,
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: CustomPaint(
            painter: DottedBorderPainter(),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 50.h,
                ),
                // Import Button
                GestureDetector(
                  onTap: onImport,
                  child: Text(importText,
                      style: context.theme.textTheme.labelMedium!.copyWith(
                        //  color: context.theme.cardColor,
                        //fontSize: 8.sp,
                        fontSize: 28.sp,
                        fontWeight: FontWeight.normal,
                        decoration: TextDecoration.underline,
                        decorationColor: AppColors.lightGreyColor,
                        decorationThickness: 2.0,
                      )),
                ),
                SizedBox(
                  height: 20.h,
                ),
                Text(
                  'or',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                      ),
                ),

                SizedBox(
                  height: 20.h,
                ),
                Container(
                  margin: EdgeInsets.only(left: 25.w, right: 25.w),
                  child: CustomButton(
                      text: title, fontSize: 22.sp, onPressed: onCreate),

                ),
                SizedBox(
                  height: 50.h,
                ),
                // Create Button
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class DottedBorderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color =
          AppColors.backgroudInActiveColor // Use your preferred border color
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    const dashWidth = 4;
    const dashSpace = 4;
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final radius = Radius.circular(12);

    // Helper function to draw dotted lines
    void drawDottedLine(Offset start, Offset end) {
      final distance = (end - start).distance;
      final dashCount = (distance / (dashWidth + dashSpace)).floor();
      final path = Path();

      for (int i = 0; i < dashCount; i++) {
        final p = start +
            (end - start) * (i * (dashWidth.toDouble() + dashSpace)) / distance;
        path.moveTo(p.dx, p.dy);
        path.lineTo(
          p.dx + (end - start).dx * dashWidth / distance,
          p.dy + (end - start).dy * dashWidth / distance,
        );
      }

      canvas.drawPath(path, paint);
    }

    // Draw all four sides with dots
    // Top line
    drawDottedLine(
      Offset(radius.x, 0),
      Offset(size.width - radius.x, 0),
    );
    // Right line
    drawDottedLine(
      Offset(size.width, radius.y),
      Offset(size.width, size.height - radius.y),
    );
    // Bottom line
    drawDottedLine(
      Offset(size.width - radius.x, size.height),
      Offset(radius.x, size.height),
    );
    // Left line
    drawDottedLine(
      Offset(0, size.height - radius.y),
      Offset(0, radius.y),
    );

    // Draw rounded corners
    final cornerPath = Path()..addRRect(RRect.fromRectAndRadius(rect, radius));
    canvas.drawPath(cornerPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
