import 'package:flutter/material.dart';
import 'package:mastercookai/app/text_styles.dart';
import 'package:mastercookai/app/theme/colors.dart';

class ExpandableFilter extends StatefulWidget {
  final String filterName;
  final List<String> options;
  final String selectedOption;
  final Function(String) onOptionSelected;
  final bool isInitiallyExpanded;

  const ExpandableFilter({
    Key? key,
    required this.filterName,
    required this.options,
    required this.selectedOption,
    required this.onOptionSelected,
    this.isInitiallyExpanded = false,
  }) : super(key: key);

  @override
  _ExpandableFilterState createState() => _ExpandableFilterState();
}

class _ExpandableFilterState extends State<ExpandableFilter> {
  late bool _isExpanded;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.isInitiallyExpanded;
  }

  @override
  Widget build(BuildContext context) {
    bool showSelected = widget.selectedOption.isNotEmpty &&
        widget.selectedOption != 'All Cuisines' &&
        widget.selectedOption != 'All Categories';

    return ExpansionTile(
      initiallyExpanded: widget.isInitiallyExpanded,
      // title: Column(
      //   crossAxisAlignment: CrossAxisAlignment.start,
      //   children: [
      //     Text(
      //       widget.filterName,
      //       style: globalTextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      //     ),
      //     if (showSelected) ...[
      //       const SizedBox(height: 4),
      //       Text(
      //         widget.selectedOption,
      //         style: globalTextStyle(fontSize: 14, color: Colors.grey),
      //       ),
      //     ],
      //   ],
      // ),
      title: RichText(
        text: TextSpan(
          style: globalTextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          children: [
            TextSpan(text: widget.filterName),
            if (showSelected)
            TextSpan(
              text:  ' (${widget.selectedOption})',
              style: globalTextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),

      trailing: Icon(
        _isExpanded
            ? Icons.keyboard_arrow_up
            : Icons.keyboard_arrow_down,
        color: _isExpanded ? AppColors.primaryColor : null,
      ),
      onExpansionChanged: (bool expanded) {
        setState(() {
          _isExpanded = expanded;
        });
      },
      children: widget.options.map((option) {
        return RadioListTile<String>(
          title: Text(option),
          value: option,
          groupValue: widget.selectedOption,
          activeColor: AppColors.primaryColor,
          onChanged: (String? value) {
            if (value != null) {
              widget.onOptionSelected(value);
            }
          },
        );
      }).toList(),
    );
  }
}
