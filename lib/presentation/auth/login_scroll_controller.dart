
import '../../../app/imports/core_imports.dart';

class LoginScrollController extends ChangeNotifier {
  final ScrollController scrollController = ScrollController();
  late final AnimationController _animationController;

  LoginScrollController() {
    _init();
  }

  void _init() {
    _animationController = AnimationController(
      vsync: ScrollableState(), // Hack, or pass in a TickerProvider manually.
      duration: const Duration(seconds: 20),
    )
      ..addListener(() {
        if (scrollController.hasClients) {
          scrollController.jumpTo(
            _animationController.value * scrollController.position.maxScrollExtent,
          );
        }
      })
      ..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    scrollController.dispose();
    super.dispose();
  }
}
