import 'package:flutter/gestures.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/auth/controllers/auth_notifier.dart';

import '../../../core/utils/Utils.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/utils/validator.dart';

import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text_button.dart';
import '../dialogs/mobile_password_reset_dialog.dart';
import '../signup_screen.dart';


class MobileLoginScreen extends HookConsumerWidget {
  const MobileLoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);
    final isHovered = useState(false);

    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final formKeyForgot = useMemoized(() => GlobalKey<FormState>());

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Center(
            child: Row(
              children: [
                // Right Section
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(18),
                    child: Form(
                      key: formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(height: 100),
                          CustomText(
                            text:  "Welcome Back!",
                            color: AppColors.primaryGreyColor,
                            size: 24,
                            weight: FontWeight.w700,
                            fontFamily: 'Inter',

                          ),
                          SizedBox(height: 100.h),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 10),
                            child: Column(
                              children: [
                                CustomInputField(
                                  hintText: 'Username',
                                  // fontSize: DeviceUtils().isTabletOrIpad(context) ? 20 : responsiveFont(20).sp,
                                  controller: emailController,
                                  keyboardType: TextInputType.emailAddress,

                                  //autoFocus: true,
                                  validator: Validator.validateEmail,
                                ),
                                SizedBox(height: 20.h),
                                CustomInputField(
                                  hintText: 'Password',
                                  controller: passwordController,
                                  keyboardType: TextInputType.emailAddress,
                                  isPassword: true,
                                  validator: Validator.validatePassword,
                                ),
                                SizedBox(height: 20.h),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: CustomTextButton(
                                    text: 'Forgot Password?',
                                    size: 14,
                                    color: AppColors.primaryLightTextColor,
                                    onPressed: () {
                                      showPasswordResetBottomSheet(context,formKeyForgot:formKeyForgot);

                                    },
                                     underline: false,
                                    hoverColor: isHovered.value
                                        ? AppColors
                                            .darkPrimaryBackgroundColor
                                        : Colors.transparent,
                                  ),
                                ),
                                SizedBox(height: 50.h),
                                CustomButton(
                                  text: 'Log In',
                                  height: 30,
                                  weight: FontWeight.w600,
                                  isLoading:
                                      authState.status == AppStatus.loading,
                                  width: 200,
                                  borderRadius: 10,
                                  onPressed: () {
                                    if (formKey.currentState!.validate()) {
                                      Utils().hideKeyboardOnTap();
                                      authNotifier.authenticate(
                                        context,
                                        emailController.text.trim(),
                                        passwordController.text.trim(),
                                      );
                                    }
                                  },
                                ),
                              ],
                            ),
                          ),
                           // const Spacer(),
                          // CustomText(
                          //  text:  "Love MasterCook?",
                          //   color: AppColors.primaryLightTextColor,
                          //   size: 20,
                          //   weight: FontWeight.w600,
                          // ),
                          // SizedBox(height: 30.h),
                          // SizedBox(
                          //   height: 35,
                          //   width: 160,
                          //   child: CustomButton(
                          //     onPressed: () {
                          //       Utils().launchURL(
                          //           url: 'https://mastercook.com/');
                          //     },
                          //     text: "Visit Website",
                          //     borderRadius: 10,
                          //     fontFamily: 'Inter',
                          //     isUnderline: true,
                          //   ),
                          // ),
                          const Spacer(),
                          // SizedBox(height: 50),
                          RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: responsiveFont(18).sp,
                                height: 16 / 12,
                                letterSpacing: 0,
                              ),
                              children: [
                                TextSpan(
                                  text: "Don’t have an Account? ",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF4F4F4F),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                TextSpan(
                                  text: "Register now",
                                  style: TextStyle(
                                    fontSize:12,
                                    color: Color(0xFF007AFF),
                                    fontWeight: FontWeight.w400,
                                    decoration: TextDecoration.underline,
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                            const SignupScreen()),
                                      );
                                      // Utils().launchURL(
                                      //     url: 'https://mastercook.ai/');
                                    },
                                ),

                              ],

                            ),
                          ),
                          SizedBox(height: 50.h),

                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
