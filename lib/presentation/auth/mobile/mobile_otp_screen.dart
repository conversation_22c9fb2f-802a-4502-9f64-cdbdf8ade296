// // import 'package:flutter/material.dart';
// // import 'package:flutter_riverpod/flutter_riverpod.dart';
// // import 'package:go_router/go_router.dart';
// // import 'package:mastercookai/app/imports/packages_imports.dart';
// // import 'package:mastercookai/core/providers/auth/controllers/auth_notifier.dart';

// // import '../../../app/theme/colors.dart';
// // import '../../../core/network/app_status.dart';
// // import '../../../core/widgets/custom_button.dart';
// // import '../../../core/widgets/custom_text.dart';

// // class MobileOtpScreen extends HookConsumerWidget {
// //   final String? email;
// //   const MobileOtpScreen({super.key, this.email});

// //   @override
// //   Widget build(BuildContext context, WidgetRef ref) {
// //     final authState = ref.watch(authNotifierProvider);
// //     final authNotifier = ref.read(authNotifierProvider.notifier);
// //     final fieldOne = useTextEditingController();
// //     final fieldTwo = useTextEditingController();
// //     final fieldThree = useTextEditingController();
// //     final fieldFour = useTextEditingController();
// //     final fieldFive = useTextEditingController();
// //     final fieldSix = useTextEditingController();

// //     void onComplete() {
// //       String otp = fieldOne.text + fieldTwo.text + fieldThree.text + fieldFour.text + fieldFive.text + fieldSix.text;
// //       if (otp.length == 6) {
// //         authNotifier.verifyotp(context, email!, otp);
// //       }
// //     }
// //     return Scaffold(
// //       body: Column(
// //         mainAxisAlignment: MainAxisAlignment.center,
// //         children: [
// //           CustomText(
// //             text: "OTP Verification",
// //             color: AppColors.blackTextColor,
// //             size: 24,
// //             weight: FontWeight.w700,
// //             fontFamily: 'Inter',
// //           ),
// //           SizedBox(height: 50),
// //           CustomText(
// //             text: "Please enter the 4-digit code sent to your email",
// //             color: AppColors.texGreyColor,
// //             size: 14,
// //             weight: FontWeight.w400,
// //             fontFamily: 'Inter',
// //           ),
// //           const SizedBox(height: 30),
// //           Padding(
// //             padding: const EdgeInsets.symmetric(horizontal: 10),
// //             child: Row(
// //               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
// //               children: [
// //                 _otpField(context, fieldOne),
// //                 SizedBox(width: 5),
// //                 _otpField(context, fieldTwo),
// //                 SizedBox(width: 5),
// //                 _otpField(context, fieldThree),
// //                 SizedBox(width: 5),
// //                 _otpField(context, fieldFour),
// //                 SizedBox(width: 5),
// //                 _otpField(context, fieldFive),
// //                 SizedBox(width: 5),
// //                 _otpField(context, fieldSix),
// //               ],
// //             ),
// //           ),
// //           const SizedBox(height: 30),
// //           CustomButton(
// //             text: 'Verify',
// //             height: 30,
// //             weight: FontWeight.w500,
// //             width: 250,
// //             isLoading: authState.status == AppStatus.creating,
// //             borderRadius: 10,
// //             onPressed: () {
// //               onComplete();
// //             },
// //           ),

// //         ],
// //       ),
// //     );
// //   }

// //   Widget _otpField(BuildContext context, TextEditingController controller) {
// //     return SizedBox(
// //         width: 50,
// //         height: 100,
// //         child: TextField(
// //           controller: controller,
// //           autofocus: true,
// //           onChanged: (value) {
// //             if (value.length == 1) {
// //               FocusScope.of(context).nextFocus();
// //             }
// //           },
// //           keyboardType: TextInputType.number,
// //           textAlign: TextAlign.center,
// //           cursorColor: AppColors.blackTextColor,
// //           maxLength: 1,
// //           style: TextStyle(
// //             color: AppColors.primaryLightTextColor,
// //             fontSize: 14,
// //             fontFamily: "Inter-Regular",
// //             fontWeight: FontWeight.w400,
// //           ),
// //           decoration: InputDecoration(
// //             hintStyle: TextStyle(
// //               color: AppColors.primaryLightTextColor,
// //               fontSize: 15,
// //               fontFamily: "Inter-Regular",
// //               fontWeight: FontWeight.w400,
// //             ),
// //             contentPadding: EdgeInsets.symmetric(
// //               vertical: 1 > 1 ? 15 : 15, // Adjust padding for multi-line
// //               horizontal: 12,
// //             ),
// //             border: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(10),
// //               borderSide: const BorderSide(
// //                 color: AppColors.primaryBorderColor,
// //                 width: 2,
// //               ),
// //             ),
// //             enabledBorder: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(10),
// //               borderSide: BorderSide(
// //                 color: Colors.grey[300]!,
// //                 width: 2,
// //               ),
// //             ),
// //             focusedBorder: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(10),
// //               borderSide: const BorderSide(
// //                 color: AppColors.primaryBorderColor,
// //                 width: 2,
// //               ),
// //             ),
// //             isDense: true,
// //           ),
// //         ));
// //   }
// // }


// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:go_router/go_router.dart';
// import 'package:mastercookai/app/imports/packages_imports.dart';
// import 'package:mastercookai/core/providers/auth/controllers/auth_notifier.dart';

// import '../../../app/theme/colors.dart';
// import '../../../core/network/app_status.dart';
// import '../../../core/widgets/custom_button.dart';
// import '../../../core/widgets/custom_text.dart';

// class MobileOtpScreen extends HookConsumerWidget {
//   final String? email;
//   final String? fullName;
//   final String? password;
//   final String? userType;

//   const MobileOtpScreen({
//     super.key,
//     this.email,
//     this.fullName,
//     this.password,
//     this.userType,
//   });

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final authState = ref.watch(authNotifierProvider);
//     final authNotifier = ref.read(authNotifierProvider.notifier);
//     final fieldOne = useTextEditingController();
//     final fieldTwo = useTextEditingController();
//     final fieldThree = useTextEditingController();
//     final fieldFour = useTextEditingController();
//     final fieldFive = useTextEditingController();
//     final fieldSix = useTextEditingController();
//     final remainingTime = useState(60); // Timer state
//     final timerActive = useState(true); // Timer active state

//     // Timer logic
//     useEffect(() {
//       if (timerActive.value)  {
//         final timer = Timer.periodic(const Duration(seconds: 1), (timer) {
//           if (remainingTime.value > 0) {
//             remainingTime.value--;
//           } else {
//             timerActive.value = false;
//             timer.cancel();
//           }
//         });
//         return () => timer.cancel();
//       }
//       return null;
//     }, [timerActive.value]);

//     void onComplete() {
//       String otp = fieldOne.text + fieldTwo.text + fieldThree.text + fieldFour.text + fieldFive.text + fieldSix.text;
//       if (otp.length == 6) {
//         authNotifier.verifyotp(context, email!, otp);
//       }
//     }

//     void resendOtp() {
//       // Reset timer and resend OTP
//       remainingTime.value = 60;
//       timerActive.value = true;
//       authNotifier.register(
//         context,
//         fullName!,
//         password!,
//         email!,
//         userType!,
//         isResend: true,
//       );
//     }

//     return Scaffold(
//       body: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           CustomText(
//             text: "OTP Verification",
//             color: AppColors.blackTextColor,
//             size: 24,
//             weight: FontWeight.w700,
//             fontFamily: 'Inter',
//           ),
//           const SizedBox(height: 50),
//           CustomText(
//             text: "OTP sent successfully on $email",
//             color: AppColors.texGreyColor,
//             size: 14,
//             weight: FontWeight.w400,
//             fontFamily: 'Inter',
//           ),
//           const SizedBox(height: 8),
//           CustomText(
//             text: "Please enter the 6-digit code sent to your mobile",
//             color: AppColors.texGreyColor,
//             size: 14,
//             weight: FontWeight.w400,
//             fontFamily: 'Inter',
//           ),
//           const SizedBox(height: 30),
//           Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 10),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//               children: [
//                 _otpField(context, fieldOne),
//                 const SizedBox(width: 5),
//                 _otpField(context, fieldTwo),
//                 const SizedBox(width: 5),
//                 _otpField(context, fieldThree),
//                 const SizedBox(width: 5),
//                 _otpField(context, fieldFour),
//                 const SizedBox(width: 5),
//                 _otpField(context, fieldFive),
//                 const SizedBox(width: 5),
//                 _otpField(context, fieldSix),
//               ],
//             ),
//           ),
//           const SizedBox(height: 20),
//           GestureDetector(
//             onTap: timerActive.value ? null : resendOtp,
//             child: Text(
//               timerActive.value 
//                   ? "Resend OTP in ${remainingTime.value}s" 
//                   : "Resend OTP",
//               style: TextStyle(
//                 color: timerActive.value 
//                     ? Colors.grey 
//                     : Colors.blue,
//                 fontSize: 14,
//                 fontWeight: FontWeight.w400,
//                 fontFamily: 'Inter',
//               ),
//             ),
//           ),
//           const SizedBox(height: 20),
//           CustomButton(
//             text: 'Verify',
//             height: 30,
//             weight: FontWeight.w500,
//             width: 250,
//             isLoading: authState.status == AppStatus.creating,
//             borderRadius: 10,
//             onPressed: () {
//               onComplete();
//             },
//           ),
//           const SizedBox(height: 20),
//           TextButton(
//             onPressed: () {
//               authNotifier.reset();
//               context.pop(); // Navigate back to the sign-up screen
//             },
//             child: const Text(
//               'Back to Sign Up',
//               style: TextStyle(
//                 color: Colors.blue,
//                 fontSize: 14,
//                 fontWeight: FontWeight.w400,
//                 fontFamily: 'Inter',
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _otpField(BuildContext context, TextEditingController controller) {
//     return SizedBox(
//         width: 50,
//         height: 100,
//         child: TextField(
//           controller: controller,
//           autofocus: true,
//           onChanged: (value) {
//             if (value.length == 1) {
//               FocusScope.of(context).nextFocus();
//             }
//           },
//           keyboardType: TextInputType.number,
//           textAlign: TextAlign.center,
//           cursorColor: AppColors.blackTextColor,
//           maxLength: 1,
//           style: TextStyle(
//             color: AppColors.primaryLightTextColor,
//             fontSize: 14,
//             fontFamily: "Inter-Regular",
//             fontWeight: FontWeight.w400,
//           ),
//           decoration: InputDecoration(
//             hintStyle: TextStyle(
//               color: AppColors.primaryLightTextColor,
//               fontSize: 15,
//               fontFamily: "Inter-Regular",
//               fontWeight: FontWeight.w400,
//             ),
//             contentPadding: EdgeInsets.symmetric(
//               vertical: 1 > 1 ? 15 : 15, // Adjust padding for multi-line
//               horizontal: 12,
//             ),
//             border: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(10),
//               borderSide: const BorderSide(
//                 color: AppColors.primaryBorderColor,
//                 width: 2,
//               ),
//             ),
//             enabledBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(10),
//               borderSide: BorderSide(
//                 color: Colors.grey[300]!,
//                 width: 2,
//               ),
//             ),
//             focusedBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(10),
//               borderSide: const BorderSide(
//                 color: AppColors.primaryBorderColor,
//                 width: 2,
//               ),
//             ),
//             isDense: true,
//           ),
//         ));
//   }
// }

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/app/theme/colors.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/auth/controllers/auth_notifier.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

class MobileOtpScreen extends HookConsumerWidget {
  final String? email;
  final String? fullName;
  final String? password;
  final String? userType;

  const MobileOtpScreen({
    super.key,
    this.email,
    this.fullName,
    this.password,
    this.userType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);
    final fieldOne = useTextEditingController();
    final fieldTwo = useTextEditingController();
    final fieldThree = useTextEditingController();
    final fieldFour = useTextEditingController();
    final fieldFive = useTextEditingController();
    final fieldSix = useTextEditingController();
    final remainingTime = useState(60);
    final timerActive = useState(true);

    useEffect(() {
      if (timerActive.value) {
        final timer = Timer.periodic(const Duration(seconds: 1), (timer) {
          if (remainingTime.value > 0) {
            remainingTime.value--;
          } else {
            timerActive.value = false;
            timer.cancel();
          }
        });
        return () => timer.cancel();
      }
      return null;
    }, [timerActive.value]);

    void onComplete() {
      String otp = fieldOne.text + fieldTwo.text + fieldThree.text + 
                  fieldFour.text + fieldFive.text + fieldSix.text;
      if (otp.length == 6) {
        authNotifier.verifyotp(context, email!, otp);
      }
    }

    void resendOtp() {
      remainingTime.value = 60;
      timerActive.value = true;
      authNotifier.register(
        context,
        fullName!,
        password!,
        email!,
        userType!,
        isResend: true,
      );
    }

    return Scaffold(
      resizeToAvoidBottomInset: false, // Prevent screen resize
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(), // Dismiss keyboard on tap outside
        child: SingleChildScrollView(
          physics: const NeverScrollableScrollPhysics(), // Disable scrolling
          child: Container(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom, // Account for keyboard
            ),
            height: MediaQuery.of(context).size.height, // Full screen height
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomText(
                  text: "OTP Verification",
                  color: AppColors.blackTextColor,
                  size: 24,
                  weight: FontWeight.w700,
                  fontFamily: 'Inter',
                ),
                const SizedBox(height: 30), // Reduced spacing
                CustomText(
                  text: "OTP sent successfully on $email",
                  color: AppColors.texGreyColor,
                  size: 14,
                  weight: FontWeight.w400,
                  fontFamily: 'Inter',
                ),
                const SizedBox(height: 8),
                CustomText(
                  text: "Please enter the 6-digit code sent to your mobile",
                  color: AppColors.texGreyColor,
                  size: 14,
                  weight: FontWeight.w400,
                  fontFamily: 'Inter',
                ),
                const SizedBox(height: 30),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _otpField(context, fieldOne),
                      const SizedBox(width: 5),
                      _otpField(context, fieldTwo),
                      const SizedBox(width: 5),
                      _otpField(context, fieldThree),
                      const SizedBox(width: 5),
                      _otpField(context, fieldFour),
                      const SizedBox(width: 5),
                      _otpField(context, fieldFive),
                      const SizedBox(width: 5),
                      _otpField(context, fieldSix),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                GestureDetector(
                  onTap: timerActive.value ? null : resendOtp,
                  child: Text(
                    timerActive.value 
                        ? "Resend OTP in ${remainingTime.value}s" 
                        : "Resend OTP",
                    style: TextStyle(
                      color: timerActive.value 
                          ? Colors.grey 
                          : Colors.blue,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                CustomButton(
                  text: 'Verify',
                  height: 30,
                  weight: FontWeight.w500,
                  width: 250,
                  isLoading: authState.status == AppStatus.creating,
                  borderRadius: 10,
                  onPressed: onComplete,
                ),
                const SizedBox(height: 20),
                TextButton(
                  onPressed: () {
                    authNotifier.reset();
                    context.pop();
                  },
                  child: const Text(
                    'Back to Sign Up',
                    style: TextStyle(
                      color: Colors.blue,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _otpField(BuildContext context, TextEditingController controller) {
    return SizedBox(
      width: 50,
      height: 70, // Reduced height
      child: TextField(
        controller: controller,
        autofocus: true,
        onChanged: (value) {
          if (value.length == 1) {
            FocusScope.of(context).nextFocus();
          }
        },
        keyboardType: TextInputType.number,
        textAlign: TextAlign.center,
        cursorColor: AppColors.blackTextColor,
        maxLength: 1,
        style: const TextStyle(
          color: AppColors.primaryLightTextColor,
          fontSize: 14,
          fontFamily: "Inter-Regular",
          fontWeight: FontWeight.w400,
        ),
        decoration: InputDecoration(
          counterText: '', // Hide character counter
          contentPadding: const EdgeInsets.symmetric(vertical: 15),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              color: AppColors.primaryBorderColor,
              width: 2,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(
              color: Colors.grey[300]!,
              width: 2,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              color: AppColors.primaryBorderColor,
              width: 2,
            ),
          ),
          isDense: true,
        ),
      ),
    );
  }
}