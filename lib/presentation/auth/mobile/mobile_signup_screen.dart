import 'package:flutter/gestures.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/auth/sub_views/otp_screen.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/auth/controllers/auth_notifier.dart';
import '../../../core/providers/profile/user_profile_notifier.dart';
import '../../../core/providers/profile/user_types_notifier.dart';
import '../../../core/utils/Utils.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/utils/validator.dart';

import '../../../core/widgets/clipper_drop_dpwn.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_drop_down.dart';
import '../../../core/widgets/custom_text_button.dart';
import '../../profile/mobileui/sub_views/build_user_types_dopdown_field.dart';
import '../dialogs/mobile_password_reset_dialog.dart';
import 'mobile_login_screen.dart';


class MobileSignupScreen extends HookConsumerWidget {
  const MobileSignupScreen({super.key});


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);
    final fullNameController = useTextEditingController();
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedUserType = useState<UserType?>(null);
    final isChecked = useState(false);
    // final userTypesState = ref.watch(userTypesNotifierProvider);
    //
    // // Set default selectedType when data is available
    // if (userTypesState.status == AppStatus.success && userTypesState.data != null) {
    //   final uniqueUserTypes = userTypesState.data!.userTypes.toSet().toList();
    //   selectedType ??= uniqueUserTypes.isNotEmpty ? uniqueUserTypes.first.type : null;
    // }
    // debugPrint("sdfjsdjh ${selectedType}");

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Builder(
        builder: (BuildContext context) {
          return Stack(
            children: [
              Center(
                child: Row(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.all(18),
                        child: Form(
                          key: formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(height: 100),
                              CustomText(
                            text: "Sign Up",
                            color: AppColors.primaryGreyColor,
                            size: 24,
                            weight: FontWeight.w700,
                            fontFamily: 'Inter',
                          ),
                          SizedBox(height: 70),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 10),
                            child: Column(
                              children: [
                                CustomInputField(
                                  hintText: 'Full Name',
                                  controller: fullNameController,
                                  keyboardType: TextInputType.text,
                                  validator: Validator.validateFullName,
                                  maxLength: 50,
                                ),
                                SizedBox(height: 20.h),
                                CustomInputField(
                                  hintText: 'Email',
                                  controller: emailController,
                                  keyboardType: TextInputType.emailAddress,
                                  validator: Validator.validateEmail,
                                  maxLength: 70,
                                ),
                                SizedBox(height: 20.h),
                                CustomInputField(
                                  hintText: 'Password',
                                  controller: passwordController,
                                  keyboardType: TextInputType.emailAddress,
                                  isPassword: true,
                                  validator: Validator.validatePassword,
                                  maxLength: 16,
                                ),
                                SizedBox(height: 20.h),
                                UserTypesDropdownField(
                                  onChanged: (userType) {
                                    selectedUserType.value = userType;
                                  },
                                ),
                                SizedBox(height: 10.h),
                                Row(
                                  children: [
                                    Checkbox(
                                      value: isChecked.value,
                                      onChanged: (bool? newValue) {
                                        isChecked.value = newValue ?? false;
                                      },
                                      activeColor: Colors.red,
                                      side: const BorderSide(
                                          color: Colors.red, width: 1),
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                          BorderRadius.circular(2)),
                                      checkColor: Colors.white,
                                    ),
                                    RichText(
                                      textAlign: TextAlign.center,
                                      text: TextSpan(
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          letterSpacing: 0,
                                        ),
                                        children: [
                                          TextSpan(
                                            text: "I accept the ",
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Color(0xFF4F4F4F),
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          TextSpan(
                                            text:
                                            " Privacy Policy and Terms of Use.",
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Color(0xFF007AFF),
                                              fontWeight: FontWeight.w400,
                                              decoration:
                                              TextDecoration.underline,
                                            ),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () {
                                                Utils().launchURL(
                                                    url: 'http://www.mastercook.com/privacy-policy');
                                              },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 50.h),
                                CustomButton(
                                  text: 'Sign Up',
                                  height: 30,
                                  weight: FontWeight.w600,
                                  isLoading:
                                      authState.status == AppStatus.loading,
                                  width: 200,
                                  borderRadius: 10,
                                  onPressed: () {
                                    if (formKey.currentState!.validate()) {
                                      if (selectedUserType.value == null) {
                                        Utils().showFlushbar(context,
                                            message: 'Please select a user type.',
                                            isError: true);
                                        return;
                                      }
                                      if (!isChecked.value) {
                                        Utils().showFlushbar(context,
                                            message:
                                                'Please accept the privacy policy and terms of use.',
                                            isError: true);
                                        return;
                                      }
                                      authNotifier.register(
                                        context,
                                        fullNameController.text.trim(),
                                        passwordController.text.trim(),
                                        emailController.text.trim(),
                                        selectedUserType.value!.id.toString(),
                                      );
                                    }
                                  },
                                ),
                              ],
                            ),
                          ),
                          const Spacer(),
                          RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: responsiveFont(18).sp,
                                height: 16 / 12,
                                letterSpacing: 0,
                              ),
                              children: [
                                TextSpan(
                                  text: "Already have an Account? ",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF4F4F4F),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                TextSpan(
                                  text: "Log In",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF007AFF),
                                    fontWeight: FontWeight.w400,
                                    decoration: TextDecoration.underline,
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                            const MobileLoginScreen()),
                                      );
                                      // Utils().launchURL(
                                      //     url: 'https://mastercook.ai/');
                                    },
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 50.h),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
        },
      ),
    );
  }
}
