import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/auth/login_screen.dart';
import 'package:mastercookai/presentation/profile/mobileui/sub_views/build_user_types_dopdown_field.dart';
import '../../../app/imports/packages_imports.dart';
import 'dart:io' show Platform;
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/YouTubeLauncher.dart';
import '../../../core/widgets/YouTubePlayer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../core/network/app_status.dart';
import '../../core/providers/auth/controllers/auth_notifier.dart';
import '../../core/utils/validator.dart';
import 'mobile/mobile_signup_screen.dart';
import 'sub_views/otp_screen.dart';

class VerfiyOtpScreen extends HookConsumerWidget {
  final String email;
  final String? fullName;
  final String? password;
  final String? userType;
  const VerfiyOtpScreen(
      {super.key,
      required this.email,
      this.fullName,
      this.password,
      this.userType});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(authNotifierProvider, (previous, next) {
      if (next.status == AppStatus.otpVerificationSuccess) {
        ref.read(authNotifierProvider.notifier).reset();
      }
    });
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);

    final fullNameController = useTextEditingController();
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedUserType = useState<UserType?>(null);

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Positioned.fill(
            child: Image.asset(
              AssetsManager.background_img,
              fit: BoxFit.cover,
            ),
          ),
          Center(
            child: FractionallySizedBox(
              widthFactor: DeviceUtils().isTabletOrIpad(context) ? 0.8 : 0.45,
              heightFactor: 0.62,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.95),
                  borderRadius: BorderRadius.circular(30.r),
                ),
                child: Row(
                  children: [
                    // Left Section
                    Visibility(
                      visible: getDeviceType(context).name == 'mobile'
                          ? false
                          : true,
                      child: Expanded(
                        child: Container(
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(30.r),
                              bottomLeft: Radius.circular(30.r),
                            ),
                            image: DecorationImage(
                              image: AssetImage(AssetsManager.recipe_bg_img),
                              fit: BoxFit.cover,
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(top: 50),
                                child: Image.asset(
                                  AssetsManager.mastercook_ai_logo,
                                  height: 250.h,
                                  width: 400.w,
                                ),
                              ),
                              SizedBox(height: 10.h),
                              Padding(
                                padding: const EdgeInsets.only(top: 70),
                                child: Align(
                                  alignment: Alignment.bottomCenter,
                                  child: CustomText(
                                    text: "Watch getting started video",
                                    fontFamily: 'Inter',
                                    size: 16,
                                    weight: FontWeight.w400,
                                    height: 16 / 12,
                                    color: AppColors.primaryGreyColor,
                                    align: TextAlign.center,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    top: 10.0, left: 20.0, right: 20.0),
                                child: Container(
                                  width: 338,
                                  height: 180,
                                  decoration: BoxDecoration(
                                    color: Color(0xFF333333),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: ClipRRect(
                                      borderRadius: BorderRadius.circular(4),
                                      child: (kIsWeb || Platform.isMacOS)
                                          ? const InAppYouTubePlayer(
                                              videoId: "d1q4nwMUegA")
                                          : (Platform.isWindows ||
                                                  Platform.isLinux)
                                              ? YouTubeLauncher()
                                              : InAppYouTubePlayer(
                                                  videoId: "d1q4nwMUegA")
                                      // Center(
                                      //   child: Icon(
                                      //     Icons.play_circle_fill,
                                      //     size: 48,
                                      //     color: Colors.white.withValues(alpha: 0.8),
                                      //   ),
                                      //
                                      // ),
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // Right Section
                    Expanded(
                        child: OtpScreen(
                      email: email,
                      fullName: fullName,
                      password: password,
                      userType: userType,
                    )),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
