import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/helpers/customAutocomplete.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/autocomplete_fields.dart';

/// Mobile-optimized item search field widget
class MobileItemSearchField extends StatelessWidget {
  final String title;
  final String placeholder;
  final TextEditingController controller;
  final bool isRequired;

  const MobileItemSearchField({
    super.key,
    required this.title,
    required this.placeholder,
    required this.controller,
    this.isRequired = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.primaryLightTextColor,
              ),
            ),
            if (isRequired)
              const Text(
                ' *',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          constraints: const BoxConstraints(minHeight: 48),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: AppColors.lightestGreyColor,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomAutocomplete<String>(
            controller: controller,
            suggestionsHeight: 200.0,
            dropdownWidthMultiplier: 1.0,
            fetchSuggestions: (query) => searchMasterIngredients(context, query),
            itemBuilder: (context, item) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.08),
                      spreadRadius: 0.5,
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                  border: Border.all(color: Colors.grey.shade100, width: 0.5),
                ),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Row(
                    children: [
                      Container(
                        width: 6,
                        height: 6,
                        decoration: const BoxDecoration(
                          color: AppColors.primaryColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          item,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.primaryGreyColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
            itemToString: (item) => item,
            onSelected: (item) {
              controller.text = item;
            },
            decoration: InputDecoration(
              hintText: placeholder,
              hintStyle: const TextStyle(
                fontSize: 14,
                color: AppColors.primaryLightTextColor,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              border: InputBorder.none,
              suffixIcon: const Icon(
                Icons.search,
                color: AppColors.primaryLightTextColor,
                size: 20,
              ),
            ),
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.primaryGreyColor,
            ),
          ),
        ),
      ],
    );
  }
}

/// Mobile-optimized unit search field widget
class MobileUnitSearchField extends ConsumerWidget {
  final String title;
  final String placeholder;
  final TextEditingController controller;
  final bool isRequired;

  const MobileUnitSearchField({
    super.key,
    required this.title,
    required this.placeholder,
    required this.controller,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.primaryLightTextColor,
              ),
            ),
            if (isRequired)
              const Text(
                ' *',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          constraints: const BoxConstraints(minHeight: 48),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: AppColors.lightestGreyColor,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomAutocomplete<String>(
            controller: controller,
            suggestionsHeight: 200.0,
            dropdownWidthMultiplier: 1.0,
            fetchSuggestions: (query) async {
              final userProfileNotifier = ref.read(userProfileNotifierProvider.notifier);
              List<String> instructionWords = userProfileNotifier.instructionWords;

              // Fallback to default units if user profile units are empty
              if (instructionWords.isEmpty) {
                instructionWords = [
                  'kg', 'g', 'lb', 'oz', 'cup', 'cups', 'tbsp', 'tsp', 'ml', 'l', 'liter', 'liters',
                  'piece', 'pieces', 'slice', 'slices', 'can', 'cans', 'bottle', 'bottles',
                  'pack', 'packs', 'box', 'boxes', 'bag', 'bags', 'jar', 'jars'
                ];
              }

              if (query.isEmpty) {
                return instructionWords.take(10).toList();
              }

              return instructionWords
                  .where((word) => word.toLowerCase().contains(query.toLowerCase()))
                  .take(10)
                  .toList();
            },
            itemBuilder: (context, item) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.08),
                      spreadRadius: 0.5,
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                  border: Border.all(color: Colors.grey.shade100, width: 0.5),
                ),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Text(
                    item,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primaryGreyColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              );
            },
            itemToString: (item) => item,
            onSelected: (item) {
              controller.text = item;
            },
            decoration: InputDecoration(
              hintText: placeholder,
              hintStyle: const TextStyle(
                fontSize: 14,
                color: AppColors.primaryLightTextColor,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              border: InputBorder.none,
              suffixIcon: const Icon(
                Icons.search,
                color: AppColors.primaryLightTextColor,
                size: 20,
              ),
            ),
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.primaryLightTextColor,
            ),
          ),
        ),
      ],
    );
  }
}

/// Mobile-optimized text field widget
class MobileTextField extends StatelessWidget {
  final String title;
  final String placeholder;
  final TextEditingController controller;
  final TextInputType keyboardType;
  final bool isRequired;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;

  const MobileTextField({
    super.key,
    required this.title,
    required this.placeholder,
    required this.controller,
    this.keyboardType = TextInputType.text,
    this.isRequired = false,
    this.maxLength,
    this.inputFormatters,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.primaryLightTextColor,
              ),
            ),
            if (isRequired)
              const Text(
                ' *',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          constraints: const BoxConstraints(minHeight: 48),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: AppColors.lightestGreyColor,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextField(
            controller: controller,
            keyboardType: keyboardType,
            maxLength: maxLength,
            inputFormatters: inputFormatters,
            decoration: InputDecoration(
              hintText: placeholder,
              hintStyle: const TextStyle(
                fontSize: 14,
                color: AppColors.primaryLightTextColor,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              border: InputBorder.none,
              counterText: '', // Hide character counter
            ),
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.primaryGreyColor,
            ),
          ),
        ),
      ],
    );
  }
}

/// Mobile-optimized date field widget
class MobileDateField extends StatelessWidget {
  final String title;
  final String placeholder;
  final TextEditingController controller;
  final VoidCallback onTap;
  final bool isRequired;

  const MobileDateField({
    super.key,
    required this.title,
    required this.placeholder,
    required this.controller,
    required this.onTap,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.primaryLightTextColor,
              ),
            ),
            if (isRequired)
              const Text(
                ' *',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: onTap,
          child: Container(
            constraints: const BoxConstraints(minHeight: 48),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(
                color: AppColors.lightestGreyColor,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: AbsorbPointer(
              child: TextField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: placeholder,
                  hintStyle: const TextStyle(
                    fontSize: 14,
                    color: AppColors.primaryLightTextColor,
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  border: InputBorder.none,
                  suffixIcon: const Icon(
                    Icons.calendar_today,
                    color: AppColors.primaryLightTextColor,
                    size: 20,
                  ),
                ),
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.primaryGreyColor,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
