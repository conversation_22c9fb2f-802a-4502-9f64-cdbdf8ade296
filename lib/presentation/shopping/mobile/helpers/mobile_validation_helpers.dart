import 'package:flutter/material.dart';
import 'package:mastercookai/core/utils/Utils.dart';

/// Mobile validation helpers that mirror the Mac implementation patterns
class MobileValidationHelpers {
  
  /// Validates item name - required field
  static String? validateItemName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Item name is required';
    }
    if (value.trim().length < 2) {
      return 'Item name must be at least 2 characters';
    }
    return null;
  }

  /// Validates amount using the same logic as Mac implementation
  static String? validateAmount(String? value, {bool isRequired = false}) {
    if (value == null || value.trim().isEmpty) {
      if (isRequired) {
        return 'Amount is required';
      }
      return null; // Optional field
    }

    final amount = double.tryParse(value.trim());
    if (amount == null) {
      return 'Please enter a valid number';
    }

    if (!Utils().isValidAmount(amount)) {
      return 'Amount must be between 0.01 and 1,000,000';
    }

    return null;
  }

  /// Validates cost field
  static String? validateCost(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Cost is optional
    }

    final cost = double.tryParse(value.trim());
    if (cost == null) {
      return 'Please enter a valid cost';
    }

    if (cost < 0) {
      return 'Cost cannot be negative';
    }

    if (cost > 1000000) {
      return 'Cost cannot exceed 1,000,000';
    }

    return null;
  }

  /// Validates date field using the same pattern as Mac
  static String? validateDate(String? value, String fieldName, {bool isRequired = false}) {
    if (value == null || value.trim().isEmpty) {
      if (isRequired) {
        return '$fieldName is required';
      }
      return null;
    }

    try {
      final parts = value.trim().split('/');
      if (parts.length != 3) {
        return 'Invalid $fieldName format. Use MM/dd/yyyy';
      }

      final month = int.parse(parts[0]);
      final day = int.parse(parts[1]);
      final year = int.parse(parts[2]);

      if (month < 1 || month > 12) {
        return 'Invalid month in $fieldName';
      }

      if (day < 1 || day > 31) {
        return 'Invalid day in $fieldName';
      }

      if (year < 1900 || year > 2100) {
        return 'Invalid year in $fieldName';
      }

      // Try to create the date to validate it
      DateTime(year, month, day);
      return null;
    } catch (e) {
      return 'Invalid $fieldName format';
    }
  }

  /// Validates that use by date is after purchase date
  static String? validateDateRange(String? purchaseDate, String? useByDate) {
    if (purchaseDate == null || purchaseDate.trim().isEmpty ||
        useByDate == null || useByDate.trim().isEmpty) {
      return null; // Skip validation if either date is empty
    }

    try {
      final purchaseParts = purchaseDate.trim().split('/');
      final useByParts = useByDate.trim().split('/');

      if (purchaseParts.length != 3 || useByParts.length != 3) {
        return null; // Let individual date validation handle format errors
      }

      final purchaseDateTime = DateTime(
        int.parse(purchaseParts[2]), // year
        int.parse(purchaseParts[0]), // month
        int.parse(purchaseParts[1]), // day
      );

      final useByDateTime = DateTime(
        int.parse(useByParts[2]), // year
        int.parse(useByParts[0]), // month
        int.parse(useByParts[1]), // day
      );

      if (useByDateTime.isBefore(purchaseDateTime)) {
        return 'Use by date cannot be before purchase date';
      }

      return null;
    } catch (e) {
      return null; // Let individual date validation handle parsing errors
    }
  }

  /// Shows validation error using the same pattern as Mac
  static void showValidationError(BuildContext context, String message) {
    if (context.mounted) {
      Utils().showFlushbar(context, message: message, isError: true);
    }
  }

  /// Shows success message
  static void showSuccessMessage(BuildContext context, String message) {
    if (context.mounted) {
      Utils().showFlushbar(context, message: message, isError: false);
    }
  }

  /// Validates all shopping item fields at once
  static List<String> validateShoppingItem({
    required String item,
    required String amount,
    required String unit,
    required String storeLocation,
    required String recipe,
    required String cost,
  }) {
    List<String> errors = [];

    final itemError = validateItemName(item);
    if (itemError != null) errors.add(itemError);

    final amountError = validateAmount(amount);
    if (amountError != null) errors.add(amountError);

    final costError = validateCost(cost);
    if (costError != null) errors.add(costError);

    return errors;
  }

  /// Validates all pantry item fields at once
  static List<String> validatePantryItem({
    required String item,
    required String amount,
    required String unit,
    required String purchaseDate,
    required String useByDate,
  }) {
    List<String> errors = [];

    final itemError = validateItemName(item);
    if (itemError != null) errors.add(itemError);

    final amountError = validateAmount(amount);
    if (amountError != null) errors.add(amountError);

    final purchaseDateError = validateDate(purchaseDate, 'Purchase date', isRequired: true);
    if (purchaseDateError != null) errors.add(purchaseDateError);

    final useByDateError = validateDate(useByDate, 'Use by date', isRequired: true);
    if (useByDateError != null) errors.add(useByDateError);

    final dateRangeError = validateDateRange(purchaseDate, useByDate);
    if (dateRangeError != null) errors.add(dateRangeError);

    return errors;
  }

  /// Parses amount with fallback to default value (same as Mac pattern)
  static double parseAmount(String? amountText, {double defaultValue = 0.0}) {
    if (amountText == null || amountText.trim().isEmpty) {
      return defaultValue;
    }
    return double.tryParse(amountText.trim()) ?? defaultValue;
  }

  /// Parses cost with fallback to default value
  static double parseCost(String? costText, {double defaultValue = 0.0}) {
    if (costText == null || costText.trim().isEmpty) {
      return defaultValue;
    }
    return double.tryParse(costText.trim()) ?? defaultValue;
  }

  /// Parses date string to DateTime (same format as Mac)
  static DateTime? parseDate(String? dateText) {
    if (dateText == null || dateText.trim().isEmpty) {
      return null;
    }

    try {
      final parts = dateText.trim().split('/');
      if (parts.length == 3) {
        final month = int.parse(parts[0]);
        final day = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      // Return null for invalid dates
    }
    return null;
  }
}
