import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../../core/data/models/pantry.dart';
import '../../../../core/utils/Utils.dart';
import '../../../../core/widgets/camera_upload_image.dart';
import '../../../../core/widgets/custom_hover_menu.dart';
import '../../../../core/widgets/custom_text.dart';

class PantryCard extends ConsumerWidget {
  final Pantry pantry;
  final bool isHighRes;
  final VoidCallback onTap;
  final String selectedTab;
  final int pantryIndex;

  PantryCard({
    super.key,
    required this.pantry,
    required this.isHighRes,
    required this.onTap,
    required this.selectedTab,
    required this.pantryIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pantryListNotifier = ref.read(pantryNotifierProvider.notifier);
    final pantryListState = ref.watch(pantryNotifierProvider);

    final isDeleting = pantryListState.status == AppStatus.loading;

    return GestureDetector(
      onTap: () {
        if (!isDeleting) {
          context.go(
              '/shopping/mobile_shopping_list_detail_screen?tab=$selectedTab&index=$pantryIndex');
        }
      },
      child: Card(
          color: context.theme.cardColor,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(15),
                        bottom: Radius.circular(15),
                      ),
                      child: CommonImage(
                        imageSource: pantry.imageUrl,
                        placeholder: AssetsManager.shoppingDummy,
                        height: 133.0,
                        width: double.infinity,
                        fit: BoxFit.cover,
                      ),
                    ),
                    cameraUploadImage(onTap: () {
                      pantryListNotifier.pickImage(
                          context, ref, pantry.id.toString(), pantry.title);
                    })
                  ],
                ),
                const SizedBox(height: 10.0),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: CustomText(
                            text: pantry.title,
                            color: AppColors.primaryGreyColor,
                            size: 14,
                            weight: FontWeight.w500,
                            maxLine: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 15.0),
                        CustomHoverMenu(
                          items: ['Edit', 'Delete'],
                          itemIcons: [Icons.edit, Icons.delete],
                          onItemSelected: (value) async {
                            if (value == 'Edit') {
                              onTap();
                            } else if (value == 'Delete') {
                              if (!isDeleting) {
                                final bool? confirmed =
                                    await Utils().showCommonConfirmDialog(
                                  context: context,
                                  title: 'Delete Pantry',
                                  subtitle:
                                      'Are you sure you want to delete "${pantry.title}" pantry?',
                                  confirmText: 'Delete',
                                  cancelText: 'Cancel',
                                );

                                if (confirmed != true || !context.mounted) {
                                  return;
                                }

                                final result = await pantryListNotifier
                                    .deletePantryList(
                                        context, pantry.id.toString());

                                if (!context.mounted) {
                                  return;
                                }

                                if (result.$1 == AppStatus.success) {
                                  pantryListNotifier.fetchPantryLists(
                                      context: context);
                                }
                              }
                            }
                          },
                          menuWidth: 140.0,
                          menuTitle: 'Show Menu',
                          triggerIcon: Icons.more_vert_outlined,
                        ),
                      ],
                    ),
                    const SizedBox(height: 6.0),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: '${pantry.recipeCount} products',
                          color: AppColors.textGreyColor,
                          size: 12.0,
                          weight: FontWeight.w400,
                        ),
                        CustomText(
                          text:
                              ' ${timeago.format(DateTime.parse(pantry.createdDate), locale: 'en_short')}',
                          color: AppColors.textGreyColor,
                          size: 12.0,
                          weight: FontWeight.w400,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }
}
