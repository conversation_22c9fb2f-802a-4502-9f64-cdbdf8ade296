import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/core/data/models/shopping.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/add_product_bottom_sheet.dart';

import '../../../../app/imports/core_imports.dart';

class ShoppingListItem extends StatefulWidget {
  final dynamic item;
  final bool isSelected;
  final ValueChanged<bool?> onChanged;
  final int? shoppingListId;

  const ShoppingListItem({
    super.key,
    required this.item,
    required this.isSelected,
    required this.onChanged,
    this.shoppingListId,
  });

  @override
  State<ShoppingListItem> createState() => _ShoppingListItemState();
}

class _ShoppingListItemState extends State<ShoppingListItem> {
  @override
  Widget build(BuildContext context) {
    final isShoppingItem = widget.item is ShoppingItem;

    return Card(
      margin: const EdgeInsets.only(top: 12),
      color: context.theme.cardColor,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: AppColors.greyBorderColor),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Checkbox(
            value: widget.isSelected,
            onChanged: widget.onChanged,
            activeColor: Colors.red,
            side: const BorderSide(color: Colors.red, width: 1),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
            checkColor: Colors.white,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(top: 10, bottom: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: widget.item.item ?? '',
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            color: AppColors.blackTextColor,
                            fontSize: 14,
                          ),
                        ),
                        TextSpan(
                          text: ' ${widget.item.amount} ${widget.item.unit} ${widget.item.purchased ? ' (Purchased)' : ''}',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryColor,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 10,
                  ),
                  SizedBox(height: 5),
                  if (isShoppingItem)
                    Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'RECIPE',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.greyBorderColor,
                                ),
                              ),
                              Text(
                                widget.item.recipe ?? '',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.texGreyColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 10),
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'PRICE',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.greyBorderColor,
                                ),
                              ),
                              Text(
                                '\$${widget.item.cost}',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.texGreyColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'STORE LOCATION',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.greyBorderColor,
                                ),
                              ),
                              Text(
                                widget.item.storeLocation ?? '',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.texGreyColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                ],
              ),
            ),
          ),
          IconButton(
            icon: SvgPicture.asset(AssetsManager.ic_blue_edit),
            onPressed: () {
              if (widget.item is ShoppingItem) {
                showAddProductBottomSheet(
                  context,
                  editItem: widget.item as ShoppingItem,
                  shoppingListId: widget.shoppingListId,
                );
              }
            },
          ),
        ],
      ),
    );
  }
}
