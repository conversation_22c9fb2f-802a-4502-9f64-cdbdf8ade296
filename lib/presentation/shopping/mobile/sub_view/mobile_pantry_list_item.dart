import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:mastercookai/core/data/models/pantry.dart';
import '../../../../app/imports/core_imports.dart';
import 'add_pantry_product_bottom_sheet.dart';

class MobilePantryListItem extends StatefulWidget {
  final PantryItem item;
  final bool isSelected;
  final ValueChanged<bool?> onChanged;
  final int? pantryListId;

  const MobilePantryListItem({
    super.key,
    required this.item,
    required this.isSelected,
    required this.onChanged,
    this.pantryListId,
  });

  @override
  State<MobilePantryListItem> createState() => _MobilePantryListItemState();
}

class _MobilePantryListItemState extends State<MobilePantryListItem> {
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(top: 12),
      color: context.theme.cardColor,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: AppColors.greyBorderColor),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Checkbox(
            value: widget.isSelected,
            onChanged: widget.onChanged,
            activeColor: Colors.red,
            side: const BorderSide(color: Colors.red, width: 1),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
            checkColor: Colors.white,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(top: 10, bottom: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: widget.item.item ?? '',
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            color: AppColors.blackTextColor,
                            fontSize: 14,
                          ),
                        ),
                        TextSpan(
                          text: ' ${widget.item.amount} ${widget.item.unit}',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryColor,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                  SizedBox(height: 5),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'PURCHASED DATE',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w400,
                                color: AppColors.greyBorderColor,
                              ),
                            ),
                            Text(
                              DateFormat('MM/dd/yyyy')
                                  .format(widget.item.purchasedDate),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: AppColors.texGreyColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'USE BY DATE',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w400,
                                color: AppColors.greyBorderColor,
                              ),
                            ),
                            Text(
                              DateFormat('MM/dd/yyyy')
                                  .format(widget.item.useByDate),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: AppColors.texGreyColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
          IconButton(
            icon: SvgPicture.asset(AssetsManager.ic_blue_edit),
            onPressed: () {
              showAddPantryProductBottomSheet(
                context,
                editItem: widget.item,
                pantryListId: widget.pantryListId,
              );
            },
          ),
        ],
      ),
    );
  }
}
