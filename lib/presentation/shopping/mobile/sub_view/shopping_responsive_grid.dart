import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/shopping.dart';
import 'package:mastercookai/core/data/models/view_type.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/no_data_widget.dart';
import 'package:mastercookai/presentation/cookbook/mobile/sub_views/import_create_card_mobile.dart';
import 'package:mastercookai/presentation/cookbook/mobile/sub_views/import_create_card_mobile_list.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/shopping_card.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/shopping_listview_items.dart';

class ShoppingResponsiveGrid extends StatelessWidget {
  final List<Shopping> items;
  final bool hasMore;
  final VoidCallback onLoadMore;
  final ViewType viewType;
  final String selectedTab;
  final Function(int) onItemTap;
  final VoidCallback onCreate;
  final VoidCallback onImport;

  const ShoppingResponsiveGrid({
    super.key,
    required this.items,
    required this.hasMore,
    required this.onLoadMore,
    required this.viewType,
    required this.selectedTab,
    required this.onItemTap,
    required this.onCreate,
    required this.onImport,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = DeviceUtils().isTabletOrIpad(context);
    final isLargeScreen = screenWidth > 600;

    // Calculate desired item width based on screen size
    final desiredItemWidth = isTablet
        ? 180.0
        : isLargeScreen
            ? 160.0
            : 150.0;

    // Calculate cross axis count dynamically
    final crossAxisCount = (screenWidth / desiredItemWidth).floor().clamp(2, 4);

    return LayoutBuilder(
      builder: (context, constraints) {
        if (viewType == ViewType.grid) {
          return GridView.builder(
            padding: const EdgeInsets.all(8),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              childAspectRatio: 0.85,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
            ),
            itemCount: items.length + 1 + (hasMore ? 1 : 0), // +1 for create card
            itemBuilder: (context, index) {
              // Create card at index 0
              if (index == 0) {
                return ImportCreateCardMobile(
                  importText: "Import Shopping",
                  title: "Create Shopping",
                  isRecipe: false,
                  onImport: onImport,
                  onCreate: onCreate,
                );
              }

              // Loading indicator at the end if hasMore
              if (hasMore && index == items.length + 1) {
                return const Center(child: CircularProgressIndicator());
              }

              // Regular items (adjust index for create card)
              final itemIndex = index - 1;
              if (itemIndex < items.length) {
                return ShoppingCard(
                  shopping: items[itemIndex],
                  isHighRes: isLargeScreen,
                  shoppingIndex: itemIndex,
                  onTap: () => onItemTap(itemIndex),
                  selectedTab: selectedTab,
                );
              }

              return const SizedBox.shrink();
            },
          );
        } else {
          return ListView.builder(
            padding: const EdgeInsets.all(8),
            itemCount: items.length + 1 + (hasMore ? 1 : 0), // +1 for create card
            itemBuilder: (context, index) {
              // Create card at index 0
              if (index == 0) {
                return ImportCreateCardMobileList(
                  importText: "Import Shopping",
                  title: "Create Shopping",
                  isRecipe: false,
                  onImport: onImport,
                  onCreate: onCreate,
                );
              }

              // Loading indicator at the end if hasMore
              if (hasMore && index == items.length + 1) {
                return const Center(child: CircularProgressIndicator());
              }

              // Regular items (adjust index for create card)
              final itemIndex = index - 1;
              if (itemIndex < items.length) {
                return ShoppingListviewItems(
                  shopping: items[itemIndex],
                  isHighRes: isLargeScreen,
                  shoppingIndex: itemIndex,
                  onTap: () => onItemTap(itemIndex),
                  selectedTab: selectedTab,
                );
              }

              return const SizedBox.shrink();
            },
          );
        }
      },
    );
  }
}