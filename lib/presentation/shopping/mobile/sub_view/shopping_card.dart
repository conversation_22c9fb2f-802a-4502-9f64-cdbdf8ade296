import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../../core/data/models/shopping.dart';
import '../../../../core/utils/Utils.dart';
import '../../../../core/widgets/camera_upload_image.dart';
import '../../../../core/widgets/custom_hover_menu.dart';
import '../../../../core/widgets/custom_text.dart';

class ShoppingCard extends ConsumerWidget {
  final Shopping shopping;
  final bool isHighRes;
  final VoidCallback onTap;
  final String selectedTab;
  final int shoppingIndex;

  ShoppingCard({
    super.key,
    required this.shopping,
    required this.isHighRes,
    required this.onTap,
    required this.selectedTab,
    required this.shoppingIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final shoppingListNotifier = ref.read(shoppingNotifierProvider.notifier);
    final shoppingNotifier = ref.read(shoppingNotifierProvider.notifier);
    final shoppingListState = ref.watch(shoppingNotifierProvider);

    final isDeleting = shoppingListState.status == AppStatus.loading;

    return GestureDetector(
      onTap: () {
        if (!isDeleting) {
          context.go(
              '/shopping/mobile_shopping_list_detail_screen?tab=$selectedTab&index=$shoppingIndex');
        }
      },
      child: Card(
          color: context.theme.cardColor,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(15),
                        bottom: Radius.circular(15),
                      ),
                      child: CommonImage(
                        imageSource: shopping.imageUrl,
                        placeholder: AssetsManager.shoppingDummy,
                        height: 133.0,
                        width: double.infinity,
                        fit: BoxFit.cover,
                      ),
                    ),
                    cameraUploadImage(onTap: () {
                      shoppingNotifier.pickImage(
                          context, ref, shopping.id.toString(), shopping.title);
                    })
                  ],
                ),
                const SizedBox(height: 10.0),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: CustomText(
                            text: shopping.title,
                            color: AppColors.primaryGreyColor,
                            size: 14,
                            weight: FontWeight.w500,
                            maxLine: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 15.0),
                        CustomHoverMenu(
                          items: ['Edit', 'Delete'],
                          itemIcons: [Icons.edit, Icons.delete],
                          onItemSelected: (value) async {
                            if (value == 'Edit') {
                              onTap();
                            } else if (value == 'Delete') {
                              if (!isDeleting) {
                                final bool? confirmed =
                                    await Utils().showCommonConfirmDialog(
                                  context: context,
                                  title: 'Delete Shopping List',
                                  subtitle:
                                      'Are you sure you want to delete "${shopping.title}" shopping list?',
                                  confirmText: 'Delete',
                                  cancelText: 'Cancel',
                                );

                                if (confirmed != true || !context.mounted) {
                                  return;
                                }

                                final result = await shoppingListNotifier
                                    .deleteShoppingList(
                                        context, shopping.id.toString());

                                if (!context.mounted) {
                                  return;
                                }

                                if (result.$1 == AppStatus.success) {
                                  shoppingNotifier.fetchShoppingLists(
                                      context: context);
                                }
                              }
                            }
                          },
                          menuWidth: 140.0,
                          menuTitle: 'Show Menu',
                          triggerIcon: Icons.more_vert_outlined,
                        ),
                      ],
                    ),
                    const SizedBox(height: 6.0),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: '${shopping.recipeCount} products',
                          color: AppColors.textGreyColor,
                          size: 12.0,
                          weight: FontWeight.w400,
                        ),
                        CustomText(
                          text:
                              ' ${timeago.format(DateTime.parse(shopping.createdDate), locale: 'en_short')}',
                          color: AppColors.textGreyColor,
                          size: 12.0,
                          weight: FontWeight.w400,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }
}
