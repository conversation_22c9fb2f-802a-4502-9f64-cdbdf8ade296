import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
 import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/core/providers/shopping/page_state_provider.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/shopping_card.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/shopping_pantry_bottom_sheet_content.dart';
import '../../../../core/data/models/shopping.dart';
import '../../../../core/helpers/local_storage_service.dart';
import '../../../../core/network/app_status.dart';

import '../../../../core/widgets/paginated_responsive_grid_list.dart';
import '../../../cookbook/mobile/sub_views/import_create_card_mobile.dart';
import '../../../cookbook/mobile/sub_views/import_create_card_mobile_list.dart';
import '../../../shimer/cookbook_shimmer.dart';
import 'package:mastercookai/presentation/shopping/mobile/mobile_shopping_list_screen.dart';
import 'package:mastercookai/core/data/models/view_type.dart';
import 'shopping_listview_items.dart';

class ShoppingView extends ConsumerStatefulWidget {
  final Size screenSize;
  final bool isHighRes;
  final String? searchQuery;
  final String selectedTab;

  ShoppingView({
    Key? key,
    required this.screenSize,
    required this.isHighRes,
    this.searchQuery,
    required this.selectedTab,
  }) : super(key: key);

  @override
  ConsumerState<ShoppingView> createState() => _ShoppingViewState();
}

class _ShoppingViewState extends ConsumerState<ShoppingView>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  Widget _buildShoppingItem(dynamic item, int index, ViewType currentViewType) {
    if (index == 0) {
      // Create card
      return currentViewType == ViewType.grid
          ? ImportCreateCardMobile(
              importText: "Import Shopping",
              title: "Create Shopping",
              isRecipe: false,
              onImport: () {},
              onCreate: () {
                ref
                    .read(shoppingNotifierProvider.notifier)
                    .showShoppingBottomSheet(
                  context: context,
                );
              },
            )
          : ImportCreateCardMobileList(
              importText: "Import Shopping",
              title: "Create Shopping",
              isRecipe: false,
              onImport: () {},
              onCreate: () => ref
                  .read(pageStateProvider.notifier)
                  .showShoppinDialog(context, ref),
            );
    }

    // Shopping list item
    final shoppingState = ref.watch(shoppingNotifierProvider);
    final shoppingIndex = index - 1;
    if (shoppingState.data?.shoppingLists == null ||
        shoppingIndex >= shoppingState.data!.shoppingLists!.length) {
      return const SizedBox
          .shrink(); // Return an empty widget if the index is out of bounds
    }
    final shoppingList = shoppingState.data!.shoppingLists![shoppingIndex];

    final shopping = Shopping(
      id: shoppingList.id!.toInt(),
      title: shoppingList.name ?? '',
      imageUrl: shoppingList.coverImageUrl ?? '',
      recipeCount: shoppingList.shoppingItemsCount.toString(),
      createdDate: shoppingList.dateCreated.toString(),
    );

    return currentViewType == ViewType.grid
        ? ShoppingCard(
            shopping: shopping,
            isHighRes: widget.isHighRes,
            shoppingIndex: shoppingIndex,
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder: (context) {
                  final keyboardHeight =
                      MediaQuery.of(context).viewInsets.bottom;
                  final screenHeight = MediaQuery.of(context).size.height;

                  // More precise height calculation for compact content
                  final contentHeight = 280.0; // Estimated content height
                  final baseHeight = contentHeight + 40; // Content + padding
                  final minHeight = screenHeight * 0.25; // 25% minimum
                  final maxHeight = screenHeight * 0.7; // 70% maximum

                  final adjustedHeight = keyboardHeight > 0
                      ? (baseHeight + keyboardHeight * 0.8)
                          .clamp(minHeight, maxHeight)
                      : baseHeight.clamp(minHeight, maxHeight);

                  return SizedBox(
                    height: adjustedHeight,
                    child: ShoppingPantryBottomSheetContent(
                      title: 'Update Shopping List',
                      hintText: 'Enter shopping list name',
                      successText: 'Shopping list updated successfully',
                      buttonText: 'Update',
                      successButtonText: 'Done',
                      scrollController: ScrollController(),
                      isShoppingList: true,
                      callFromUpdate: true,
                      listId: shoppingList.id.toString(),
                      listName: shoppingList.name,
                      onCreate: (name, context, ref) {
                        // Handle creation logic here
                      },
                    ),
                  );
                },
              );
            },
            selectedTab: widget.selectedTab,
          )
        : ShoppingListviewItems(
            shopping: shopping,
            isHighRes: widget.isHighRes,
            shoppingIndex: shoppingIndex,
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder: (context) {
                  final keyboardHeight =
                      MediaQuery.of(context).viewInsets.bottom;
                  final screenHeight = MediaQuery.of(context).size.height + 300;

                  // More precise height calculation for compact content
                  final contentHeight = 280.0; // Estimated content height
                  final baseHeight = contentHeight + 40; // Content + padding
                  final minHeight = screenHeight * 0.25; // 25% minimum
                  final maxHeight = screenHeight * 0.7; // 70% maximum

                  final adjustedHeight = keyboardHeight > 0
                      ? (baseHeight + keyboardHeight * 0.8)
                          .clamp(minHeight, maxHeight)
                      : baseHeight.clamp(minHeight, maxHeight);

                  return SizedBox(
                    height: adjustedHeight,
                    child: ShoppingPantryBottomSheetContent(
                      title: 'Update Shopping List',
                      hintText: 'Enter shopping list name',
                      successText: 'Shopping list updated successfully',
                      buttonText: 'Update',
                      successButtonText: 'Done',
                      scrollController: ScrollController(),
                      isShoppingList: true,
                      callFromUpdate: true,
                      listId: shoppingList.id.toString(),
                      listName: shoppingList.name,
                      onCreate: (name, context, ref) {
                        // Handle creation logic here
                      },
                    ),
                  );
                },
              );
            },
            selectedTab: widget.selectedTab,
          );
  }


  @override
  Widget build(BuildContext context) {
    super.build(context);
    final shoppingState = ref.watch(shoppingNotifierProvider);
    final currentViewType = ref.watch(viewTypeProvider);

    if (shoppingState.status == AppStatus.loading &&
        (shoppingState.data?.shoppingLists?.isEmpty ?? true)) {
      return CookbookShimmer(
        crossAxisCount: 2,
        itemsPerPage: 10,
      );
    }

 

    return Padding(
      padding: const EdgeInsets.only(top: 16.0, bottom: 0.0),
      child: PaginatedResponsiveGridList<dynamic>(
        items: [
          // Add placeholder for create card
          'create_shopping_card',
          // Add all shopping lists
          ...(shoppingState.data?.shoppingLists ?? [])
        ],
        itemBuilder: (item, index) =>
            _buildShoppingItem(item, index, currentViewType),
        desiredItemWidth: 164,
        minSpacing: 10,
        pageSize: 20,
        viewType: currentViewType,
        onLoadMore: () {
          if (shoppingState.hasMore &&
              shoppingState.status != AppStatus.loadingMore) {
            ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists(
                  loadMore: true,
                  context: context,
                  search: widget.searchQuery,
                );
          }
        },
      ),
    );
  }
}
