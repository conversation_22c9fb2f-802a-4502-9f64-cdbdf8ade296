import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/pantry.dart';
import 'package:mastercookai/core/data/models/shopping.dart';
import 'package:mastercookai/core/providers/shopping/pantry_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/core/providers/shopping/shopping_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/core/widgets/no_data_widget.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/add_pantry_product_bottom_sheet.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/add_product_bottom_sheet.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/mobile_pantry_list_item.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/mobile_shopping_list_item.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/pantry_helpers.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/shopping_helpers.dart';
import '../../../app/assets_manager.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../app/theme/colors.dart';
import 'package:mastercookai/core/network/app_status.dart';

class MobileShoppingListDetail extends ConsumerStatefulWidget {
  const MobileShoppingListDetail({super.key});

  @override
  ConsumerState<MobileShoppingListDetail> createState() =>
      _MobileShoppingListDetailState();
}

class _MobileShoppingListDetailState
    extends ConsumerState<MobileShoppingListDetail> {
  String _selectedTab = '';
  int _selectedIndex = 0;
  final Set<int> _selectedShoppingItemIds = {};
  final Set<int> _selectedPantryItemIds = {};
  bool _isAllShoppingItemsSelected = false;
  bool _isAllPantryItemsSelected = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final queryParams = GoRouterState.of(context).uri.queryParameters;
      final tabParam = queryParams['tab'] ?? 'Shopping';
      final indexParam = queryParams['index'] ?? '0';

      setState(() {
        _selectedTab = tabParam;
        _selectedIndex = int.tryParse(indexParam) ?? 0;
      });

      _fetchInitialData();
    });
  }

  void _fetchInitialData() {
    if (_selectedTab == 'Shopping') {
      // Clear any existing shopping data before fetching new data
      ref.read(shoppingItemNotifierProvider.notifier).clearData();

      final shoppingState = ref.read(shoppingNotifierProvider);
      final shoppingLists = shoppingState.data?.shoppingLists ?? [];

      if (shoppingLists.isNotEmpty && _selectedIndex < shoppingLists.length) {
        final selectedShoppingListId =
            shoppingLists[_selectedIndex].id!.toInt();
        ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
              id: selectedShoppingListId,
              search: null,
            );
      }
    } else {
      // Clear any existing pantry data before fetching new data
      ref.read(pantryItemNotifierProvider.notifier).clearData();

      final pantryState = ref.read(pantryNotifierProvider);
      final pantryLists = pantryState.data?.pantries ?? [];

      if (pantryLists.isNotEmpty && _selectedIndex < pantryLists.length) {
        final selectedPantryListId = pantryLists[_selectedIndex].id;
        ref.read(pantryItemNotifierProvider.notifier).fetchPantryItems(
              id: selectedPantryListId ?? 0,
              search: null,
            );
      }
    }
  }

  void _onShoppingCheckboxChanged(int itemId, bool? value) {
    setState(() {
      if (itemId == -1) {
        final shoppingItemState = ref.read(shoppingItemNotifierProvider);
        final items = shoppingItemState.data?.shoppingItems ?? [];

        if (value == true) {
          _selectedShoppingItemIds.clear();
          for (var item in items) {
            _selectedShoppingItemIds.add(item.id);
          }
          _isAllShoppingItemsSelected = true;
        } else {
          _selectedShoppingItemIds.clear();
          _isAllShoppingItemsSelected = false;
        }
      } else {
        if (value == true) {
          _selectedShoppingItemIds.add(itemId);
        } else {
          _selectedShoppingItemIds.remove(itemId);
        }
        final shoppingItemState = ref.read(shoppingItemNotifierProvider);
        _isAllShoppingItemsSelected = _selectedShoppingItemIds.length ==
            (shoppingItemState.data?.shoppingItems.length ?? 0);
      }
    });
  }

  void _onPantryCheckboxChanged(int itemId, bool? value) {
    setState(() {
      if (itemId == -1) {
        final pantryItemState = ref.read(pantryItemNotifierProvider);
        final items = pantryItemState.data?.pantryItems ?? [];

        if (value == true) {
          _selectedPantryItemIds.clear();
          for (var item in items) {
            _selectedPantryItemIds.add(item.id);
          }
          _isAllPantryItemsSelected = true;
        } else {
          _selectedPantryItemIds.clear();
          _isAllPantryItemsSelected = false;
        }
      } else {
        if (value == true) {
          _selectedPantryItemIds.add(itemId);
        } else {
          _selectedPantryItemIds.remove(itemId);
        }
        final pantryItemState = ref.read(pantryItemNotifierProvider);
        _isAllPantryItemsSelected = _selectedPantryItemIds.length ==
            (pantryItemState.data?.pantryItems.length ?? 0);
      }
    });
  }

  Future<void> _onDelete() async {
    if (_selectedTab == 'Shopping') {
      final (success, message) = await deleteSelectedShoppingItems(
        context: context,
        ref: ref,
        selectedTab: _selectedTab,
        selectedIndex: _selectedIndex,
        selectedShoppingItemIds: _selectedShoppingItemIds,
        isAllShoppingItemsSelected: _isAllShoppingItemsSelected,
      );

      if (!success) {
        debugPrint("Delete failed: $message");
      } else {
        debugPrint("Delete success");
        setState(() {
          _selectedShoppingItemIds.clear();
          _isAllShoppingItemsSelected = false;
        });
      }
    } else {
      final (success, message) = await deleteSelectedPantryItems(
        context: context,
        ref: ref,
        selectedTab: _selectedTab,
        selectedIndex: _selectedIndex,
        selectedPantryItemIds: _selectedPantryItemIds,
        isAllPantryItemsSelected: _isAllPantryItemsSelected,
      );

      if (!success) {
        debugPrint("Pantry delete failed: $message");
      } else {
        setState(() {
          _selectedPantryItemIds.clear();
          _isAllPantryItemsSelected = false;
        });
      }
    }
  }

  Future<void> _onMarkPurchased() async {
    bool success = await markSelectedShoppingItemsAsPurchased(
      context: context,
      ref: ref,
      selectedTab: _selectedTab,
      selectedIndex: _selectedIndex,
      selectedShoppingItemIds: _selectedShoppingItemIds,
      isAllShoppingItemsSelected: _isAllShoppingItemsSelected,
    );
    if (success) {
      setState(() {
        _selectedShoppingItemIds.clear();
        _isAllShoppingItemsSelected = false;
      });
    }
  }

  Widget _buildBody() {
    if (_selectedTab == 'Shopping') {
      final shoppingItemState = ref.watch(shoppingItemNotifierProvider);

      if (shoppingItemState.status == AppStatus.loading) {
        return const Center(child: null);
      }

      if (shoppingItemState.status == AppStatus.error) {
        return const Center(child: Text('An error occurred'));
      }

      final items = shoppingItemState.data?.shoppingItems;

      if (items == null || items.isEmpty) {
        return SizedBox(
          width: double.infinity,
          child: const Center(
            child: NoDataWidget(
              title: 'No Items Found',
              subtitle:
                  'Add items to your list to get started.',
              width: 250,
              height: 250,
            ),
          )
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          final shoppingItem = item as ShoppingItem;
          final isSelected =
              _selectedShoppingItemIds.contains(shoppingItem.id);
          final shoppingState = ref.read(shoppingNotifierProvider);
          final shoppingLists =
              shoppingState.data?.shoppingLists ?? [];
          final shoppingListId = shoppingLists.isNotEmpty &&
                  _selectedIndex < shoppingLists.length
              ? shoppingLists[_selectedIndex].id?.toInt()
              : null;
          return ShoppingListItem(
            item: shoppingItem,
            isSelected: isSelected,
            shoppingListId: shoppingListId,
            onChanged: (value) {
              _onShoppingCheckboxChanged(shoppingItem.id, value);
            },
          );
        },
      );
    } else {
      final pantryItemState = ref.watch(pantryItemNotifierProvider);

      if (pantryItemState.status == AppStatus.loading) {
        return const Center(child: null);
      }

      if (pantryItemState.status == AppStatus.error) {
        return const Center(child: Text('An error occurred'));
      }

      final items = pantryItemState.data?.pantryItems;

      if (items == null || items.isEmpty) {
        return SizedBox(
          width: double.infinity,
          child: Container(
            alignment: Alignment.center,
             child: const Center(
              child: NoDataWidget(
                title: 'No Items Found',
                subtitle:
                    'Add items to your list to get started.',
                width: 250,
                height: 250,
              ),
            )
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          final pantryItem = item as PantryItem;
          final isSelected =
              _selectedPantryItemIds.contains(pantryItem.id);
          final pantryState = ref.read(pantryNotifierProvider);
          final pantryLists = pantryState.data?.pantries ?? [];
          final pantryListId = pantryLists.isNotEmpty &&
                  _selectedIndex < pantryLists.length
              ? pantryLists[_selectedIndex].id
              : null;
          return MobilePantryListItem(
            item: pantryItem,
            isSelected: isSelected,
            pantryListId: pantryListId,
            onChanged: (value) {
              _onPantryCheckboxChanged(pantryItem.id, value);
            },
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Return loading screen if _selectedTab is not initialized yet
    if (_selectedTab.isEmpty) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final shoppingState = ref.watch(shoppingNotifierProvider);
    final pantryState = ref.watch(pantryNotifierProvider);

    final String appBarTitle;
    if (_selectedTab == 'Shopping') {
      final shoppingLists = shoppingState.data?.shoppingLists ?? [];
      if (shoppingLists.isNotEmpty && _selectedIndex < shoppingLists.length) {
        appBarTitle = shoppingLists[_selectedIndex].name ?? 'Shopping List';
      } else {
        appBarTitle = 'Shopping List';
      }
    } else {
      final pantryLists = pantryState.data?.pantries ?? [];
      if (pantryLists.isNotEmpty && _selectedIndex < pantryLists.length) {
        appBarTitle = pantryLists[_selectedIndex].name ?? 'Pantry';
      } else {
        appBarTitle = 'Pantry';
      }
    }

    final hasSelectedItems = _selectedTab == 'Shopping'
        ? _selectedShoppingItemIds.isNotEmpty
        : _selectedPantryItemIds.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: SvgPicture.asset(
            AssetsManager.ic_back,
            width: 24,
            height: 24,
          ),
          onPressed: () {
            context.pop();
          },
        ),
        title: Text(
          appBarTitle,
          style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: AppColors.primaryColor),
        ),
        actions: [
          if (_selectedTab == 'Shopping' && hasSelectedItems)
            SizedBox(
              height: 30, // Fixed height
              child: TextButton.icon(
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: AppColors.greyBorderColor),
                  ),
                ),
                onPressed: _onMarkPurchased,
                icon: SvgPicture.asset(AssetsManager.ic_purchased),
                label: Text(
                  "Purchased",
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                    color: AppColors.primaryGreyColor,
                  ),
                ),
              ),
            ),
          if (hasSelectedItems) ...[
            const SizedBox(width: 5),
            SizedBox(
              height: 30, // Fixed height
              width: 30, // Fixed width,
              child: IconButton(
                style: IconButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: AppColors.greyBorderColor),
                  ),
                ),
                onPressed: _onDelete,
                icon: SvgPicture.asset(AssetsManager.ic_dlt_red),
              ),
            ),
          ],
          const SizedBox(width: 8),
          TextButton.icon(
            onPressed: () {
              if (_selectedTab == 'Shopping') {
                final shoppingState = ref.read(shoppingNotifierProvider);
                final shoppingLists = shoppingState.data?.shoppingLists ?? [];
                final shoppingListId = shoppingLists.isNotEmpty &&
                        _selectedIndex < shoppingLists.length
                    ? shoppingLists[_selectedIndex].id?.toInt()
                    : null;
                showAddProductBottomSheet(context,
                    shoppingListId: shoppingListId);
              } else {
                final pantryState = ref.read(pantryNotifierProvider);
                final pantryLists = pantryState.data?.pantries ?? [];
                final pantryListId = pantryLists.isNotEmpty &&
                        _selectedIndex < pantryLists.length
                    ? pantryLists[_selectedIndex].id
                    : null;
                showAddPantryProductBottomSheet(context,
                    pantryListId: pantryListId);
              }
            },
            icon: SvgPicture.asset(
              AssetsManager.add,
              height: 17,
              width: 17,
            ),
            label: const Text(
              "Add",
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: 12,
                color: AppColors.texLightBlackColor,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }
}
