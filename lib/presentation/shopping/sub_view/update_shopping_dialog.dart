import 'package:mastercookai/presentation/shopping/sub_view/shopping_dialog.dart';

import '../../../app/imports/core_imports.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/providers/shopping/shopping_notifier.dart';

void showUpdateShoppingDialog(
    BuildContext context,
    WidgetRef ref, {
      String? shoppingId,
      String? shoppingName,
    }) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => ShoppingDialog(
      dialogType: DialogType.shopping,
      title: "Edit Shopping",
      hintText: "Edit Shopping Name",
      successText: "Shopping Updated \nSuccessfully",
      buttonText: "Update",
      successButtonText: "Done",
      shoppingId: shoppingId,
      shoppingName: shoppingName,
      callFromUpdate: true,
    ),
  ).then((_) {
    // ✅ Dialog already dismissed — just refresh data
    ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists();
  });
}
