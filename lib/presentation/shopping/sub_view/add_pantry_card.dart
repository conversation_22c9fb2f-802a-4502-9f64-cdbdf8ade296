import 'package:flutter/material.dart';

class AddPantryCard extends StatelessWidget {
  const AddPantryCard({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 200,
      child: Card(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 2,
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Import Pantry', style: TextStyle(color: Colors.black54)),
                const SizedBox(height: 8),
                const Text('or'),
                const SizedBox(height: 8),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  onPressed: () {},
                  child: const Text('Add new Pantry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
