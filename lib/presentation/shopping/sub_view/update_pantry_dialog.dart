import 'package:mastercookai/presentation/shopping/sub_view/shopping_dialog.dart';
import '../../../app/imports/packages_imports.dart';
 import '../../../core/providers/shopping/pantry_notifier.dart';

void showUpdatePantryDialog(BuildContext context, WidgetRef ref,{String? pantryId, String? pantryName}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => ShoppingDialog(
      dialogType: DialogType.pantry,
      title: "Edit Pantry",
      hintText: "Edit Pantry Name",
      successText: "Pantry Updated \nSuccessfully",
      buttonText: "Update",
      successButtonText: "Done",
      shoppingId: pantryId,
      shoppingName: pantryName,
      callFromUpdate: true,
    ),
  ).then((_) {
    // Refresh pantry lists when dialog closes
    ref.read(pantryNotifierProvider.notifier).fetchPantryLists();
  });
}