import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text_field.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/shopping/pantry_notifier.dart';
import '../../../core/providers/shopping/shopping_notifier.dart';
 import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_input_field.dart';
import '../../../core/widgets/custom_text.dart';

enum DialogType { shopping, pantry }

class ShoppingDialog extends HookConsumerWidget {
  final DialogType dialogType;
  final String title;
  final String hintText;
  final String successText;
  final String buttonText;
  final String successButtonText;
  final bool callFromUpdate;
  final VoidCallback? onSuccess;
  final Function(String, BuildContext, WidgetRef)? onCreate;
  final String? shoppingId;
  final String? shoppingName;

  const ShoppingDialog({
    super.key,
    required this.dialogType,
    required this.title,
    required this.hintText,
    required this.successText,
    required this.buttonText,
    required this.successButtonText,
    this.onSuccess,
    this.onCreate,
    this.shoppingId,
    this.shoppingName,
    this.callFromUpdate = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = dialogType == DialogType.shopping
        ? ref.read(shoppingNotifierProvider.notifier)
        : ref.read(pantryNotifierProvider.notifier);
    final appState = dialogType == DialogType.shopping
        ? ref.watch(shoppingNotifierProvider)
        : ref.watch(pantryNotifierProvider);
    final nameController = useTextEditingController(text: shoppingName ?? '');
    final formKey = useMemoized(() => GlobalKey<FormState>());

    // Show SnackBar for errors
    ref.listen(dialogType == DialogType.shopping ? shoppingNotifierProvider : pantryNotifierProvider, (previous, next) {
       if (next.status == (callFromUpdate ? AppStatus.updateError : AppStatus.error) && next.errorMessage != null) {
        Utils().showSnackBar(context, next.errorMessage!);
      }
    });


    return Dialog(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(26)),
      child: Container(
        padding: EdgeInsets.only(left: 25, right: 25, top: 20, bottom: 15),
        width: 500,
        child: appState.status == (callFromUpdate ? AppStatus.updateSuccess : AppStatus.createSuccess)
            ? _buildSuccessContent(context, ref, successText, successButtonText)
            : _buildFormContent(context, ref, nameController, formKey, notifier),
      ),
    );
  }

  Widget _buildFormContent(
      BuildContext context,
      WidgetRef ref,
      TextEditingController controller,
      GlobalKey<FormState> formKey, Object notifier,

      ) {
    final appState = dialogType == DialogType.shopping
        ? ref.watch(shoppingNotifierProvider)
        : ref.watch(pantryNotifierProvider);

    return Form(
      key: formKey,
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _closeButton(context, ref),

              CustomText(
                text:  title,
                color: AppColors.blackTextColor,
                size: DeviceUtils().isTabletOrIpad(context) ? 24 : 24,
                weight: FontWeight.w700,
              ),

              SizedBox(height: 50),
              Stack(
                children: [
                  SizedBox(
                    width: DeviceUtils().isTabletOrIpad(context) ? 500 : 300,
                    child: Container(
                      padding: EdgeInsets.only(left: 20, right: 20),
                      child:
                      CustomInputField(
                          hintText:hintText,
                          controller: controller,
                          keyboardType: TextInputType.text,
                        // autoFocus: true,
                        // formats: [
                        //   FilteringTextInputFormatter.allow(
                        //     RegExp(r'[a-zA-Z0-9\s]'),
                        //   ),
                        // ],
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter ${dialogType == DialogType.shopping ? 'shopping' : 'pantry'} name';
                          }
                          if (value.trim().length < 3) {
                            return '${dialogType == DialogType.shopping ? 'Shopping' : 'Pantry'} name must be at least 3 characters';
                          }
                          if (value.trim().length > 100) {
                            return '${dialogType == DialogType.shopping ? 'Shopping' : 'Pantry'} name must not exceed 100 characters';
                          }
                          if (RegExp(r'\s{2,}').hasMatch(value)) {
                            return '${dialogType == DialogType.shopping ? 'Shopping' : 'Pantry'} name should not contain multiple spaces in a row';
                          }
                          if (value != value.trim()) {
                            return '${dialogType == DialogType.shopping ? 'Shopping' : 'Pantry'} name should not start or end with a space';
                          }
                          return null;
                        },
                        onChanged: (val) {},
                      ),

                    ),
                  ),
                ],
              ),
              SizedBox(height: 50),
              CustomButton(
                width: DeviceUtils().isTabletOrIpad(context) ? 180 : 200,
                fontSize: DeviceUtils().isTabletOrIpad(context) ? 18 : 22,
                text: buttonText,
                isLoading: appState.status == (callFromUpdate ? AppStatus.updating : AppStatus.creating),
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    if (onCreate != null) {
                      await onCreate!(controller.text.trim(), context, ref);
                    } else {
                      if (callFromUpdate) {
                        if (dialogType == DialogType.shopping) {
                          await ref.read(shoppingNotifierProvider.notifier).updateShopping(
                            context,
                            id: shoppingId!,
                            name: controller.text.trim(),
                            filePath: File(''),
                            callFromUpdate: true,
                          );
                        } else {
                          await ref.read(pantryNotifierProvider.notifier).updatePantry(
                            context,
                            id: shoppingId!,
                            name: controller.text.trim(),
                            filePath: File(''),
                            callFromUpdate: true,
                          );
                        }
                      } else {
                        if (dialogType == DialogType.shopping) {
                          await ref.read(shoppingNotifierProvider.notifier).createShoppingList(
                            context,
                            controller.text.trim(),
                          );
                        } else {
                          await ref.read(pantryNotifierProvider.notifier).createPantryList(
                            context,
                            controller.text.trim(),
                          );
                        }
                      }
                    }
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessContent(
      BuildContext context,
      WidgetRef ref,
      String successResponse,
      String buttonText,
      ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
          alignment: Alignment.topRight,
          child: IconButton(
            icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
            onPressed: () {
              Navigator.of(context).pop();
              if (dialogType == DialogType.shopping) {
                ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists(context: context);
              } else {
                ref.read(pantryNotifierProvider.notifier).fetchPantryLists(context: context);
              }
            },
          ),
        ),
        SvgPicture.asset(
          AssetsManager.success,
          height: 80,
          width: 80,
        ),
        SizedBox(height: 40),
        Center(
          child:
          CustomText(
            text:  successResponse,
            color: AppColors.primaryGreyColor,
            size: DeviceUtils().isTabletOrIpad(context) ? 24 : 24,
            weight: FontWeight.w400,
            align: TextAlign.center,
          ),
        ),
        SizedBox(height: 40),
        CustomButton(
          width: DeviceUtils().isTabletOrIpad(context) ? 180 : 250,
          fontSize: DeviceUtils().isTabletOrIpad(context) ? 18 : 22,
          isLoading: (dialogType == DialogType.shopping
              ? ref.watch(shoppingNotifierProvider)
              : ref.watch(pantryNotifierProvider))
              .status == AppStatus.loading,
          text: buttonText,
          onPressed: () async {
            Navigator.of(context).pop();
            if (dialogType == DialogType.shopping) {
              await ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists(context: context);
            } else {
              await ref.read(pantryNotifierProvider.notifier).fetchPantryLists(context: context);
            }
          },
        ),
      ],
    );
  }

  Widget _closeButton(BuildContext context, WidgetRef ref) {
    return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
        onPressed: () {
          // Reset the notifier state when closing
          if (dialogType == DialogType.shopping) {
            ref.read(shoppingNotifierProvider.notifier).resetToIdle();
          } else {
            ref.read(pantryNotifierProvider.notifier).resetToIdle();
          }
          Navigator.of(context).pop();
        },
      ),
    );
  }
}