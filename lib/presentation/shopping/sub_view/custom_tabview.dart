import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

class CustomTabView extends StatelessWidget {
  final List<String> tabs;
  final String selected;
  final double? height;
  final double? width;
  final double? fontSize;
  final void Function(String) onChanged;
  final Color selectedTabColor;
  final Color bgTabColor;

  const CustomTabView({super.key,
      required this.tabs,
      required this.selected,
      required this.onChanged,
      required this.fontSize,
      required this.selectedTabColor,
      required this.bgTabColor,
      this.height,
      this.width});

  @override
  Widget build(BuildContext context) {
    return Container(
      // width: width ?? 500.w,
      height: height ?? 70.h,
      margin: EdgeInsets.only(left: 20.w, right: 20.w),
      decoration: BoxDecoration(
        color: bgTabColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color:AppColors.greyBorderColor , width: 1)

      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: tabs.map((tab) {
          final isSelected = tab == selected;
          return GestureDetector(
            onTap: () => onChanged(tab),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 250),
              decoration: BoxDecoration(
                color: isSelected ? selectedTabColor : Colors.transparent,
                borderRadius: BorderRadius.circular(10),
                // border: Border(
                //   left: BorderSide(color: AppColors.greyCardColor.withOpacity(0.1), width: 1),
                //   top: BorderSide(color: AppColors.greyCardColor.withOpacity(0.1), width: 1),
                //   bottom: BorderSide(color: AppColors.greyCardColor.withOpacity(0.1), width: 1),
                //   // No right border
                // ),
              ),
              child: SizedBox(
                width: width ?? 300.w,
                child: Center(
                  child: Text(tab,
                      style: context.theme.textTheme.labelMedium!.copyWith(
                        color: isSelected ? AppColors.primaryGreyColor : AppColors.primaryHintColor,
                        fontSize: fontSize,
                        fontWeight: FontWeight.w400,
                      )),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
