import 'package:flutter_svg/svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/screen_sizer.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import '../../../app/imports/packages_imports.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../../../core/widgets/custom_button.dart';
import '../../../../core/data/models/pantry.dart';
import '../../../core/providers/shopping/pantry_notifier.dart';
import '../../../core/utils/Utils.dart';
import '../../../core/widgets/camera_upload_image.dart';
import '../../../core/widgets/custom_hover_menu.dart';

class PantryCard extends ConsumerWidget {
  final Pantry pantry;
  final bool isHighRes;
  final VoidCallback onTap;
  final String selectedTab;
  final int pantryIndex;

  PantryCard({
    super.key,
    required this.pantry,
    required this.isHighRes,
    required this.onTap,
    required this.selectedTab,
    required this.pantryIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenSize = MediaQuery.of(context).size;

    final pantryListNotifier = ref.read(pantryNotifierProvider.notifier);
    final pantryListState = ref.watch(pantryNotifierProvider);

    final pantryListNotifierNew = ref.read(pantryNotifierProvider.notifier);
    final pantryListStateNew = ref.watch(pantryNotifierProvider);

    // Show loading indicator when deleting
    final isDeleting = pantryListState.status == AppStatus.loading;

    return Card(
      color: context.theme.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(10),
        onTap: () {
          context.go('/shopping/shopping_detail_views');
          // Navigate to cookbook details
        },
        child: Padding(
          padding: EdgeInsets.only(
              left: screenSize.width * 0.005,
              right: screenSize.width * 0.005,
              top: screenSize.width * 0.005),
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Stack(
                    children: [
                      ClipRRect(
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(15),
                            bottom: Radius.circular(15),
                          ),
                          child: CommonImage(
                              imageSource: pantry.imageUrl,
                              width: screenSize.width,
                              fit: BoxFit.cover,
                              placeholder: AssetsManager.shoppingDummy,
                              height:
                                  ScreenSizer().calculateImageHeight(context))),
                      cameraUploadImage(onTap: () {
                        pantryListNotifierNew.pickImage(
                          context,
                          ref,
                          pantry.id.toString(),
                          pantry.title,
                        );
                      })
                    ],
                  ),
                  SizedBox(height: 16.h),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Text(
                              pantry.title,
                              style:
                                  context.theme.textTheme.bodyMedium!.copyWith(
                                color: AppColors.primaryGreyColor,
                                fontSize: DeviceUtils().isTabletOrIpad(context)
                                    ? 14
                                    : responsiveFont(28).sp,
                                fontWeight: FontWeight.w400,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          CustomHoverMenu(
                            items: ['Edit', 'Delete'],
                            itemIcons: [Icons.edit, Icons.delete],
                            // Icons for each item
                            onItemSelected: (value) async {
                              if (value == 'Edit') {
                                onTap();
                              } else if (value == 'Delete') {
                                if (!isDeleting) {
                                  final currentContext =
                                      context; // Store context before async operation

                                  // Show confirmation dialog
                                  final bool? confirmed =
                                      await Utils().showCommonConfirmDialog(
                                    context: currentContext,
                                    title: 'Delete Pantry List',
                                    subtitle:
                                        'Are you sure you want to delete "${pantry.title}" pantry list?',
                                    confirmText: 'Delete',
                                    cancelText: 'Cancel',
                                  );

                                  if (confirmed != true) {
                                    return; // User cancelled deletion
                                  }

                                  await pantryListNotifier.deletePantryList(
                                      context, pantry.id.toString());
                                }
                              }
                            },
                            menuWidth: 140.0,
                            // Matches previous menu width
                            menuTitle: 'Show Menu',
                            // Tooltip on hover
                            triggerIcon:
                                Icons.more_vert_outlined, // Custom trigger icon
                          ),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('${pantry.recipeCount} products',
                              style:
                                  context.theme.textTheme.labelSmall!.copyWith(
                                color: AppColors.textGreyColor,
                                fontSize: DeviceUtils().isTabletOrIpad(context)
                                    ? 12
                                    : 22.sp,
                                fontWeight: FontWeight.normal,
                              )),
                          Text(
                              'Created : ${timeago.format(DateTime.parse(pantry.createdDate), locale: 'en_short')}',
                              style:
                                  context.theme.textTheme.labelSmall!.copyWith(
                                color: AppColors.textGreyColor,
                                fontSize: DeviceUtils().isTabletOrIpad(context)
                                    ? 10
                                    : 20.sp,
                                fontWeight: FontWeight.normal,
                              )),
                        ],
                      ),
                      SizedBox(height: 10.h),
                      CustomButton(
                          fontSize: DeviceUtils().isTabletOrIpad(context)
                              ? 14
                              : 20.sp,
                          text: 'Open Pantry List',
                          onPressed: () {
                            //context.go('/shopping/shopping_detail_views');
                            context.go(
                                '/shopping/shopping_detail_views?tab=$selectedTab&index=$pantryIndex');
                          }),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
