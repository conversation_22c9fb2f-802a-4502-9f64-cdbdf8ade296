import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
 import '../../../core/data/models/pantry.dart';
import '../../../core/data/models/pantry_response.dart';
import '../../../core/data/models/shopping_response.dart';
import '../../../core/widgets/custom_hover_menu.dart';
import '../../cookbook/widgets/custom_desc_text.dart';


class ShoppingListDetailCard extends StatelessWidget {
  final dynamic listItem; // Can be either ShoppingLists, PantryList, or Pantry (old static)
  final bool isSelected;
  final bool isPantry;
  final void Function(String) onMenuItemSelected; // Callback for menu item selection


    ShoppingListDetailCard({
    super.key,
    required this.listItem,
    this.isSelected = false,
    this.isPantry = false,
      required this.onMenuItemSelected,
  });

  String? get _name {
    if (isPantry) {
      // Handle both PantryList (new API) and Pantry (old static)
      if (listItem is PantryList) {
        return (listItem as PantryList).name;
      } else {
        return (listItem as Pantries).name;
      }
    } else {
      return (listItem as ShoppingLists).name ?? '';
    }
  }

  String get _imageUrl {
    if (isPantry) {
      // Handle both PantryList (new API) and Pantry (old static)
      if (listItem is PantryList) {
        return (listItem as PantryList).coverImageUrl ?? "";
      } else {
        return (listItem as Pantries).coverImageUrl??'';
      }
    } else {
      return (listItem as ShoppingLists).coverImageUrl?.toString() ?? "";
    }
  }

  int? get _itemCount {
    if (isPantry) {
      // Handle both PantryList (new API) and Pantry (old static)
      if (listItem is PantryList) {
        return (listItem as PantryList).pantryItemCount;
      } else {
        return (listItem as Pantries).pantryItemCount;
      }
    } else {
      return (listItem as ShoppingLists).shoppingItemsCount?.toInt() ?? 0;
    }
  }

  String? get _creationDate {
    if (isPantry) {
      // Handle both PantryList (new API) and Pantry (old static)
      if (listItem is PantryList) {
        return _formatDate((listItem as PantryList).dateCreated);
      } else {
        final dateString =(listItem as Pantries).dateCreated;
        if (dateString != null) {
          try {
            return _formatDate(DateTime.parse(dateString));
          } catch (e) {
            return dateString; // Return the original string if parsing fails
          }
        }
      //  return (listItem as Pantries).dateCreated;
      }
    } else {
      final dateString = (listItem as ShoppingLists).dateCreated;
      if (dateString != null) {
        try {
          return _formatDate(DateTime.parse(dateString));
        } catch (e) {
          return dateString; // Return the original string if parsing fails
        }
      }
      return 'Unknown';
    }
  }

  String _formatDate(DateTime date) {
    // Format the date as you prefer, e.g., "5 days ago"
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else {
      return 'Just now';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: context.theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: Colors.blue, width: 3)
            : BorderSide(color: AppColors.greyBorderColor, width: 1),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.sp),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
               child: CommonImage(imageSource: _imageUrl,
                 placeholder: isPantry
                    ? AssetsManager.pantryDummy
                     : AssetsManager.shoppingDummy,
                 width: DeviceUtils().isTabletOrIpad(context) ? 90 : 150.w,
                       height: DeviceUtils().isTabletOrIpad(context) ? 70 : 150.h,
                        fit: BoxFit.cover,)
              ),
            SizedBox(width: 12.w),
            // Text and Action Section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          _name??'',
                          style: context.theme.textTheme.bodyMedium!.copyWith(
                            color: context.theme.hintColor,
                            fontWeight: FontWeight.w400,
                            fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 30.sp,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      CustomHoverMenu(
                        items: ['Edit', 'Delete'],
                        itemIcons: [Icons.edit, Icons.delete],
                        // Icons for each item
                        onItemSelected: onMenuItemSelected,
                        menuWidth: 140.0,
                        // Matches previous menu width
                        menuTitle: 'Show Menu',
                        // Tooltip on hover
                        triggerIcon:
                        Icons.more_vert_outlined, // Custom trigger icon
                      ),
                    ],
                  ),
                  SizedBox(height: 30.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomDescText(
                        desc: "$_itemCount Products",
                        size: DeviceUtils().isTabletOrIpad(context) ? 12 : 22.sp,
                        textColor: AppColors.textGreyColor,
                      ),
                      SizedBox(width: 40.w),
                      CustomDescText(
                        desc: "Created: $_creationDate",
                        size: DeviceUtils().isTabletOrIpad(context) ? 10 : 17.sp,
                        textColor: AppColors.textGreyColor,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
