import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/core/data/models/pantry_response.dart';
import 'package:mastercookai/core/data/models/shopping_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/shopping_list_card.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/page_state_provider.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/presentation/shimer/shopping_pantry_list_shimmer.dart';
import 'package:mastercookai/presentation/shopping/sub_view/update_shopping_dialog.dart';
import '../../../core/data/models/shopping.dart';
import '../../../core/providers/shopping/pantry_item_notifier.dart';
import '../../../core/providers/shopping/shopping_item_notifier.dart';
import '../../../core/utils/Utils.dart';
import 'add_shopping_list_card.dart';

// Helper method to handle list deletion and fetch next index data
Future<void> handleListDeletion({
  required BuildContext context,
  required WidgetRef ref,
  required String selectedTab,
  required int deletedIndex,
  required Function(int) onIndexChanged,
}) async {
  final shoppingNotifier = ref.read(shoppingNotifierProvider.notifier);
  final pantryNotifier = ref.read(pantryNotifierProvider.notifier);

  // Refresh the lists
  if (selectedTab == 'Shopping list') {
    await shoppingNotifier.fetchShoppingLists(context: context);
  } else {
    await pantryNotifier.fetchPantryLists(context: context);
  }

  if (!context.mounted) return;

  // Get the updated list
  final updatedList = selectedTab == 'Shopping list'
      ? ref.read(shoppingNotifierProvider).data!.shoppingLists!
      : ref.read(pantryNotifierProvider).data!.pantries!;

  // Determine the new index
  int newIndex = -1;
  if (updatedList.isNotEmpty) {
    if (deletedIndex >= updatedList.length) {
      newIndex = updatedList.length - 1; // Select the last item
    } else if (deletedIndex < updatedList.length) {
      newIndex = deletedIndex; // Select the item at the same index
    }
  }

  // Update the selected index
  onIndexChanged(newIndex);

  // Fetch data for the new index
  if (newIndex >= 0 && newIndex < updatedList.length) {
    if (selectedTab == 'Shopping list') {
      // Clear any existing shopping data before fetching new data
      ref.read(shoppingItemNotifierProvider.notifier).clearData();

      final listId = (updatedList[newIndex] as ShoppingLists).id as int;
      await ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
        id: listId, // Handle null case
        search: null,
      );
    } else {
      // Clear any existing pantry data before fetching new data
      ref.read(pantryItemNotifierProvider.notifier).clearData();

      final listId = (updatedList[newIndex] as Pantries).id;
      await ref.read(pantryItemNotifierProvider.notifier).fetchPantryItems(
        id: (listId ?? 0).toInt(), // Convert num? to int
        search: null,
      );
    }
  }
}

Widget buildListContent(
    AppState<ShoppingResponseData> shoppingState,
    AppState<PantryResponseData> pantryState,
    String selectedTab,
    WidgetRef ref,
    int selectedIndex,
    String currentSearchQuery,
    TextEditingController searchController,
    Function(int) onIndexChanged,
    ScrollController listScrollController,
    ) {
  final isShoppingLoading = selectedTab == 'Shopping list' &&
      shoppingState.status == AppStatus.loading &&
      shoppingState.data!.shoppingLists!.isEmpty;

  final isPantryLoading = selectedTab == 'Pantry' &&
      pantryState.status == AppStatus.loading &&
      pantryState.data!.pantries!.isEmpty;

  if (isShoppingLoading || isPantryLoading) {
    return const ShoppingPantryListShimmer(itemCount: 5);
  }

  final isDeleting = selectedTab == 'Shopping list'
      ? shoppingState.status == AppStatus.loading
      : pantryState.status == AppStatus.loading;

  final currentList = selectedTab == 'Shopping list'
      ? shoppingState.data!.shoppingLists!
      : pantryState.data!.pantries!;

  return NotificationListener<ScrollNotification>(
    onNotification: (scrollNotification) {
      if (scrollNotification is ScrollEndNotification &&
          scrollNotification.metrics.pixels ==
              scrollNotification.metrics.maxScrollExtent) {
        final shoppingState = ref.read(shoppingNotifierProvider);
        final pantryState = ref.read(pantryNotifierProvider);

        if (selectedTab == 'Shopping list' && shoppingState.hasMore) {
          ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists(
            loadMore: true,
            search: currentSearchQuery.isEmpty ? null : currentSearchQuery,
          );
        } else if (selectedTab == 'Pantry' && pantryState.hasMore) {
          ref.read(pantryNotifierProvider.notifier).fetchPantryLists(
            loadMore: true,
            search: currentSearchQuery.isEmpty ? null : currentSearchQuery,
          );
        }
      }
      return false;
    },
    child: ListView.separated(
      controller: listScrollController,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      itemCount: currentList.length +
          (((selectedTab == 'Shopping list' && shoppingState.hasMore) ||
              (selectedTab == 'Pantry' && pantryState.hasMore))
              ? 1
              : 0) +
          1,
      separatorBuilder: (_, __) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        if (index == 0) {
          return AddShoppingListDetailCard(
            title: selectedTab == 'Shopping list'
                ? 'Add New Shopping List'
                : 'Add New Pantry',
            isSelected: false,
            onTap: () {
              if (selectedTab == 'Shopping list') {
                ref.read(pageStateProvider.notifier).showShoppinDialog(context, ref);
              } else {
                ref.read(pageStateProvider.notifier).showPantryDialog(context, ref);
              }
            },
          );
        }

        final listIndex = index - 1;

        if (listIndex >= currentList.length) {
          // Show load more loader only when actually loading more (not during deletion)
          final isLoadingMore = selectedTab == 'Shopping list'
              ? shoppingState.status == AppStatus.loadingMore
              : pantryState.status == AppStatus.loadingMore;

          if (isLoadingMore) {
            return Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 16.h),
                child: LoadingAnimationWidget.fallingDot(
                  color: Colors.black54,
                  size: 50.0,
                ),
              ),
            );
          } else {
            // Return empty container if not loading more
            return const SizedBox.shrink();
          }
        }

        final listItem = currentList[listIndex];
        return GestureDetector(
          onTap: () {
            onIndexChanged(listIndex);
          },
          child: ShoppingListDetailCard(
            listItem: listItem,
            isSelected: selectedIndex == listIndex,
            isPantry: selectedTab == 'Pantry',
            onMenuItemSelected: (value) async {
             //

              if (value == 'Edit') {
                ref.read(shoppingNotifierProvider.notifier).resetToIdle();
                showUpdateShoppingDialog(
                  context,
                  ref,
                  shoppingId: selectedTab == 'Shopping list'
                      ? (listItem as ShoppingLists).id?.toString() ?? '0'
                      : (listItem as Pantries).id?.toString() ?? '0',
                  shoppingName: selectedTab == 'Shopping list'
                      ? (listItem as ShoppingLists).name?.toString() ?? ''
                      : (listItem as Pantries).name?.toString() ?? '',
                );
              } else if (value == 'Delete') {
                if (!isDeleting) {
                  final bool? confirmed = await Utils().showCommonConfirmDialog(
                    context: context,
                    title: selectedTab == 'Shopping list'
                        ? 'Delete Shopping List'
                        : 'Delete Pantry',
                    subtitle: 'Are you sure you want to delete "${selectedTab == 'Shopping list' ? (listItem as ShoppingLists).name?.toString() ?? '' : (listItem as Pantries).name?.toString() ?? ''}"?',
                    confirmText: 'Delete',
                    cancelText: 'Cancel',
                  );

                  if (confirmed != true || !context.mounted) {
                    debugPrint(
                        'Deletion cancelled for ${selectedTab == 'Shopping list' ? (listItem as ShoppingLists).name?.toString() ?? '' : (listItem as Pantries).name?.toString() ?? ''}');
                    return;
                  }

                  debugPrint("sdfgjsdfhg ${selectedIndex}   ${listIndex}");

                  final shoppingNotifier = ref.read(shoppingNotifierProvider.notifier);
                  final pantryNotifier = ref.read(pantryNotifierProvider.notifier);
                  var result = await (selectedTab == 'Shopping list'
                      ? shoppingNotifier.deleteShoppingList(
                      context, (listItem as ShoppingLists).id?.toString() ?? '0')
                      : pantryNotifier.deletePantryList(
                      context, (listItem as Pantries).id?.toString() ?? '0'));

                  if (!context.mounted) {
                    return;
                  }

                  if(selectedIndex==listIndex) {
                    onIndexChanged(listIndex);
                    await handleListDeletion(
                      context: context,
                      ref: ref,
                      selectedTab: selectedTab,
                      deletedIndex: listIndex,
                      onIndexChanged: onIndexChanged,
                    );

                  }
                  }
              }
            },
          ),
        );
      },
    ),
  );
}