// lib/screens/shopping_list_detail/widgets/footer.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/providers/shopping/shopping_item_notifier.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import '../../../app/imports/core_imports.dart';

Widget buildFooter({
  required BuildContext context,
  required WidgetRef ref,
  required String selectedTab,
  required VoidCallback onAddMoreRows,
}) {
  return Padding(
    padding: const EdgeInsets.all(8.0),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.lightestGreyColor, width: 1),
            borderRadius: BorderRadius.all(Radius.circular(10.sp)),
          ),
          child: CustomButton(
            width: DeviceUtils().isTabletOrIpad(context) ? 130 : 230.w,
            text: "Add more rows",
            fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
            color: Colors.white,
            textColor: AppColors.primaryLightTextColor,
            onPressed: onAddMoreRows,
          ),
        ),
        if (selectedTab == 'Shopping list')
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: AppColors.lightestGreyColor),
              color: Colors.white,
            ),
            child: Consumer(
              builder: (context, ref, child) {
                final shoppingItemState = ref.watch(shoppingItemNotifierProvider);
                final totalCost = shoppingItemState.data?.totalCost ?? 0.0;
                
                return Row(
                  children: [
                    Text(
                      '\$${totalCost.toStringAsFixed(2)} ',
                      style: context.theme.textTheme.displaySmall!.copyWith(
                        color: AppColors.primaryLightTextColor,
                        fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      '(Total bill)',
                      style: context.theme.textTheme.displaySmall!.copyWith(
                        color: AppColors.textGreyColor,
                        fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                );
              },
            ),
          )
      ],
    ),
  );
}