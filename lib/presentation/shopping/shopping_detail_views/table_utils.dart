// lib/screens/shopping_list_detail/widgets/table_utils.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
 import '../../../../app/imports/core_imports.dart';

Widget buildTableCell(Widget child) {
  return Container(
    padding: EdgeInsets.all(8),
    decoration: BoxDecoration(
      border: Border(right: BorderSide(color: Colors.grey.shade300)),
    ),
    child: child,
  );
}

Widget buildTitle(BuildContext context,String value) {
  return Padding(
    padding: EdgeInsets.only(left: 3.w, top: 10.h),
    child: CustomText(
     text:  value,
      size:  14,
      weight: FontWeight.w400,
      color: AppColors.primaryLightTextColor,
      align: TextAlign.center,
    ),
  );
}

Widget buildRowText(BuildContext context,String value) {
   return Padding(
    padding: EdgeInsets.only(left: 3.w, top: 10.h),
    child: CustomText(
      text:  value,
      size:  14,
      weight: FontWeight.w400,
      color: AppColors.primaryLightTextColor,
      align:Utils().isNumeric(value)? TextAlign.right:TextAlign.start,
    ),
  );
}