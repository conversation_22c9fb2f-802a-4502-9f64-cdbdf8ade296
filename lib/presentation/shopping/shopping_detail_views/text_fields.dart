// lib/screens/shopping_list_detail/widgets/text_fields.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/utils/screen_sizer.dart';
import 'package:intl/intl.dart';
import '../../../app/imports/core_imports.dart';

Widget buildEditableTextField(BuildContext context,TextEditingController controller, {String? placeholder}) {
  return Padding(
    padding: EdgeInsets.all(6.w),
    child: SizedBox(
      height: 48.h,
      child: TextField(
        controller: controller,
        style: context.theme.textTheme.labelSmall!.copyWith(
          fontSize:  DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.primaryGreyColor,
        ),
        decoration: InputDecoration(
          hintText: placeholder,
          hintStyle: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
            fontWeight: FontWeight.w400,
            color: Colors.grey.shade400,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
          isDense: true,
          filled: true,
          fillColor: Colors.white,
        ),
      ),
    ),
  );
}

Widget buildAmountTextField(BuildContext context,TextEditingController controller, {String? placeholder}) {
  return Padding(
    padding: EdgeInsets.all(6.w),
    child: SizedBox(
      height: 48.h,
      child: TextField(
        controller: controller,textAlign: TextAlign.left,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        maxLength: 14,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
          TextInputFormatter.withFunction((oldValue, newValue) {
            if (newValue.text.isEmpty) return newValue;

            // Allow intermediate values during typing (like "0", "0.", "0.0")
            if (newValue.text == '0' || newValue.text == '0.' || newValue.text.endsWith('.')) {
              return newValue;
            }

            final double? value = double.tryParse(newValue.text);
            if (value == null) return oldValue;

            // Only validate final values, allow intermediate typing
            if (value > 1000000) return oldValue;

            return newValue;
          }),
        ],
        style: context.theme.textTheme.labelSmall!.copyWith(
          fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.primaryGreyColor,
        ),
        decoration: InputDecoration(
          hintText: placeholder,
          hintStyle: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
            fontWeight: FontWeight.w400,
            color: Colors.grey.shade400,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
          isDense: true,
          filled: true,
          fillColor: Colors.white,
          counterText: "",
        ),
      ),
    ),
  );
}

Widget buildCostTextField(BuildContext context,TextEditingController controller, {String? placeholder}) {
  return Padding(
    padding: EdgeInsets.all(6.w),
    child: SizedBox(
      height: 48.h,
      child: TextField(
        controller: controller,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        maxLength: 14,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
        ],
        style: context.theme.textTheme.labelSmall!.copyWith(
          fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.primaryGreyColor,
        ),
        decoration: InputDecoration(
          hintText: placeholder,
          hintStyle: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
            fontWeight: FontWeight.w400,
            color: Colors.grey.shade400,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
          isDense: true,
          filled: true,
          fillColor: Colors.white,
          counterText: "",
        ),
      ),
    ),
  );
}

Widget buildRecipeTextField(BuildContext context,TextEditingController controller, {String? placeholder}) {
  return Padding(
    padding: EdgeInsets.all(6.w),
    child: SizedBox(
      height: 48.h,
      child: TextField(
        controller: controller,
        maxLength: 50,
        style: context.theme.textTheme.labelSmall!.copyWith(
          fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.primaryGreyColor,
        ),
        decoration: InputDecoration(
          hintText: placeholder,
          hintStyle: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
            fontWeight: FontWeight.w400,
            color: Colors.grey.shade400,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
          isDense: true,
          filled: true,
          fillColor: Colors.white,
          counterText: "",
        ),
      ),
    ),
  );
}

// Widget buildDatePickerField(BuildContext context, TextEditingController controller, {String? placeholder}) {
//   return Padding(
//     padding: EdgeInsets.all(6.w),
//     child: SizedBox(
//       height: 48.h,
//       child: TextField(
//         controller: controller,
//         readOnly: true,
//         onTap: () async {
//           final DateTime? pickedDate = await showDialog(
//             context: context,
//             builder: (BuildContext context) {
//               return Dialog(
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(12),
//                 ),
//                 child: Container(
//                   width: MediaQuery.of(context).size.width * 0.25, // 80% of screen width
//                   padding: EdgeInsets.all(16),
//                   child: Column(
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       Text(
//                         'Datepicker',
//                         style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
//                       ),
//                       SizedBox(height: 16),
//                       TextField(
//                         controller: TextEditingController(
//                           text: DateFormat('EEEE, d\'th\' MMMM').format(DateTime.now()),
//                         ),
//                         readOnly: true,
//                         decoration: InputDecoration(
//                           border: OutlineInputBorder(
//                             borderRadius: BorderRadius.circular(8),
//                             borderSide: BorderSide(color: Colors.grey),
//                           ),
//                           suffixIcon: Icon(Icons.arrow_drop_down),
//                         ),
//                       ),
//                       SizedBox(height: 16),
//                       Container(
//                         padding: EdgeInsets.all(8),
//                         decoration: BoxDecoration(
//                           border: Border.all(color: Colors.grey),
//                           borderRadius: BorderRadius.circular(8),
//                         ),
//                         child: Column(
//                           children: [
//                             Row(
//                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                               children: [
//                                 IconButton(
//                                   icon: Icon(Icons.chevron_left),
//                                   onPressed: () {
//                                     // Add logic to move to previous month
//                                   },
//                                 ),
//                                 Text(
//                                   '${DateFormat('MMMM yyyy').format(DateTime.now())}',
//                                   style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//                                 ),
//                                 IconButton(
//                                   icon: Icon(Icons.chevron_right),
//                                   onPressed: () {
//                                     // Add logic to move to next month
//                                   },
//                                 ),
//                               ],
//                             ),
//                             Row(
//                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                               children: ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su']
//                                   .map((day) => Padding(
//                                 padding: EdgeInsets.all(4),
//                                 child: Text(day, style: TextStyle(fontWeight: FontWeight.bold)),
//                               ))
//                                   .toList(),
//                             ),
//                             GridView.count(
//                               crossAxisCount: 7,
//                               shrinkWrap: true,
//                               physics: NeverScrollableScrollPhysics(),
//                               children: List.generate(31, (index) {
//                                 final day = index + 1;
//                                 bool isHighlighted = [10, 18].contains(day); // Highlight specific days
//                                 return GestureDetector(
//                                   onTap: () {
//                                     // Add logic to select date
//                                     Navigator.pop(context, DateTime(2023, 1, day));
//                                   },
//                                   child: Container(
//                                     margin: EdgeInsets.all(4),
//                                     decoration: BoxDecoration(
//                                       color: isHighlighted ? Colors.red : null,
//                                       border: isHighlighted ? Border.all(color: Colors.red) : null,
//                                       borderRadius: BorderRadius.circular(4),
//                                     ),
//                                     child: Center(
//                                       child: Text(
//                                         day.toString(),
//                                         style: TextStyle(
//                                           color: isHighlighted ? Colors.white : Colors.black,
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                 );
//                               }),
//                             ),
//                           ],
//                         ),
//                       ),
//                       SizedBox(height: 16),
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.end,
//                         children: [
//                           TextButton(
//                             onPressed: () => Navigator.pop(context),
//                             child: Text('Cancel', style: TextStyle(color: Colors.black)),
//                             style: TextButton.styleFrom(
//                               backgroundColor: Colors.grey[200],
//                               shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
//                             ),
//                           ),
//                           SizedBox(width: 8),
//                           ElevatedButton(
//                             onPressed: () => Navigator.pop(context),
//                             child: Text('Choose Date', style: TextStyle(color: Colors.white)),
//                             style: ElevatedButton.styleFrom(
//                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ),
//               );
//             },
//           );
//
//           if (pickedDate != null) {
//             final formattedDate =
//                 "${pickedDate.month.toString().padLeft(2, '0')}/${pickedDate.day.toString().padLeft(2, '0')}/${pickedDate.year}";
//             controller.text = formattedDate;
//           }
//         },
//         style: context.theme.textTheme.labelSmall!.copyWith(
//           fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
//           fontWeight: FontWeight.w400,
//           color: AppColors.primaryGreyColor,
//         ),
//         decoration: InputDecoration(
//           hintText: placeholder,
//           hintStyle: context.theme.textTheme.labelSmall!.copyWith(
//             fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
//             fontWeight: FontWeight.w400,
//             color: Colors.grey.shade400,
//           ),
//           border: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(6),
//             borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
//           ),
//           enabledBorder: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(6),
//             borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
//           ),
//           focusedBorder: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(6),
//             borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
//           ),
//           contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
//           isDense: true,
//           filled: true,
//           fillColor: Colors.white,
//           suffixIcon: Icon(
//             Icons.calendar_today,
//             color: AppColors.primaryColor,
//             size: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
//           ),
//         ),
//       ),
//     ),
//   );
// }

Widget buildDatePickerField(BuildContext context,TextEditingController controller, {String? placeholder}) {
  return Padding(
    padding: EdgeInsets.all(6.w),
    child: SizedBox(
      height: 48.h,
      child: TextField(
        controller: controller,
        readOnly: true,
        onTap: () async {
          final DateTime? pickedDate = await showDatePicker(
            context: context,
            initialDate: DateTime.now(),
            firstDate: DateTime(2000),
            lastDate: DateTime(2100),
            builder: (context, child) {
              return Theme(
                data: Theme.of(context).copyWith(
                  colorScheme: ColorScheme.light(
                    primary: AppColors.primaryColor,
                    onPrimary: Colors.white,
                    surface: Colors.white,
                    onSurface: Colors.black,
                  ),
                ),
                child: child!,
              );
            },
          );

          if (pickedDate != null) {
            final formattedDate =
                "${pickedDate.month.toString().padLeft(2, '0')}/${pickedDate.day.toString().padLeft(2, '0')}/${pickedDate.year}";
            controller.text = formattedDate;
          }
        },
        style: context.theme.textTheme.labelSmall!.copyWith(
          fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.primaryGreyColor,
        ),
        decoration: InputDecoration(
          hintText: placeholder,
          hintStyle: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
            fontWeight: FontWeight.w400,
            color: Colors.grey.shade400,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
          isDense: true,
          filled: true,
          fillColor: Colors.white,
          suffixIcon: Icon(
            Icons.calendar_today,
            color: AppColors.primaryColor,
            size: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
          ),
        ),
      ),
    ),
  );
}