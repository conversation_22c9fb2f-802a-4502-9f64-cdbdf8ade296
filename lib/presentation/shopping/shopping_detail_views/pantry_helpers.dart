// lib/screens/shopping_list_detail/helpers/pantry_helpers.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/core/data/models/pantry.dart';
import 'package:mastercookai/core/data/request_query/pantry_item_request.dart'
    as pantry_request;
import 'package:mastercookai/core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:intl/intl.dart';

import '../../../core/providers/shopping/pantry_item_notifier.dart';

/// Helper function to create empty pantry rows
List<Map<String, TextEditingController>> createEmptyPantryRows(int count) {
  List<Map<String, TextEditingController>> newRows = [];

  for (int i = 0; i < count; i++) {
    final controllers = {
      'item': TextEditingController(),
      'purchaseDate': TextEditingController(), // Empty - user will select date
      'useByDate': TextEditingController(), // Empty - user will select date
      'amount': TextEditingController(),
      'unit': TextEditingController(),
    };

    newRows.add(controllers);
  }

  return newRows;
}

/// Helper function to add listeners to pantry row controllers
void addPantryRowListeners(
  List<Map<String, TextEditingController>> rows,
  VoidCallback onTextChanged,
) {
  for (var row in rows) {
    for (var controller in row.values) {
      controller.addListener(onTextChanged);
    }
  }
}

Future<void> savePantryItems({
  required BuildContext context,
  required WidgetRef ref,
  required String selectedTab,
  required int selectedIndex,
  required List<Map<String, TextEditingController>> newPantryRows,
}) async {
  if (selectedTab != 'Pantry') {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please select a pantry to save items', isError: true);
    }
    return;
  }

  final pantryState = ref.read(pantryNotifierProvider);
  if (pantryState.data!.pantries!.isEmpty) {
    if (context.mounted) {
      Utils()
          .showFlushbar(context, message: 'No pantry available', isError: true);
    }
    return;
  }

  if (newPantryRows.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'No new items to save', isError: true);
    }
    return;
  }

  final selectedPantryListId = pantryState.data!.pantries![selectedIndex].id;
  List<pantry_request.PantryItem> pantryItems = [];

  for (var row in newPantryRows) {
    final item = row['item']?.text.trim() ?? '';
    final amountText = row['amount']?.text.trim() ?? '';
    final unit = row['unit']?.text.trim() ?? '';
    final purchasedDateText = row['purchaseDate']?.text.trim() ?? '';
    final useByDateText = row['useByDate']?.text.trim() ?? '';

    if (item.isEmpty) continue;

    if (purchasedDateText.isEmpty) {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Purchase date is required for item: $item',
            isError: true);
      }
      return;
    }

    if (useByDateText.isEmpty) {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Use by date is required for item: $item', isError: true);
      }
      return;
    }

    // // Parse amount - validate if user entered a value, default to 1.0 if empty
    // double amount;
    // if (amountText.isEmpty) {
    //   amount = 0.0; // Default value for empty fields
    // } else {
    //   amount = double.tryParse(amountText) ?? 0.0;
    //   if (!Utils().isValidAmount(amount)) {
    //     _showAmountValidationError(context, item);
    //     return;
    //   }
    // }

    DateTime? purchasedDate;
    DateTime? useByDate;

    try {
      final purchasedParts = purchasedDateText.split('/');
      if (purchasedParts.length == 3) {
        final month = int.parse(purchasedParts[0]);
        final day = int.parse(purchasedParts[1]);
        final year = int.parse(purchasedParts[2]);
        purchasedDate = DateTime(year, month, day);
      } else {
        if (context.mounted) {
          Utils().showFlushbar(context,
              message: 'Invalid purchase date format for item: $item',
              isError: true);
        }
        return;
      }
    } catch (e) {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Invalid purchase date for item: $item', isError: true);
      }
      return;
    }

    try {
      final useByParts = useByDateText.split('/');
      if (useByParts.length == 3) {
        final month = int.parse(useByParts[0]);
        final day = int.parse(useByParts[1]);
        final year = int.parse(useByParts[2]);
        useByDate = DateTime(year, month, day);
      } else {
        if (context.mounted) {
          Utils().showFlushbar(context,
              message: 'Invalid use by date format for item: $item',
              isError: true);
        }
        return;
      }
    } catch (e) {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Invalid use by date for item: $item', isError: true);
      }
      return;
    }

    pantryItems.add(pantry_request.PantryItem(
      item: item,
      amount: double.tryParse(amountText) ?? 0.0,
      unit: unit,
      purchasedDate: purchasedDate,
      useByDate: useByDate,
    ));
  }

  if (pantryItems.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please fill in at least one item', isError: true);
    }
    return;
  }

  final pantryItemRequest = pantry_request.PantryItemsRequest(
    pantryItems: pantryItems,
  );

  try {
    final (success, errorMessage) =
        await ref.read(pantryItemNotifierProvider.notifier).addPantryItems(
              pantryListId: selectedPantryListId ?? 0,
              request: pantryItemRequest,
            );

    if (success) {
      newPantryRows.clear();

      // Refresh pantry lists to update product counts
      ref
          .read(pantryNotifierProvider.notifier)
          .fetchPantryLists(context: context);

      // Refresh pantry items for the current list
      final pantryState = ref.read(pantryNotifierProvider);
      if (pantryState.data!.pantries!.isNotEmpty &&
          selectedIndex < pantryState.data!.pantries!.length) {
        final selectedPantryListId =
            pantryState.data!.pantries![selectedIndex].id;
        ref
            .read(pantryItemNotifierProvider.notifier)
            .fetchPantryItems(id: selectedPantryListId ?? 0);
      }

      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Pantry items saved successfully!', isError: false);
      }
    } else {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Failed to save items: ${errorMessage ?? 'Unknown error'}',
            isError: true);
      }
    }
  } catch (e) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Error saving items: $e', isError: true);
    }
  }
}

Future<(bool success, String? message)> deleteSelectedPantryItems({
  required BuildContext context,
  required WidgetRef ref,
  required String selectedTab,
  required int selectedIndex,
  required Set<int> selectedPantryItemIds,
  required bool isAllPantryItemsSelected,
}) async {
  if (selectedTab != 'Pantry') {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please select pantry tab to delete items', isError: true);
    }
    return (false, 'Wrong tab selected');
  }

  if (selectedPantryItemIds.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please select items to delete', isError: true);
    }
    return (false, 'No items selected');
  }

  final pantryItemState = ref.read(pantryNotifierProvider);
  final allPantryItems = pantryItemState.data!.pantries!;
  final isAllSelected = isAllPantryItemsSelected ||
      (allPantryItems.isNotEmpty &&
          selectedPantryItemIds.length == allPantryItems.length);

  String confirmationMessage;
  if (isAllSelected) {
    confirmationMessage = 'Are you sure you want to delete ALL pantry items?';
  } else if (selectedPantryItemIds.length == 1) {
    confirmationMessage = 'Are you sure you want to delete this pantry item?';
  } else {
    confirmationMessage =
        'Are you sure you want to delete ${selectedPantryItemIds.length} pantry items?';
  }

  final bool? confirmed = await Utils().showCommonConfirmDialog(
    context: context,
    title: 'Delete Pantry Items',
    subtitle: confirmationMessage,
    confirmText: 'Delete',
    cancelText: 'Cancel',
  );

  if (confirmed != true) {
    return (false, 'Deletion cancelled by user');
  }

  final pantryState = ref.read(pantryNotifierProvider);
  if (pantryState.data!.pantries!.isEmpty ||
      selectedIndex >= pantryState.data!.pantries!.length) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'No pantry list selected', isError: true);
    }
    return (false, 'Invalid pantry list index');
  }

  final selectedPantryListId = pantryState.data!.pantries![selectedIndex].id;
  final deleteType = isAllSelected ? "All" : "CUSTOM";

  final deleteRequest = pantry_request.DeletePantryItemsRequest(
    type: deleteType,
    pantryItemIds: deleteType == "All" ? null : selectedPantryItemIds.toList(),
  );

  try {
    final (success, errorMessage) =
        await ref.read(pantryItemNotifierProvider.notifier).deletePantryItems(
              pantryListId: selectedPantryListId ?? 0,
              request: deleteRequest,
            );

    if (success) {
      // Refresh pantry lists to update product counts
      if (context.mounted) {
        ref
            .read(pantryNotifierProvider.notifier)
            .fetchPantryLists(context: context);
      }

      // Refresh pantry items for the current list
      final pantryState = ref.read(pantryNotifierProvider);
      if (pantryState.data!.pantries!.isNotEmpty &&
          selectedIndex < pantryState.data!.pantries!.length) {
        final selectedPantryListId =
            pantryState.data!.pantries![selectedIndex].id;
        ref
            .read(pantryItemNotifierProvider.notifier)
            .fetchPantryItems(id: selectedPantryListId ?? 0);
      }

      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Items deleted successfully!', isError: false);
      }

      return (true, null);
    } else {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message:
                'Failed to delete items: ${errorMessage ?? 'Unknown error'}',
            isError: true);
      }

      return (false, errorMessage ?? 'Unknown error');
    }
  } catch (e) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Error deleting items: $e', isError: true);
    }

    return (false, 'Exception: $e');
  }
}

Future<void> updatePantryItems({
  required BuildContext context,
  required WidgetRef ref,
  required String selectedTab,
  required int selectedIndex,
  required Map<int, Map<String, TextEditingController>> editablePantryItems,
  required Set<int> modifiedPantryItemIds,
}) async {
  if (selectedTab != 'Pantry') {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please select a pantry to update items', isError: true);
    }
    return;
  }

  final pantryState = ref.read(pantryNotifierProvider);
  if (pantryState.data!.pantries!.isEmpty) {
    if (context.mounted) {
      Utils()
          .showFlushbar(context, message: 'No pantry available', isError: true);
    }
    return;
  }

  if (modifiedPantryItemIds.isEmpty) {
    if (context.mounted) {
      Utils()
          .showFlushbar(context, message: 'No items to update', isError: true);
    }
    return;
  }

  final selectedPantryListId = pantryState.data!.pantries![selectedIndex].id;
  List<pantry_request.PantryItem> pantryItems = [];

  final originalPantryItems =
      ref.read(pantryItemNotifierProvider).data!.pantryItems;

  // for (int itemId in modifiedPantryItemIds) {
  //   final controllers = editablePantryItems[itemId];
  //   if (controllers != null) {
  //     final itemName = controllers['item']?.text.trim();
  //     final unit = controllers['unit']?.text.trim();
  //     final purchasedDateText = controllers['purchaseDate']?.text.trim();
  //     final useByDateText = controllers['useByDate']?.text.trim();
  //
  //     final originalItem = originalPantryItems.firstWhere(
  //       (item) => item.id == itemId,
  //       orElse: () => throw Exception('Original item not found'),
  //     );
  //
  //     // Use original values as defaults, update with new values if provided
  //     String finalItemName =
  //         itemName?.isNotEmpty == true ? itemName! : originalItem.item;
  //
  //     double amount = originalItem.amount;
  //
  //     DateTime purchasedDate = originalItem.purchasedDate;
  //     if (purchasedDateText?.isNotEmpty == true) {
  //       try {
  //         final purchasedParts = purchasedDateText!.split('/');
  //         if (purchasedParts.length == 3) {
  //           final month = int.parse(purchasedParts[0]);
  //           final day = int.parse(purchasedParts[1]);
  //           final year = int.parse(purchasedParts[2]);
  //           purchasedDate = DateTime(year, month, day);
  //         } else {
  //           if (context.mounted) {
  //             Utils().showFlushbar(context,
  //                 message:
  //                     'Invalid purchase date format for item: $finalItemName',
  //                 isError: true);
  //           }
  //           return;
  //         }
  //       } catch (e) {
  //         if (context.mounted) {
  //           Utils().showFlushbar(context,
  //               message: 'Invalid purchase date for item: $finalItemName',
  //               isError: true);
  //         }
  //         return;
  //       }
  //     }
  //
  //     DateTime useByDate = originalItem.useByDate;
  //     if (useByDateText?.isNotEmpty == true) {
  //       try {
  //         final useByParts = useByDateText!.split('/');
  //         if (useByParts.length == 3) {
  //           final month = int.parse(useByParts[0]);
  //           final day = int.parse(useByParts[1]);
  //           final year = int.parse(useByParts[2]);
  //           useByDate = DateTime(year, month, day);
  //         } else {
  //           if (context.mounted) {
  //             Utils().showFlushbar(context,
  //                 message:
  //                     'Invalid use by date format for item: $finalItemName',
  //                 isError: true);
  //           }
  //           return;
  //         }
  //       } catch (e) {
  //         if (context.mounted) {
  //           Utils().showFlushbar(context,
  //               message: 'Invalid use by date for item: $finalItemName',
  //               isError: true);
  //         }
  //         return;
  //       }
  //     }
  //     pantryItems.add(pantry_request.PantryItem(
  //       id: itemId,
  //       item: finalItemName,
  //       amount: amount,
  //       unit: unit?.isNotEmpty == true ? unit! : originalItem.unit,
  //       purchasedDate: purchasedDate,
  //       useByDate: useByDate,
  //     ));
  //   }
  // }
  for (int itemId in modifiedPantryItemIds) {
    final controllers = editablePantryItems[itemId];
    if (controllers != null) {
      final itemName = controllers['item']?.text.trim();
      final unit = controllers['unit']?.text.trim();
      final purchasedDateText = controllers['purchaseDate']?.text.trim();
      final useByDateText = controllers['useByDate']?.text.trim();
      final amountText = controllers['amount']?.text.trim(); // Add this to get amount from controller

      final originalItem = originalPantryItems.firstWhere(
            (item) => item.id == itemId,
        orElse: () => throw Exception('Original item not found'),
      );

      // Use original values as defaults, update with new values if provided
      String finalItemName =
      itemName?.isNotEmpty == true ? itemName! : originalItem.item;

      // Parse amount - validate if user entered a value, default to original if empty
      double amount = originalItem.amount;
      if (amountText?.isNotEmpty == true) {
        try {
          amount = double.tryParse(amountText!) ?? originalItem.amount;
          if (!Utils().isValidAmount(amount)) {
            if (context.mounted) {
              Utils().showFlushbar(context,
                  message: 'Invalid amount for item: $finalItemName',
                  isError: true);
            }
            return;
          }
        } catch (e) {
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: 'Invalid amount format for item: $finalItemName',
                isError: true);
          }
          return;
        }
      }

      DateTime purchasedDate = originalItem.purchasedDate;
      if (purchasedDateText?.isNotEmpty == true) {
        try {
          final purchasedParts = purchasedDateText!.split('/');
          if (purchasedParts.length == 3) {
            final month = int.parse(purchasedParts[0]);
            final day = int.parse(purchasedParts[1]);
            final year = int.parse(purchasedParts[2]);
            purchasedDate = DateTime(year, month, day);
          } else {
            if (context.mounted) {
              Utils().showFlushbar(context,
                  message: 'Invalid purchase date format for item: $finalItemName',
                  isError: true);
            }
            return;
          }
        } catch (e) {
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: 'Invalid purchase date for item: $finalItemName',
                isError: true);
          }
          return;
        }
      }

      DateTime useByDate = originalItem.useByDate;
      if (useByDateText?.isNotEmpty == true) {
        try {
          final useByParts = useByDateText!.split('/');
          if (useByParts.length == 3) {
            final month = int.parse(useByParts[0]);
            final day = int.parse(useByParts[1]);
            final year = int.parse(useByParts[2]);
            useByDate = DateTime(year, month, day);
          } else {
            if (context.mounted) {
              Utils().showFlushbar(context,
                  message: 'Invalid use by date format for item: $finalItemName',
                  isError: true);
            }
            return;
          }
        } catch (e) {
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: 'Invalid use by date for item: $finalItemName',
                isError: true);
          }
          return;
        }
      }

      pantryItems.add(pantry_request.PantryItem(
        id: itemId,
        item: finalItemName,
        amount: amount, // Use the parsed amount
        unit: unit?.isNotEmpty == true ? unit! : originalItem.unit,
        purchasedDate: purchasedDate,
        useByDate: useByDate,
      ));
    }
  }


  if (pantryItems.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please fill in at least one item', isError: true);
    }
    return;
  }

  final pantryItemRequest = pantry_request.PantryItemsRequest(
    pantryItems: pantryItems,
  );

  try {
    final (success, errorMessage) =
        await ref.read(pantryItemNotifierProvider.notifier).addPantryItems(
              pantryListId: selectedPantryListId ?? 0,
              request: pantryItemRequest,
            );

    if (success) {
      for (int itemId in modifiedPantryItemIds) {
        editablePantryItems[itemId]?.forEach((key, controller) {
          controller.dispose();
        });
      }
      editablePantryItems
          .removeWhere((key, value) => modifiedPantryItemIds.contains(key));
      modifiedPantryItemIds.clear();

      // Refresh pantry lists to update product counts
      ref
          .read(pantryNotifierProvider.notifier)
          .fetchPantryLists(context: context);

      final pantryState = ref.read(pantryNotifierProvider);
      if (pantryState.data!.pantries!.isNotEmpty &&
          selectedIndex < pantryState.data!.pantries!.length) {
        final selectedPantryListId =
            pantryState.data!.pantries![selectedIndex].id;
        ref
            .read(pantryItemNotifierProvider.notifier)
            .fetchPantryItems(id: selectedPantryListId!.toInt());
      }

      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Pantry items updated successfully!', isError: false);
      }
    } else {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message:
                'Failed to update items: ${errorMessage ?? 'Unknown error'}',
            isError: true);
      }
    }
  } catch (e) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Error updating items: $e', isError: true);
    }
  }
}
