// lib/screens/shopping_list_detail/widgets/autocomplete_fields.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/utils/screen_sizer.dart';
 import 'dart:convert';
import '../../../../app/imports/core_imports.dart';
import '../../../../core/helpers/customAutocomplete.dart';

Future<List<String>> searchMasterIngredients(BuildContext context,String query) async {
  if (query.isEmpty) return [];

  try {
    List<dynamic>? _masterIngredientsCache;
    if (_masterIngredientsCache == null) {
      final String jsonString = await DefaultAssetBundle.of(context).loadString('assets/MasterIngredients.json');
      _masterIngredientsCache = json.decode(jsonString);
      print('MasterIngredients.json loaded and cached. Total ingredients: ${_masterIngredientsCache!.length}');
    }

    final List<dynamic> ingredients = _masterIngredientsCache!;
    final List<String> suggestions = [];
    final String lowerQuery = query.toLowerCase();
    int matchCount = 0;
    final Set<String> uniqueSuggestions = {};

    for (final ingredient in ingredients) {
      final String ingredientName = ingredient['IngredientName']?.toString() ?? '';
      final String purchaseAs = ingredient['PurchaseAs']?.toString() ?? '';

      if (ingredientName.toLowerCase().contains(lowerQuery) || purchaseAs.toLowerCase().contains(lowerQuery)) {
        final String suggestion = purchaseAs.isNotEmpty ? purchaseAs : ingredientName;
        if (suggestion.isNotEmpty && uniqueSuggestions.add(suggestion)) {
          suggestions.add(suggestion);
          matchCount++;
          if (matchCount >= 15) break;
        }
      }
    }

    print('Search for "$query" returned ${suggestions.length} suggestions');
    return suggestions;
  } catch (e) {
    print('Error loading MasterIngredients.json: $e');
    return [];
  }
}

Widget buildItemAutocompleteField(BuildContext context,TextEditingController controller, {String? placeholder}) {
  return Padding(
    padding: EdgeInsets.all(6.w),
    child: SizedBox(
      height: 48.h,
      child: CustomAutocomplete<String>(
        controller: controller,
        suggestionsHeight: 350.0,
        dropdownWidthMultiplier: 2,
        fetchSuggestions: (query) => searchMasterIngredients(context,query),
        itemBuilder: (context, item) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 3.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.08),
                  spreadRadius: 0.5,
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
              border: Border.all(color: Colors.grey.shade100, width: 0.5),
            ),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 10.h),
              child: Row(
                children: [
                  Container(
                    width: 6.w,
                    height: 6.h,
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Text(
                      item,
                      style: context.theme.textTheme.labelSmall!.copyWith(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.primaryGreyColor,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        itemToString: (item) => item,
        onSelected: (item) {
          debugPrint('Item autocomplete: onSelected called with "$item"');
          controller.text = item;
          debugPrint('Item autocomplete: controller.text set to "${controller.text}"');
        },
        decoration: InputDecoration(
          hintText: placeholder,
          hintStyle: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.w400,
            color: Colors.grey.shade400,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
          isDense: true,
          filled: true,
          fillColor: Colors.white,
        ),
        style: context.theme.textTheme.labelSmall!.copyWith(
          fontSize: 20.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.primaryGreyColor,
        ),
      ),
    ),
  );
}

Widget buildUnitAutocompleteField(BuildContext context,TextEditingController controller, {String? placeholder, required WidgetRef ref,}) {
  return Padding(
    padding: EdgeInsets.all(6.w),
    child: SizedBox(
      height: 48.h,
      child: CustomAutocomplete<String>(
        controller: controller,
        suggestionsHeight: 350.0,
        dropdownWidthMultiplier: 1.0,
        fetchSuggestions: (query) async {
          final userProfileNotifier = ref.read(userProfileNotifierProvider.notifier);
          List<String> instructionWords = userProfileNotifier.instructionWords;

          if (instructionWords.isEmpty) {
            instructionWords = [
              'kg', 'g', 'lb', 'oz', 'cup', 'cups', 'tbsp', 'tsp', 'ml', 'l', 'liter', 'liters',
              'piece', 'pieces', 'slice', 'slices', 'can', 'cans', 'bottle', 'bottles',
              'pack', 'packs', 'box', 'boxes', 'bag', 'bags', 'jar', 'jars'
            ];
          }

          if (query.trim().isEmpty) {
            return instructionWords;
          } else {
            return instructionWords
                .where((word) => word.toLowerCase().contains(query.toLowerCase()))
                .toList();
          }
        },
        itemBuilder: (context, item) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 3.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.08),
                  spreadRadius: 0.5,
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
              border: Border.all(color: Colors.grey.shade100, width: 0.5),
            ),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 10.h),
              child: Row(
                children: [
                  Container(
                    width: 6.w,
                    height: 6.h,
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Text(
                      item,
                      style: context.theme.textTheme.labelSmall!.copyWith(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.primaryGreyColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        itemToString: (item) => item,
        onSelected: (item) {
          debugPrint('Unit autocomplete: onSelected called with "$item"');
          controller.text = item;
          debugPrint('Unit autocomplete: controller.text set to "${controller.text}"');
        },
        decoration: InputDecoration(
          hintText: placeholder,
          counterText: '',
          hintStyle: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.w400,
            color: Colors.grey.shade400,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
          isDense: true,
          filled: true,
          fillColor: Colors.white,
        ),
        style: context.theme.textTheme.labelSmall!.copyWith(
          fontSize: 20.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.primaryGreyColor,
        ),
      ),
    ),
  );
}