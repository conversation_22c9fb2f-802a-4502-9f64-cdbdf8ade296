import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import '../../../app/imports/core_imports.dart';

Widget buildActionButtonsRow({
  required BuildContext context,
  required String selectedTab,
  required bool hasSelectedItems,
  required VoidCallback onMarkPurchased,
  required VoidCallback onDelete,
  required VoidCallback onSave,
  required int newShoppingRows,
  required int newPantryRows,
  required int modifiedShoppingItemCount,
  required int modifiedPantryItemCount,
  bool isSavingShoppingItems = false,
  bool isUpdatingShoppingItems = false,
  bool isSavingPantryItems = false,
  bool isUpdatingPantryItems = false,
  required bool showSaveButton,
}) {
  final screenSize = MediaQuery.of(context).size;

  return Padding(
    padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.0),
    child: Row(
      children: [
        if (selectedTab == 'Shopping list' && hasSelectedItems) ...[
          GestureDetector(
            onTap: onMarkPurchased,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.local_offer, color: Colors.white, size: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp),
                  SizedBox(width: 6.w),
                  Text(
                    'Mark as Purchased',
                    style: context.theme.textTheme.labelSmall!.copyWith(
                      fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(width: 12.w),
        ],
        if (hasSelectedItems) ...[
          GestureDetector(
            onTap: onDelete,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.delete_outline, color: Colors.white, size: DeviceUtils().isTabletOrIpad(context) ? 12 : 24.sp),
                  SizedBox(width: 6.w),
                  Text(
                    'Delete',
                    style: context.theme.textTheme.labelSmall!.copyWith(
                      fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(width: 12.w),
        ],
        Spacer(),
        if (showSaveButton)
          GestureDetector(
            onTap: (isSavingShoppingItems ||
                    isUpdatingShoppingItems ||
                    isSavingPantryItems ||
                    isUpdatingPantryItems)
                ? null
                : onSave,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: (isSavingShoppingItems ||
                        isUpdatingShoppingItems ||
                        isSavingPantryItems ||
                        isUpdatingPantryItems)
                    ? Colors.red.withOpacity(0.6)
                    : Colors.red,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isSavingShoppingItems ||
                      isUpdatingShoppingItems ||
                      isSavingPantryItems ||
                      isUpdatingPantryItems) ...[
                    SizedBox(width: 8.w),
                  ],
                  Text(
                    _getButtonText(
                      selectedTab: selectedTab,
                      newShoppingRows: newShoppingRows,
                      newPantryRows: newPantryRows,
                      modifiedShoppingItemCount: modifiedShoppingItemCount,
                      modifiedPantryItemCount: modifiedPantryItemCount,
                      isSavingShoppingItems: isSavingShoppingItems,
                      isUpdatingShoppingItems: isUpdatingShoppingItems,
                      isSavingPantryItems: isSavingPantryItems,
                      isUpdatingPantryItems: isUpdatingPantryItems,
                    ),
                    style: context.theme.textTheme.labelSmall!.copyWith(
                      fontSize:
                          DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    ),
  );
}

String _getButtonText({
  required String selectedTab,
  required int newShoppingRows,
  required int newPantryRows,
  required int modifiedShoppingItemCount,
  required int modifiedPantryItemCount,
  required bool isSavingShoppingItems,
  required bool isUpdatingShoppingItems,
  required bool isSavingPantryItems,
  required bool isUpdatingPantryItems,
}) {
  if (selectedTab == 'Shopping list') {
    if (isSavingShoppingItems) return 'Saving...';
    if (isUpdatingShoppingItems) return 'Updating...';
    if (newShoppingRows > 0) {
      return 'Save (${newShoppingRows + modifiedShoppingItemCount})';
    }
    if (modifiedShoppingItemCount > 0) {
      return 'Update ($modifiedShoppingItemCount)';
    }
  } else {
    if (isSavingPantryItems) return 'Saving...';
    if (isUpdatingPantryItems) return 'Updating...';
    if (newPantryRows > 0) {
      return 'Save (${newPantryRows + modifiedPantryItemCount})';
    }
    if (modifiedPantryItemCount > 0) {
      return 'Update ($modifiedPantryItemCount)';
    }
  }
  return 'Save';
}
