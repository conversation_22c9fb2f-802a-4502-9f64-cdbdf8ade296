// lib/screens/shopping_list_detail/widgets/shopping_table.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/core/data/models/shopping.dart';
import 'package:mastercookai/core/providers/shopping/shopping_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/table_utils.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/text_fields.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../core/network/app_status.dart';
 import '../../../core/utils/screen_sizer.dart';
import '../../../core/widgets/no_data_widget.dart';
import 'autocomplete_fields.dart';
import 'footer.dart';

Widget buildShoppingTable({
  required BuildContext context,
  required WidgetRef ref,
  required int selectedIndex,
  required List<Map<String, TextEditingController>> newShoppingRows,
  required Map<int, Map<String, TextEditingController>> editableShoppingItems,
  required Set<int> selectedShoppingItemIds,
  required bool isAllShoppingItemsSelected,
  required Function(int, bool?) onCheckboxChanged,
  required Function(ShoppingItem) onMakeEditable,
  required VoidCallback? onAddEmptyRow,
  String? currentSearchQuery,
  required String selectedTab,
}) {
  return _ShoppingTableWidget(
    context: context,
    ref: ref,
    selectedIndex: selectedIndex,
    newShoppingRows: newShoppingRows,
    editableShoppingItems: editableShoppingItems,
    selectedShoppingItemIds: selectedShoppingItemIds,
    isAllShoppingItemsSelected: isAllShoppingItemsSelected,
    onCheckboxChanged: onCheckboxChanged,
    onMakeEditable: onMakeEditable,
    onAddEmptyRow: onAddEmptyRow,
    currentSearchQuery: currentSearchQuery,
    selectedTab: selectedTab,
  );
}

class _ShoppingTableWidget extends StatefulWidget {
  final BuildContext context;
  final WidgetRef ref;
  final int selectedIndex;
  final List<Map<String, TextEditingController>> newShoppingRows;
  final Map<int, Map<String, TextEditingController>> editableShoppingItems;
  final Set<int> selectedShoppingItemIds;
  final bool isAllShoppingItemsSelected;
  final Function(int, bool?) onCheckboxChanged;
  final Function(ShoppingItem) onMakeEditable;
  final VoidCallback? onAddEmptyRow;
  final String? currentSearchQuery;
  final String selectedTab;

  const _ShoppingTableWidget({
    required this.context,
    required this.ref,
    required this.selectedIndex,
    required this.newShoppingRows,
    required this.editableShoppingItems,
    required this.selectedShoppingItemIds,
    required this.isAllShoppingItemsSelected,
    required this.onCheckboxChanged,
    required this.onMakeEditable,
    required this.onAddEmptyRow,
    required this.currentSearchQuery,
    required this.selectedTab,
  });

  @override
  State<_ShoppingTableWidget> createState() => _ShoppingTableWidgetState();
}

class _ShoppingTableWidgetState extends State<_ShoppingTableWidget> {
  late ScrollController scrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    scrollController.addListener(_loadMoreItems);
  }

  @override
  void dispose() {
    scrollController.removeListener(_loadMoreItems);
    scrollController.dispose();
    super.dispose();
  }

  void _loadMoreItems() {
    if (_isLoadingMore) return;

    final shoppingState = widget.ref.read(shoppingNotifierProvider);
    final shoppingItemState = widget.ref.read(shoppingItemNotifierProvider);

    final selectedShoppingListId =
        shoppingState.data!.shoppingLists!.isNotEmpty &&
                widget.selectedIndex < shoppingState.data!.shoppingLists!.length
            ? shoppingState.data!.shoppingLists![widget.selectedIndex].id!.toInt()
            : null;

    if (selectedShoppingListId == null) return;

    // Check if we're near the bottom of the scroll view
    if (scrollController.position.pixels >= scrollController.position.maxScrollExtent - 200 &&
        shoppingItemState.status != AppStatus.loading &&
        shoppingItemState.status != AppStatus.loadingMore) {
      final notifier = widget.ref.read(shoppingItemNotifierProvider.notifier);
      if (notifier.canFetchMore) {
        setState(() {
          _isLoadingMore = true;
        });

        notifier.fetchShoppingListsItems(
          id: selectedShoppingListId,
          search: widget.currentSearchQuery,
          loadMore: true, // This is the key fix - pass loadMore: true
        ).then((_) {
          if (mounted) {
            setState(() {
              _isLoadingMore = false;
            });
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final shoppingState = widget.ref.watch(shoppingNotifierProvider);
    final shoppingItemState = widget.ref.watch(shoppingItemNotifierProvider);

    // Reset local loading state if the notifier state is not loading more
    if (_isLoadingMore && shoppingItemState.status != AppStatus.loadingMore) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
        }
      });
    }

    final selectedShoppingListId =
        shoppingState.data!.shoppingLists!.isNotEmpty &&
                widget.selectedIndex < shoppingState.data!.shoppingLists!.length
            ? shoppingState.data!.shoppingLists![widget.selectedIndex].id!.toInt()
            : null;

    final items = selectedShoppingListId != null
        ? (shoppingItemState.data?.shoppingItems ?? [])
        : [];

    final isSearching =
        widget.currentSearchQuery != null && widget.currentSearchQuery!.trim().isNotEmpty;
    final hasNoResults = items.isEmpty && widget.newShoppingRows.isEmpty;

    if (isSearching && hasNoResults) {
      return const NoDataWidget(
        title: "Nothing Here Yet",
        subtitle: "No items found. Try searching again or add something new.",
        width: 250,
        height: 250,
      );
    }

    if (!isSearching &&
        hasNoResults &&
        shoppingState.data!.shoppingLists!.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onAddEmptyRow?.call();
      });
    }

    return SizedBox(
      height: ScreenSizer().getVisibleScreenHeight(context) - 150,
      child: Column(
        children: [
          Table(
            columnWidths: const {
              0: FlexColumnWidth(0.9),
              1: FixedColumnWidth(50),
              2: FlexColumnWidth(3.2),
              3: FlexColumnWidth(1.5),
              4: FlexColumnWidth(2),
              5: FlexColumnWidth(2.5),
              6: FlexColumnWidth(2.5),
              7: FlexColumnWidth(1.5),
            },
            border: TableBorder.symmetric(
              inside: BorderSide(color: Colors.grey.shade300),
            ),
            children: [
              buildShoppingHeaderRow(
                context,
                isAllSelected: widget.isAllShoppingItemsSelected,
                onSelectAllChanged: (value) => widget.onCheckboxChanged(-1, value),
              ),
            ],
          ),
          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              padding: EdgeInsets.only(bottom: 30.h),
              child: Column(
                children: [
                  Table(
                    columnWidths: const {
                      0: FlexColumnWidth(0.9),
                      1: FixedColumnWidth(50),
                      2: FlexColumnWidth(3.2),
                      3: FlexColumnWidth(1.5),
                      4: FlexColumnWidth(2),
                      5: FlexColumnWidth(2.5),
                      6: FlexColumnWidth(2.5),
                      7: FlexColumnWidth(1.5),
                    },
                    border: TableBorder.symmetric(
                      inside: BorderSide(color: Colors.grey.shade300),
                    ),
                    children: [
                      ...List.generate(items.length, (index) {
                        final item = items[index];
                        return buildShoppingDataRow(
                          count: index + 1,
                          item: item,
                          isEditable: widget.editableShoppingItems.containsKey(item.id),
                          isSelected: widget.selectedShoppingItemIds.contains(item.id),
                          onCheckboxChanged: (value) =>
                              widget.onCheckboxChanged(item.id, value),
                          onTap: () => widget.onMakeEditable(item),
                          controllers: widget.editableShoppingItems[item.id],
                          context: context,
                          ref: widget.ref,
                        );
                      }),
                      ...List.generate(widget.newShoppingRows.length, (index) {
                        return buildEditableShoppingRow(
                          count: items.length + index + 1,
                          controllers: widget.newShoppingRows[index],
                          context: context,
                          ref: widget.ref,
                        );
                      }),
                    ],
                  ),
                ],
              ),
            ),
          ),
        if (shoppingItemState.status == AppStatus.loadingMore)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Center(
              child: LoadingAnimationWidget.fallingDot(
                color: Colors.black54,
                size: 50.0,
              ),
            ),
          ),
          buildFooter(
            context: context,
            ref: widget.ref,
            selectedTab: widget.selectedTab,
            onAddMoreRows: () {
              widget.onAddEmptyRow?.call();
            },
          ),
        ],
      ),
    );
  }
}

TableRow buildShoppingHeaderRow(
  BuildContext context, {
  required bool isAllSelected,
  required Function(bool?) onSelectAllChanged,
}) {
  return TableRow(
    decoration: BoxDecoration(
      color: Colors.grey.shade200,
      borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8), topRight: Radius.circular(8)),
    ),
    children: [
      buildTableCell(buildTitle(context, "#")),
      buildTableCell(
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () => onSelectAllChanged(!isAllSelected),
            behavior: HitTestBehavior.opaque,
            child: Checkbox(
              value: isAllSelected,
              onChanged: onSelectAllChanged,
              activeColor: Colors.red,
              side: const BorderSide(color: Colors.red, width: 1),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
              checkColor: Colors.white,
            ),
          ),
        ),
      ),
      buildTableCell(buildTitle(context, "Item")),
      buildTableCell(buildTitle(context, "Amount")),
      buildTableCell(buildTitle(context, "Unit")),
      buildTableCell(buildTitle(context, "Store location")),
      buildTableCell(buildTitle(context, "Recipe")),
      buildTableCell(buildTitle(context, "Cost")),
    ],
  );
}

TableRow buildShoppingDataRow({
  required int count,
  required ShoppingItem item,
  required bool isEditable,
  required bool isSelected,
  required Function(bool?) onCheckboxChanged,
  required VoidCallback onTap,
  required Map<String, TextEditingController>? controllers,
  required BuildContext context,
  required WidgetRef ref,
}) {
  return TableRow(
    decoration: BoxDecoration(
      border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      color: isEditable ? Colors.blue.shade50 : null,
    ),
    children: [
      buildTableCell(Center(child: buildRowText(context, "$count"))),
      buildTableCell(
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () => onCheckboxChanged(!isSelected),
            behavior: HitTestBehavior.opaque,
            child: Checkbox(
              value: isSelected,
              onChanged: onCheckboxChanged,
              activeColor: Colors.red,
              side: const BorderSide(color: Colors.red, width: 1),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
              checkColor: Colors.white,
            ),
          ),
        ),
      ),
      buildTableCell(
        isEditable
            ? buildItemAutocompleteField(context, controllers!['item']!,
                placeholder: "Item name")
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.item),
                ),
              ),
      ),
      buildTableCell(
        isEditable
            ? buildAmountTextField(context, controllers!['amount']!,
                placeholder: "Amount")
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.amount?.toString() ?? "0"),
                ),
              ),
      ),
      buildTableCell(
        isEditable
            ? buildUnitAutocompleteField(context, controllers!['unit']!,
                placeholder: "Unit", ref: ref)
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.unit),
                ),
              ),
      ),
      buildTableCell(
        isEditable
            ? buildEditableTextField(context, controllers!['storeLocation']!,
                placeholder: "Store location")
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.storeLocation),
                ),
              ),
      ),
      buildTableCell(
        isEditable
            ? buildRecipeTextField(context, controllers!['recipe']!,
                placeholder: "Recipe")
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.recipe),
                ),
              ),
      ),
      buildTableCell(
        isEditable
            ? buildCostTextField(context, controllers!['cost']!,
                placeholder: "Cost")
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.cost.toString()),
                ),
              ),
      ),
    ],
  );
}

TableRow buildEditableShoppingRow({
  required int count,
  required Map<String, TextEditingController> controllers,
  required BuildContext context,
  required WidgetRef ref,
}) {
  return TableRow(
    decoration: BoxDecoration(
      border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
    ),
    children: [
      buildTableCell(Center(child: buildRowText(context, "$count"))),
      buildTableCell(
        MouseRegion(
          cursor: SystemMouseCursors.basic,
          child: Checkbox(
            value: false,
            onChanged: null,
            activeColor: Colors.red,
            side: const BorderSide(color: Colors.red, width: 1),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
            checkColor: Colors.white,
          ),
        ),
      ),
      buildTableCell(buildItemAutocompleteField(context, controllers['item']!,
          placeholder: "Item name")),
      buildTableCell(buildAmountTextField(context, controllers['amount']!,
          placeholder: "Amount")),
      buildTableCell(buildUnitAutocompleteField(context, controllers['unit']!,
          placeholder: "Unit", ref: ref)),
      buildTableCell(buildEditableTextField(
          context, controllers['storeLocation']!,
          placeholder: "Store location")),
      buildTableCell(buildRecipeTextField(context, controllers['recipe']!,
          placeholder: "Recipe")),
      buildTableCell(buildCostTextField(context, controllers['cost']!,
          placeholder: "Cost")),
    ],
  );
}
