
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import '../../../../app/imports/core_imports.dart';

class AddShoppingListDetailCard extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const AddShoppingListDetailCard({super.key, required this.title , this.isSelected = false,  required this.onTap});

  @override
  Widget build(BuildContext context) {
    return  GestureDetector(
      onTap: onTap,
      child: Card(
        color: context.theme.cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
          side: isSelected
              ? BorderSide(color:AppColors.selectionColor, width: 3)
              : BorderSide(color: AppColors.greyBorderColor, width: 1),
        ),
        child: Padding(
          padding: EdgeInsets.only(top: 20.sp , bottom: 20.sp, left: 20.sp ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              // Image Section
              ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Image.asset(
                   title == 'Add New Shopping List'
                    ? AssetsManager.addShopping
                    : AssetsManager.addPantry,
                  width: DeviceUtils().isTabletOrIpad(context) ? 50 : 100.w,
                  height: DeviceUtils().isTabletOrIpad(context) ? 50 : 100.h,
                  fit: BoxFit.cover,
                ),
              ),
              SizedBox(width: 15.w),
              Text(
                title,
                style: context.theme.textTheme.displaySmall!.copyWith(
                  color: AppColors.primaryGreyColor,
                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 25.sp,
                  fontWeight: FontWeight.w400,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }


}