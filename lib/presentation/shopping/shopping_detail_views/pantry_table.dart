import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/core/data/models/pantry.dart';
import 'package:mastercookai/core/providers/shopping/pantry_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/core/utils/screen_sizer.dart';
import 'package:intl/intl.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../core/network/app_status.dart';
import '../../../core/widgets/no_data_widget.dart';
import 'autocomplete_fields.dart';
import 'footer.dart';
import 'text_fields.dart';
import 'table_utils.dart';

Widget buildPantryTable({
  required BuildContext context,
  required WidgetRef ref,
  required int selectedIndex,
  required List<Map<String, TextEditingController>> newPantryRows,
  required Map<int, Map<String, TextEditingController>> editablePantryItems,
  required Set<int> selectedPantryItemIds,
  required bool isAllPantryItemsSelected,
  required Function(int, bool?) onCheckboxChanged,
  required Function(PantryItem) onMakeEditable,
  required VoidCallback? onAddEmptyRow,
  String? currentSearchQuery,
  required String selectedTab,
}) {
  return _PantryTableWidget(
    context: context,
    ref: ref,
    selectedIndex: selectedIndex,
    newPantryRows: newPantryRows,
    editablePantryItems: editablePantryItems,
    selectedPantryItemIds: selectedPantryItemIds,
    isAllPantryItemsSelected: isAllPantryItemsSelected,
    onCheckboxChanged: onCheckboxChanged,
    onMakeEditable: onMakeEditable,
    onAddEmptyRow: onAddEmptyRow,
    currentSearchQuery: currentSearchQuery,
    selectedTab: selectedTab,
  );
}

class _PantryTableWidget extends StatefulWidget {
  final BuildContext context;
  final WidgetRef ref;
  final int selectedIndex;
  final List<Map<String, TextEditingController>> newPantryRows;
  final Map<int, Map<String, TextEditingController>> editablePantryItems;
  final Set<int> selectedPantryItemIds;
  final bool isAllPantryItemsSelected;
  final Function(int, bool?) onCheckboxChanged;
  final Function(PantryItem) onMakeEditable;
  final VoidCallback? onAddEmptyRow;
  final String? currentSearchQuery;
  final String selectedTab;

  const _PantryTableWidget({
    required this.context,
    required this.ref,
    required this.selectedIndex,
    required this.newPantryRows,
    required this.editablePantryItems,
    required this.selectedPantryItemIds,
    required this.isAllPantryItemsSelected,
    required this.onCheckboxChanged,
    required this.onMakeEditable,
    required this.onAddEmptyRow,
    required this.currentSearchQuery,
    required this.selectedTab,
  });

  @override
  State<_PantryTableWidget> createState() => _PantryTableWidgetState();
}

class _PantryTableWidgetState extends State<_PantryTableWidget> {
  late ScrollController scrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    scrollController.addListener(_loadMoreItems);
  }

  @override
  void dispose() {
    scrollController.removeListener(_loadMoreItems);
    scrollController.dispose();
    super.dispose();
  }

  void _loadMoreItems() {
    if (_isLoadingMore) return;

    final pantryState = widget.ref.read(pantryNotifierProvider);
    final pantryItemState = widget.ref.read(pantryItemNotifierProvider);

    final selectedPantryListId = pantryState.data!.pantries!.isNotEmpty &&
            widget.selectedIndex < pantryState.data!.pantries!.length
        ? pantryState.data!.pantries![widget.selectedIndex].id
        : null;

    if (selectedPantryListId == null) return;

    // Check if we're near the bottom of the scroll view
    if (scrollController.position.pixels >= scrollController.position.maxScrollExtent - 200 &&
        pantryItemState.status != AppStatus.loading &&
        pantryItemState.status != AppStatus.loadingMore) {
      final notifier = widget.ref.read(pantryItemNotifierProvider.notifier);
      if (notifier.canFetchMore) {
        setState(() {
          _isLoadingMore = true;
        });

        notifier.fetchPantryItems(
          id: selectedPantryListId,
          search: widget.currentSearchQuery,
          loadMore: true, // This is the key fix - pass loadMore: true
        ).then((_) {
          if (mounted) {
            setState(() {
              _isLoadingMore = false;
            });
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final pantryItemState = widget.ref.watch(pantryItemNotifierProvider);
    final pantryState = widget.ref.watch(pantryNotifierProvider);

    final selectedPantryListId = pantryState.data!.pantries!.isNotEmpty &&
            widget.selectedIndex < pantryState.data!.pantries!.length
        ? pantryState.data!.pantries![widget.selectedIndex].id
        : null;

    final items =
        selectedPantryListId != null ? (pantryItemState.data?.pantryItems ?? []) : [];

    final isSearching =
        widget.currentSearchQuery != null && widget.currentSearchQuery!.trim().isNotEmpty;
    final hasNoResults = items.isEmpty && widget.newPantryRows.isEmpty;

    if (isSearching && hasNoResults) {
      return const NoDataWidget(
        title: "Nothing Here Yet",
        subtitle: "No items found. Try searching again or add something new.",
        width: 250,
        height: 250,
      );
    }

    if (!isSearching &&
        hasNoResults &&
        pantryState.data != null &&
        pantryState.data!.pantries!.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onAddEmptyRow?.call();
      });
    }

    return SizedBox(
      height: ScreenSizer().getVisibleScreenHeight(context) - 150,
      child: Column(
        children: [
          Table(
            columnWidths: const {
              0: FlexColumnWidth(0.6),
              1: FixedColumnWidth(50),
              2: FlexColumnWidth(3),
              3: FlexColumnWidth(1.5),
              4: FlexColumnWidth(1.5),
              5: FlexColumnWidth(1),
              6: FlexColumnWidth(1.5),
            },
            border: TableBorder.symmetric(
              inside: BorderSide(color: Colors.grey.shade300),
            ),
            children: [
              buildPantryHeaderRow(
                context: context,
                isAllSelected: widget.isAllPantryItemsSelected,
                onSelectAllChanged: (value) => widget.onCheckboxChanged(-1, value),
              ),
            ],
          ),
          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              padding: EdgeInsets.only(bottom: 30.h),
              child: Column(
                children: [
                  Table(
                    columnWidths: const {
                      0: FlexColumnWidth(0.6),
                      1: FixedColumnWidth(50),
                      2: FlexColumnWidth(3),
                      3: FlexColumnWidth(1.5),
                      4: FlexColumnWidth(1.5),
                      5: FlexColumnWidth(1),
                      6: FlexColumnWidth(1.5),
                    },
                    border: TableBorder.symmetric(
                      inside: BorderSide(color: Colors.grey.shade300),
                    ),
                    children: [
                      ...List.generate(items.length, (index) {
                        final item = items[index];
                        return buildPantryDataRow(
                          count: index + 1,
                          item: item,
                          isEditable: widget.editablePantryItems.containsKey(item.id),
                          isSelected: widget.selectedPantryItemIds.contains(item.id),
                          onCheckboxChanged: (value) =>
                              widget.onCheckboxChanged(item.id, value),
                          onTap: () => widget.onMakeEditable(item),
                          controllers: widget.editablePantryItems[item.id],
                          context: context,
                          ref: widget.ref,
                        );
                      }),
                      ...List.generate(widget.newPantryRows.length, (index) {
                        return buildEditablePantryRow(
                          count: items.length + index + 1,
                          controllers: widget.newPantryRows[index],
                          context: context,
                          ref: widget.ref,
                        );
                      }),
                    ],
                  ),
                  // Add loading indicator when loading more
                  if (pantryItemState.status == AppStatus.loadingMore || _isLoadingMore)
                    Padding(
                       padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: LoadingAnimationWidget.fallingDot(
                          color: Colors.black54,
                          size: 50.0,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          buildFooter(
            context: context,
            ref: widget.ref,
            selectedTab: widget.selectedTab,
            onAddMoreRows: () {
              widget.onAddEmptyRow?.call();
            },
          ),
        ],
      ),
    );
  }
}

TableRow buildPantryHeaderRow({
  required BuildContext context,
  required bool isAllSelected,
  required Function(bool?) onSelectAllChanged,
}) {
  return TableRow(
    decoration: BoxDecoration(
      color: Colors.grey.shade200,
      borderRadius: BorderRadius.only(topLeft: Radius.circular(8), topRight: Radius.circular(8)),
    ),
    children: [
      buildTableCell(buildTitle(context, "#")),
      buildTableCell(
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () => onSelectAllChanged(!isAllSelected),
            behavior: HitTestBehavior.opaque,
            child: Checkbox(
              value: isAllSelected,
              onChanged: onSelectAllChanged,
              activeColor: Colors.red,
              side: const BorderSide(color: Colors.red, width: 1),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
              checkColor: Colors.white,
            ),
          ),
        ),
      ),
      buildTableCell(buildTitle(context, "Item")),
      buildTableCell(buildTitle(context, "Purchase Date")),
      buildTableCell(buildTitle(context, "Use By Date")),
      buildTableCell(buildTitle(context, "Amount")),
      buildTableCell(buildTitle(context, "Unit")),
    ],
  );
}

TableRow buildPantryDataRow({
  required int count,
  required PantryItem item,
  required bool isEditable,
  required bool isSelected,
  required Function(bool?) onCheckboxChanged,
  required VoidCallback onTap,
  required Map<String, TextEditingController>? controllers,
  required BuildContext context,
  required WidgetRef ref,
}) {
  return TableRow(
    decoration: BoxDecoration(
      border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      color: isEditable ? Colors.blue.shade50 : null,
    ),
    children: [
      buildTableCell(Center(child: buildRowText(context, "$count"))),
      buildTableCell(
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () => onCheckboxChanged(!isSelected),
            behavior: HitTestBehavior.opaque,
            child: Checkbox(
              value: isSelected,
              onChanged: onCheckboxChanged,
              activeColor: Colors.red,
              side: const BorderSide(color: Colors.red, width: 1),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
              checkColor: Colors.white,
            ),
          ),
        ),
      ),
      buildTableCell(
        isEditable
            ? buildItemAutocompleteField(context, controllers!['item']!, placeholder: "Item name")
            : GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: buildRowText(context, item.item),
          ),
        ),
      ),
      buildTableCell(
        isEditable
            ? buildDatePickerField(context, controllers!['purchaseDate'] ?? TextEditingController(), placeholder: "MM/DD/YYYY")
            : GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: buildRowText(context, DateFormat('MM/dd/yyyy').format(item.purchasedDate)),
          ),
        ),
      ),
      buildTableCell(
        isEditable
            ? buildDatePickerField(context, controllers!['useByDate']!, placeholder: "MM/DD/YYYY")
            : GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: buildRowText(context, DateFormat('MM/dd/yyyy').format(item.useByDate)),
          ),
        ),
      ),
      buildTableCell(
        isEditable
            ? buildAmountTextField(context, controllers!['amount']!, placeholder: "Amount")
            : GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: buildRowText(context, item.amount.toString()),
          ),
        ),
      ),
      buildTableCell(
        isEditable
            ? buildUnitAutocompleteField(context, controllers!['unit']!, placeholder: "Unit", ref: ref)
            : GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: buildRowText(context, item.unit),
          ),
        ),
      ),
    ],
  );
}

TableRow buildEditablePantryRow({
  required int count,
  required Map<String, TextEditingController> controllers,
  required BuildContext context,
  required WidgetRef ref,
}) {
  return TableRow(
    decoration: BoxDecoration(
      border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
    ),
    children: [
      buildTableCell(Center(child: buildRowText(context, "$count"))),
      buildTableCell(
        MouseRegion(
          cursor: SystemMouseCursors.basic,
          child: Checkbox(
            value: false,
            onChanged: null,
            activeColor: Colors.red,
            side: const BorderSide(color: Colors.red, width: 1),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
            checkColor: Colors.white,
          ),
        ),
      ),
      buildTableCell(buildItemAutocompleteField(context, controllers['item']!, placeholder: "Item name")),
      buildTableCell(buildDatePickerField(context, controllers['purchaseDate']!, placeholder: "MM/DD/YYYY")),
      buildTableCell(buildDatePickerField(context, controllers['useByDate']!, placeholder: "MM/DD/YYYY")),
      buildTableCell(buildAmountTextField(context, controllers['amount']!, placeholder: "Amount")),
      buildTableCell(buildUnitAutocompleteField(context, controllers['unit']!, placeholder: "Unit", ref: ref)),
    ],
  );
}
