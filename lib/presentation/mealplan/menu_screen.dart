import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import '../../../../../app/imports/core_imports.dart';
import '../../core/providers/mealplan/meal_plan_provider.dart';
import '../cookbook/widgets/custom_desc_text.dart';
import '../cookbook/widgets/edit_button.dart';
import 'widgets/item_tile.dart';
import 'widgets/sho_add_menu_dialog.dart';

class MenuScreen extends ConsumerWidget {
  String selectedTab = 'Menu';
  final TextEditingController recipeNameController = TextEditingController();
  final TextEditingController recipeDescController = TextEditingController();
  final TextEditingController recipePortionController = TextEditingController();

  final Map<String, List<String>> _filterOptions = {
    "Lunch": ["Breakfast", "Lunch", "Dinner"],
  };
  final Map<String, String> _selectedFilters = {
    "Lunch": "Lunch",
  };

  MenuScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mealPlans = ref.watch(mealPlansProvider);
    final selectedIndex = ref.watch(selectedMealPlanProvider);
    final screenSize = MediaQuery.of(context).size;

    return Expanded(
      flex: 6,
      child: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(AssetsManager.background_img, fit: BoxFit.cover),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: screenSize.width * 0.03,
              vertical: screenSize.width * 0.03,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                    padding: EdgeInsets.all(25),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        // Text and Action Section
                        Expanded(
                          flex: 3,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              // Image Section
                              ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: Image.asset(
                                  AssetsManager.mealPlan,
                                  width: 200.w,
                                  height: 210.h,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              SizedBox(width: 25.h),

                              // Title and icons row
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Dinner Party",
                                    style: context.theme.textTheme.bodyMedium!
                                        .copyWith(
                                      color: AppColors.primaryGreyColor,
                                      fontSize: 30.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(
                                    height: 55.h,
                                  ),
                                  CustomDescText(
                                    desc: "Menu Notes:",
                                    size: 20.sp,
                                    textColor: AppColors.primaryLightTextColor,
                                  ),
                                  SizedBox(height: 5.h),
                                  Text(
                                    "Night party Veg Food & Non Veg Food items with drinks",
                                    style: context.theme.textTheme.displaySmall!
                                        .copyWith(
                                      color: AppColors.primaryGreyColor,
                                      fontSize: 20.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomDescText(
                                    desc: "Menu Portion:",
                                    size: 20.sp,
                                    textColor: AppColors.primaryLightTextColor,
                                  ),
                                  SizedBox(
                                    height: 5.h,
                                  ),
                                  Text(
                                    "2",
                                    style: context.theme.textTheme.bodyMedium!
                                        .copyWith(
                                      color: AppColors.primaryGreyColor,
                                      fontSize: 20.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(
                                    height: 32.h,
                                  ),
                                  CustomDescText(
                                    desc: "Meal Type:",
                                    size: 20.sp,
                                    textColor: AppColors.primaryLightTextColor,
                                  ),
                                  SizedBox(
                                    height: 5.h,
                                  ),
                                  Text(
                                    "Lunch",
                                    style: context.theme.textTheme.bodyMedium!
                                        .copyWith(
                                      color: AppColors.primaryGreyColor,
                                      fontSize: 20.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                              SizedBox(
                                width: 30.w,
                              ),
                              SizedBox(width: 14.w),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Align(
                                alignment: Alignment.topRight,
                                child: EditButton(
                                  onPressed: () {
                                    //  context.go('/cookbook/cookbookDetail/recipeDetail/editRecipe');
                                  },
                                ),
                              ),
                              SizedBox(
                                width: 15.w,
                              ),
                              TextButton.icon(
                                style: TextButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    side:
                                        BorderSide(color: Colors.grey.shade300),
                                  ),
                                ),
                                onPressed: () {},
                                label: SvgPicture.asset(
                                  AssetsManager.dlt,
                                  height: 50.h,
                                  width: 60.w,
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    )),
                // Title and Inputs

                SizedBox(height: 24.h),

                Expanded(
                  child: Container(
                    // padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(18),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            // color: AppColors.primaryLightTextColor,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.white,
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),

                          padding: EdgeInsets.only(
                            left: 12,   // Start (left)
                            right: 30,  // End (right)
                            top: 12,     // Top
                            bottom: 12,  // Bottom
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,

                            children: [
                              Padding(
                                padding: EdgeInsets.only(left: 16.0), // <-- start margin
                                child: Text(
                                  "Items",
                                  style: context.theme.textTheme.displaySmall!.copyWith(
                                    color: Colors.black,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 22.sp,
                                  ),
                                ),
                              ),
                              CustomButton(
                                  width: 150.w,
                                  height: 50.h,
                                  icon: Icons.add,
                                  text: "Add",
                                  fontSize: 20.sp,
                                  onPressed: () {
                                    showAddItemDialog(context, ref);
                                  })
                            ],
                          ),
                        ),
                        Divider(
                          height: 1,
                          color: AppColors.lightestGreyColor,
                        ),
                        Expanded(
                          child: ListView.separated(
                            itemCount: mealPlans[selectedIndex]["items"].length,
                            separatorBuilder: (context, index) => Divider(
                                height: 1, color: AppColors.lightestGreyColor),
                            itemBuilder: (context, index) {
                              // Alternate background colors
                              final backgroundColor = index % 2 == 0
                                  ? Colors.white
                                  : Colors.blue.shade50;

                              return Container(
                                  decoration: BoxDecoration(
                                    color: backgroundColor,
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppColors.lightestGreyColor,
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: ItemTile(
                                      name: mealPlans[selectedIndex]["items"]
                                          [index]));
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _circleIcon(String assetName, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Card(
        elevation: 2,
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(3.0),
          child: SvgPicture.asset(assetName),
        ),
      ),
    );
  }

  Widget _buildDropdownFilter(String filterKey) {
    final selected = _selectedFilters[filterKey]!;
    return PopupMenuButton<String>(
      color: Colors.white,
      onSelected: (value) {
        //setState(() {
        _selectedFilters[filterKey] = value;
        //});
      },
      itemBuilder: (context) {
        return _filterOptions[filterKey]!.map((option) {
          return PopupMenuItem<String>(
            value: option,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
              decoration: option == selected
                  ? BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(6),
                    )
                  : null,
              child: Text(
                option,
                style: TextStyle(
                  fontSize: context.theme.textTheme.displaySmall!.fontSize,
                  fontWeight:
                      option == selected ? FontWeight.w400 : FontWeight.w400,
                  color: option == selected
                      ? Colors.white
                      : AppColors.primaryGreyColor,
                ),
              ),
            ),
          );
        }).toList();
      },
      child: Container(
        height: 80.h,
        width: 300.w,
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
        margin: EdgeInsets.only(right: 20.w),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black38.withOpacity(.2)),
          color: AppColors.secondaryColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomDescText(
              desc: filterKey,
              textColor: Colors.black,
              size: 22.sp,
            ),
            SizedBox(
              width: 5.w,
            ),
            Container(
                margin: EdgeInsets.symmetric(horizontal: 5.w),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.black38.withOpacity(.2)),
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Icon(
                  Icons.keyboard_arrow_down,
                  size: 20.w,
                  color: AppColors.secondaryColor,
                )),
          ],
        ),
      ),
    );
  }
}

void showAddItemDialog(BuildContext context, WidgetRef ref) {
  showDialog(
    context: context,
    useRootNavigator: true, //<--- add this
    barrierDismissible: false,
    builder: (_) => AddMenuItemDialog(),
  );
}
