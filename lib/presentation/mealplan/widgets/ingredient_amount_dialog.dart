import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../../app/assets_manager.dart';
import '../../../app/theme/colors.dart';
import '../../../core/widgets/custom_button.dart';

void showIngredientAmountDialog(BuildContext context) {
  showDialog(
    context: context,
    useRootNavigator: true, //<--- add this
    barrierDismissible: false,
    builder: (_) => IngredientAmountDialog(),
  );
}

class IngredientAmountDialog extends StatefulWidget {
  @override
  State<IngredientAmountDialog> createState() => _IngredientAmountDialogState();
}

class _IngredientAmountDialogState extends State<IngredientAmountDialog> {
  bool isPerMenu = true;
  int amount = 3;
  int unit = 3;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20,horizontal: 30),
        child: Container(
          width: 600.w,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              /// Close Button Row
              Stack(
                alignment: Alignment.center,
                children: [
                  Center(
                    child: Text(
                      "Ingredient Amount",
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: AppColors.primaryGreyColor,
                            fontSize: 30.sp,
                            fontWeight: FontWeight.w700,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Align(
                    alignment: Alignment.topRight,
                    child: IconButton(
                      icon: SvgPicture.asset(
                        AssetsManager.cross,
                        height: 40.h,
                        width: 40.w,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              /// Ingredient Title
              Row(
                children: [
                  Text(
                    "Ingredient",
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: AppColors.primaryGreyColor,
                          fontSize: 26.sp,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              const Align(
                alignment: Alignment.centerLeft,
                child: Text('Egg',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.primaryGreyColor,
                      fontWeight: FontWeight.w600,
                    )),
              ),

              const SizedBox(height: 16),

              /// Radio Buttons
              Row(
                children: [
                  Radio<bool>(
                    value: false,
                    groupValue: isPerMenu,
                    activeColor: Colors.red,
                    onChanged: (value) => setState(() => isPerMenu = value!),
                  ),
                  Text(
                    'Per Serving',
                    style: context.theme.textTheme.displaySmall!.copyWith(
                      color: AppColors.primaryGreyColor,
                      fontWeight: FontWeight.w400,
                      fontSize: 18.sp,
                    ),
                  ),
                  const SizedBox(width: 20),
                  Radio<bool>(
                    value: true,
                    groupValue: isPerMenu,
                    activeColor: Colors.red,
                    onChanged: (value) => setState(() => isPerMenu = value!),
                  ),
                  Text(
                    'Per Menu',
                    style: context.theme.textTheme.displaySmall!.copyWith(
                      color: AppColors.primaryGreyColor,
                      fontWeight: FontWeight.w400,
                      fontSize: 18.sp,
                    ),
                  )
                ],
              ),

              const SizedBox(height: 10),

              /// Info Text
              Text(
                'The amount per is not scaled when using set serving commnd.',
                style: context.theme.textTheme.displaySmall!.copyWith(
                  color: AppColors.primaryGreyColor,
                  fontWeight: FontWeight.w500,
                  fontSize: 20.sp,
                ),
              ),

              const SizedBox(height: 20),

              /// Amount and Unit Dropdowns
              Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          "Amount",
                          style: context.theme.textTheme.displaySmall!.copyWith(
                            color: AppColors.primaryGreyColor,
                            fontWeight: FontWeight.w400,
                            fontSize: 18.sp,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                                color: AppColors.lightestGreyColor, width: 1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SizedBox(
                            height: 60.h,
                            child: DropdownButtonFormField<int>(
                              value: amount,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding:
                                    EdgeInsets.symmetric(horizontal: 8),
                              ),
                              items: List.generate(
                                10,
                                (index) => DropdownMenuItem(
                                  value: index,
                                  child: Text(
                                    index.toString().padLeft(2, '0'),
                                    style: TextStyle(
                                      fontSize: 14,
                                      // Change to your desired size
                                      fontWeight: FontWeight.w500,
                                      color: Colors
                                          .black, // Change color as needed
                                    ),
                                  ),
                                ),
                              ),
                              onChanged: (val) => setState(() => amount = val!),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      children: [
                        Text("Unit",
                            style:
                                context.theme.textTheme.displaySmall!.copyWith(
                              color: AppColors.primaryGreyColor,
                              fontWeight: FontWeight.w400,
                              fontSize: 18.sp,
                            )),
                        const SizedBox(height: 10),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                                color: AppColors.lightestGreyColor, width: 1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SizedBox(
                            height: 60.h,
                            child: DropdownButtonFormField<int>(
                              value: unit,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding:
                                    EdgeInsets.symmetric(horizontal: 8),
                              ),
                              items: List.generate(
                                  10,
                                  (index) => DropdownMenuItem(
                                        value: index,
                                        child: Text(
                                          index.toString().padLeft(2, '0'),
                                          style: TextStyle(
                                            fontSize: 14,
                                            // Change to your desired size
                                            fontWeight: FontWeight.w500,
                                            color: Colors
                                                .black, // Change color as needed
                                          ),
                                        ),
                                      )),
                              onChanged: (val) => setState(() => unit = val!),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              /// Save Button
              Center(
                child: CustomButton(
                  text: "Save",
                  onPressed: () => Navigator.pop(context),
                  width: 250.w,
                  height: 55.h,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
