import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../app/assets_manager.dart';
import '../../../app/theme/colors.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text.dart';

class AddMealTypeDialog extends StatefulWidget {
  const AddMealTypeDialog({super.key});

  @override
  State<AddMealTypeDialog> createState() => _AddMealTypeDialogState();
}

class _AddMealTypeDialogState extends State<AddMealTypeDialog> {
  final Map<String, bool> mealTypes = {
    'Breakfast': true,
    'Brunch': false,
    'AM Snack': false,
    'Lunch': true,
    'PM Snack': false,
    'Dinner': true,
    'Evening Snack': false,
    'Nightcap': false,
  };

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Container(
          width: 700,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  Center(
                    child:
                    CustomText(
                      text:  'Add Meal Type',
                      color: AppColors.primaryGreyColor,
                      size: DeviceUtils().isTabletOrIpad(context) ? 24 : 24,
                      weight: FontWeight.w700,
                    ),

                  ),
                  Align(
                    alignment: Alignment.topRight,
                    child: InkWell(
                      onTap: () => Navigator.of(context).pop(),
                      child: SvgPicture.asset(
                        AssetsManager.cross,
                        height: 20,
                        width: 20,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.all(20),
                margin: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.greyBorderColor),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  children: mealTypes.entries.map((entry) {
                    return _buildCustomCheckboxTile(
                      entry.key,
                      entry.value,
                      (newValue) {
                        setState(() {
                          mealTypes[entry.key] = newValue ?? false;
                        });
                      },
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 10),
              Center(
                child: CustomButton(
                  text: 'Insert Meal Type',
                  onPressed: () =>  Navigator.of(context).pop(mealTypes)
                  ,
                  width: 250,
                  height: 55,
                ),
              ),
              const SizedBox(height: 10),

            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomCheckboxTile(
      String label, bool value, Function(bool?) onChanged) {
    return InkWell(
      onTap: () => onChanged(!value),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 6),
        child: Row(
          children: [
            SvgPicture.asset(
              value
                  ? AssetsManager.ic_unchecked
                  : AssetsManager.ic_checked,
              height: 20,
              width: 20,
            ),
            const SizedBox(width: 10),
            Expanded(
              child:
              CustomText(
                text: label,
                color: AppColors.primaryGreyColor,
                size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
                weight: FontWeight.w400,
              ),

            ),
          ],
        ),
      ),
    );
  }
}
