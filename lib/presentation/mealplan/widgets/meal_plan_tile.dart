import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../cookbook/widgets/custom_desc_text.dart';

class MealPlanTile extends StatelessWidget {
  final Map<String, dynamic> data;
  final bool isSelected;

  const MealPlanTile({super.key, required this.data , this.isSelected = false,});

  @override
  Widget build(BuildContext context) {
    return  Card(
      color: context.theme.cardColor,
      elevation: 5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(25.r),
        side: isSelected
            ? BorderSide(color: Colors.blue, width: 3)
            : BorderSide(color: AppColors.greyBorderColor, width: 1),
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 38.sp , bottom: 38.sp, left: 20.sp , right: 20.sp),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Text and Action Section
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      "GYM Diet",
                      style: context.theme.textTheme.displaySmall!.copyWith(
                        color: AppColors.primaryGreyColor,
                        fontSize: 25.sp,
                        fontWeight: FontWeight.w400,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  CustomDescText(
                    desc: "Updated: 5 days ago",
                    size: headingSixFontSize * MediaQuery.textScaleFactorOf(context),
                    textColor: AppColors.textGreyColor,

                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

