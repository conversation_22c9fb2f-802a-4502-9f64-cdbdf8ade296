class MealItem {
  final String name;
  int portion;
  bool selected;

  MealItem({
    required this.name,
    this.portion = 1,
    this.selected = false,
  });

  // Optional: create a copy with updates
  MealItem copyWith({
    String? name,
    int? portion,
    bool? selected,
  }) {
    return MealItem(
      name: name ?? this.name,
      portion: portion ?? this.portion,
      selected: selected ?? this.selected,
    );
  }

  // Optional: convert to/from JSON
  factory MealItem.fromJson(Map<String, dynamic> json) {
    return MealItem(
      name: json['name'],
      portion: json['portion'] ?? 1,
      selected: json['selected'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'portion': portion,
      'selected': selected,
    };
  }
}
