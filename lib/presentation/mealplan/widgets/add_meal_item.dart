import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../../app/assets_manager.dart';
import '../../../app/theme/colors.dart';
import '../../../core/widgets/custom_button.dart';

class AddMealItemsDialog extends StatefulWidget {
  const AddMealItemsDialog({super.key});

  @override
  State<AddMealItemsDialog> createState() => _AddItemsDialogState();
}

class _AddItemsDialogState extends State<AddMealItemsDialog> {
  String selectedMealType = 'Lunch';
  String selectedType = 'Recipe';

  List<String> selectedTags = ['Sushi', 'Pasta'];
  List<String> cookbooks = [
    'Italian Dishes',
    'Chines Dishes',
    'Chicken Recipes',
    'Italian Dishes',
    'Chines Dishes',
    'Chicken Recipes',
    'Chicken Recipes',
    'Italian Dishes',
    'Chines Dishes',
    'Chicken Recipes',
  ];
  List<String> recipes = [
    'Shushi',
    'Pizza',
    'Chines Noodles',
    'Pasta',
    'Pizza',
    'Chines Noodles',
    'Pasta',
    'Chines Noodles',
    'Pasta',
    'Chines Noodles',
  ];
  Set<String> selectedRecipes = {'Shushi', 'Pizza'};

  int selectedCookbookIndex = -1; // Add this in your State class


  void _toggleRecipe(String recipe) {
    setState(() {
      if (selectedRecipes.contains(recipe)) {
        selectedRecipes.remove(recipe);
      } else {
        selectedRecipes.add(recipe);
      }
    });
  }
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: SizedBox(
          width: 600,
          height: 600,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// Header
              Stack(
                children: [
                  Center(
                    child: Column(
                      children: [
                        Text(
                          "Add Items",
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: AppColors.primaryGreyColor,
                            fontSize: 28.sp,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                         Text(
                          'Day 1 of 7',
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: AppColors.blackTextColor,
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w500,
                          ),                        ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.topRight,
                    child: IconButton(
                      icon: SvgPicture.asset(
                        AssetsManager.cross,
                        height: 40.h,
                        width: 40.w,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              /// Meal Type Dropdown
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                      color: AppColors.lightestGreyColor, width: 1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SizedBox(
                  height: 60.h,
                  child: DropdownButtonFormField<String>(
                    isDense: true,
                    value: selectedMealType,
                    style: context.theme.textTheme.displaySmall!.copyWith(
                      color: AppColors.primaryGreyColor,
                      fontWeight: FontWeight.w400,
                      fontSize: 16.sp,
                    ),
                    icon: SvgPicture.asset(
                      AssetsManager.droparrow,
                      height: 24,
                      width: 24,
                    ),
                    items: ['Breakfast', 'Lunch', 'Dinner']
                        .map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type),
                    ))
                        .toList(),
                    onChanged: (val) => selectedMealType = val!,
                    decoration: const InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                      fillColor: Colors.white,
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ),

              SizedBox(height: 10.h),

              /// Type Dropdown
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                      color: AppColors.lightestGreyColor, width: 1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SizedBox(
                  height: 60.h,
                  child: DropdownButtonFormField<String>(
                    isDense: true,
                    value: selectedType,
                    style: context.theme.textTheme.displaySmall!.copyWith(
                      color: AppColors.primaryGreyColor,
                      fontWeight: FontWeight.w400,
                      fontSize: 16.sp,
                    ),
                    icon: SvgPicture.asset(
                      AssetsManager.droparrow,
                      height: 24,
                      width: 24,
                    ),
                    items: ['Ingredient', 'Recipe', 'Text']
                        .map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type),
                    ))
                        .toList(),
                    onChanged: (val) => selectedType = val!,
                    decoration: const InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                      fillColor: Colors.white,
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ),

              SizedBox(height: 10.h),

              /// Tags
              buildLabel("Tags:", context),
              Wrap(
                spacing: 8,
                children: selectedTags.map((tag) => Chip(
                  deleteIcon: const Icon(Icons.close, size: 16),
                  onDeleted: () => setState(() {
                    selectedTags.remove(tag);
                  }),
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                       AssetsManager.menu_img, // Replace with your image path
                        height: 18,
                        width: 18,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        tag,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: AppColors.primaryLightTextColor,
                        ),
                      ),
                    ],
                  ),
                )).toList(),
              ),

              const SizedBox(height: 10),

              /// Cookbooks and Recipes
              Expanded(
                child: Row(
                  children: [
                    /// Cookbooks
                    Expanded(
                      flex: 1,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text.rich(
                            TextSpan(
                              text: 'Cookbooks ',
                              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                color: AppColors.primaryGreyColor,
                                fontSize: 20.sp,
                                fontWeight: FontWeight.w600,
                              ),
                              children: [
                                TextSpan(
                                  text: '(${cookbooks.length})',
                                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    color: AppColors.primaryColor,
                                    fontSize: 20.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.greyBorderColor),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: ListView.builder(
                                itemCount: cookbooks.length,
                                itemBuilder: (context, index) {
                                  bool isHovered = false;

                                  return StatefulBuilder(
                                    builder: (context, setInnerState) {
                                      final isSelected = selectedCookbookIndex == index;

                                      return MouseRegion(
                                        onEnter: (_) => setInnerState(() => isHovered = true),
                                        onExit: (_) => setInnerState(() => isHovered = false),
                                        child: GestureDetector(
                                          onTap: () {
                                            setState(() => selectedCookbookIndex = index);
                                          },
                                          child: Container(
                                            color: isSelected
                                                ? Colors.blue.withOpacity(0.2)
                                                : (isHovered ? Colors.blue.withOpacity(0.1) : Colors.transparent),
                                            child: ListTile(
                                              dense: true,
                                              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                                              visualDensity: const VisualDensity(vertical: -4),
                                              title: Text(
                                                cookbooks[index],
                                                style: Theme.of(context).textTheme.displaySmall!.copyWith(
                                                  color: AppColors.primaryGreyColor,
                                                  fontSize: 16.sp,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),

                    /// Recipes
                    Expanded(
                      flex: 3,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text.rich(
                            TextSpan(
                              text: 'Recipe ',
                              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                color: AppColors.primaryGreyColor,
                                fontSize: 20.sp,
                                fontWeight: FontWeight.w600,
                              ),
                              children: [
                                TextSpan(
                                  text: '(${recipes.length})',
                                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                  color: AppColors.primaryColor,
                                  fontSize: 20.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.greyBorderColor),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: ListView.builder(
                                itemCount: recipes.length,
                                itemBuilder: (context, index) {
                                  final recipe = recipes[index];
                                  final isSelected =
                                  selectedRecipes.contains(recipe);
                                  return ListTile(
                                    dense: true, // Reduces vertical padding
                                    contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 0), // Remove vertical spacing
                                    visualDensity: VisualDensity(vertical: -4), // Tighter height (optional)
                                    title: Text(recipe,style: Theme.of(context).textTheme.displaySmall!.copyWith(
                                      color: AppColors.primaryGreyColor,
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w400,
                                    )),
                                    tileColor: isSelected
                                        ? Colors.red.withOpacity(0.1)
                                        : null,
                                    trailing: isSelected
                                        ? const Icon(Icons.check,
                                        color: Colors.red)
                                        : null,
                                    onTap: () => _toggleRecipe(recipe),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              /// Save Button
              Center(
                child: CustomButton(
                  text: "Save",
                  onPressed: () => Navigator.pop(context),
                  width: 250.w,
                  height: 55.h,
                ),
              ),


            ],
          ),
        ),
      ),
    );
  }
  Widget buildLabel(String text, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0, left: 2),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(text,
            style: Theme.of(context).textTheme.displaySmall!.copyWith(
              color: AppColors.primaryLightTextColor,
              fontSize: 18.sp,
              fontWeight: FontWeight.w400,
            )),
      ),
    );
  }

}
