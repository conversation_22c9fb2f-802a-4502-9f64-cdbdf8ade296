import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../cookbook/widgets/custom_desc_text.dart';

class ItemTile extends StatelessWidget {
  final String name;

  const ItemTile({super.key, required this.name});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 12, // Start (left)
        right: 30, // End (right)
        top: 12, // Top
        bottom: 12, // Bottom
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(
                  width: 20.w,
                ),
                Image.asset(AssetsManager.menu_img),
                SizedBox(
                  width: 20.w,
                ),
                CustomDescText(
                  desc: name,
                  textColor: Colors.black,
                  size: 20.sp,
                )
              ],
            ),
          ),
          Row(
            children: [
              CustomDescText(
                desc: "2",
                textColor: AppColors.blackTextColor,
                size: 20.sp,
              )
            ],
          ),
          SizedBox(
            width: 20.w,
          )
        ],
      ),
    );
  }
}
