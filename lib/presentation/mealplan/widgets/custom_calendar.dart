import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CalendarWidget extends StatefulWidget {
  final DateTime initialMonth;
  final ValueChanged<List<DateTime>>? onDatesSelected;
  final bool multiSelect;

  const CalendarWidget({
    Key? key,
    required this.initialMonth,
    this.onDatesSelected,
    this.multiSelect = true,
  }) : super(key: key);

  @override
  _CalendarWidgetState createState() => _CalendarWidgetState();
}

class _CalendarWidgetState extends State<CalendarWidget> {
  late DateTime _currentMonth;
  List<DateTime> _selectedDates = [];

  @override
  void initState() {
    super.initState();
    _currentMonth = widget.initialMonth;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildMonthHeader(),
        _buildWeekdaysHeader(),
        _buildCalendarGrid(),
      ],
    );
  }

  Widget _buildMonthHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        DateFormat('MMMM yyyy').format(_currentMonth),
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildWeekdaysHeader() {
    return Row(
      children: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
          .map((day) => Expanded(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Text(
              day,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
        ),
      ))
          .toList(),
    );
  }

  Widget _buildCalendarGrid() {
    final firstDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month, 1);
    final daysInMonth = DateUtils.getDaysInMonth(_currentMonth.year, _currentMonth.month);
    final startingWeekday = firstDayOfMonth.weekday;

    List<Widget> dayWidgets = [];

    // Add empty cells for days before the first day of the month
    for (int i = 0; i < startingWeekday % 7; i++) {
      dayWidgets.add(Container());
    }

    // Add day cells for the month
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(_currentMonth.year, _currentMonth.month, day);
      final isSelected = _selectedDates.any((selectedDate) =>
      selectedDate.year == date.year &&
          selectedDate.month == date.month &&
          selectedDate.day == date.day);

      dayWidgets.add(
        GestureDetector(
          onTap: () => _handleDateSelection(date),
          child: Container(
            margin: EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue : Colors.transparent,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                day.toString(),
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ),
        ),
      );
    }

    // Create rows for the calendar grid
    List<Widget> rows = [];
    for (int i = 0; i < dayWidgets.length; i += 7) {
      int end = i + 7;
      if (end > dayWidgets.length) end = dayWidgets.length;
      rows.add(Row(
        children: dayWidgets.sublist(i, end).map((day) => Expanded(child: day)).toList(),
      ));
    }

    return Column(
      children: rows,
    );
  }

  void _handleDateSelection(DateTime date) {
    setState(() {
      if (!widget.multiSelect) {
        _selectedDates = [date];
      } else if (_selectedDates.contains(date)) {
        _selectedDates.remove(date);
      } else {
        _selectedDates.add(date);
      }
    });

    widget.onDatesSelected?.call(_selectedDates);
  }
}