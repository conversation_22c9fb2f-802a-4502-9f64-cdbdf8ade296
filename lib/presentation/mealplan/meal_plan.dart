// Import Statements
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_appbar.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../core/providers/cookbook_detail_provider.dart';
import '../../core/widgets/custom_input_field.dart';
import '../cookbook/widgets/cookbook_details_card.dart';
 import '../shopping/sub_view/custom_tabview.dart';


class AddMealPlanScreen extends ConsumerStatefulWidget {
  const AddMealPlanScreen({super.key});

  @override
  ConsumerState<AddMealPlanScreen> createState() => _AddMealPlanScreenState();
}

  class _AddMealPlanScreenState extends ConsumerState<AddMealPlanScreen> {

  final PageController _pageController = PageController();
  final TextEditingController recipeNameController = TextEditingController();
  final TextEditingController recipeDescController = TextEditingController();
  final TextEditingController searchController = TextEditingController();

  int _currentPage = 0;
  int _selectedCookbookIndex = 0;
  String selectedTab = 'Menu';


  final items = [
    {'name': 'Spanish omelette', 'image': '🍳'},
    {'name': 'Tater tots', 'image': '🍟'},
    {'name': 'San Francisco sourdough bread', 'image': '🥖'},
    {'name': 'Milk', 'image': '🥛'},
    {'name': 'Pot roast', 'image': '🍖'},
    {'name': 'Eggs', 'image': '🥚'},
  ];

  final Map<String, String> _selectedFilters = {
    "Lunch": "Item One",
  };

  final Map<String, List<String>> _filterOptions = {
    "Lunch": ["BreakFeast", "Dinner", "Lunch"],
  };


  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  int? selectedIndex;

  @override
  void dispose() {
    _pageController.dispose();
    recipeNameController.dispose();
    recipeDescController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
   // final cookbooks = ref.watch(cookbookProvider);
    final cookbookDetail = ref.watch(cookbookDetailProvider);

    final screenSize = MediaQuery.of(context).size;
    final width = screenSize.width;

  

    return Scaffold(
      appBar: CustomAppBar(title: "Meal Plan" ,
      actions: [
        // Nutrition Analysis Button
        TextButton.icon(
          onPressed: () {},
          icon: SvgPicture.asset(
            'assets/mealplan/analysis.svg',
            height: 16,
            width: 18,
          ),
          label: const Text(
            "Nutrition Analysis",
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              color: AppColors.texLightBlackColor,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),

        const SizedBox(width: 8),
        // Add Button
        TextButton.icon(
          onPressed: () {},
          icon: SvgPicture.asset(
            'assets/mealplan/add.svg',
            height: 17,
            width: 17,
          ),
          label: const Text(
            "Add",
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              color: AppColors.texLightBlackColor,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),

        const SizedBox(width: 8),

        // Search Bar
        CustomSearchBar(
          controller: searchController,
          width: 440.w,
          height: 60.h,
        ),
      ],
      ),
      body: Row(
        children: [
          _buildLeftSidebar(screenSize, cookbookDetail),
         Expanded(
           flex: 7,
             child: Stack(
               fit: StackFit.expand,
               children: [
                 Image.asset(AssetsManager.background_img, fit: BoxFit.cover),
                 Padding(
                   padding: EdgeInsets.symmetric(
                     horizontal: screenSize.width * 0.03,
                     vertical: screenSize.width * 0.03,
                   ),
                   child: Column(
                     children: [
                        _buildRecipeForm(),
                        _buildItemList(),
                     ],
                   ),

                 ),
               ],
             ))
         // _buildRightPanel(screenSize),
        ],
      ),
    );
  }

  Widget _buildLeftSidebar(Size screenSize, List cookbooks) {
    return Expanded(
      flex: 2,
      child: Column(
        children: [
         // Container(height: 1, width: 500, color: Colors.grey.shade400),
          SizedBox(height: 20.h),
          CustomTabView(
            width: 210.w,
              selectedTabColor: Colors.white,
              tabs:  ['Menu', 'Meal Plans'],
              selected: selectedTab,
               bgTabColor: Colors.red,
               onChanged: (tab) => setState(() => selectedTab = tab),
              fontSize: 18.sp),

          // MealPlanCustomTabview(
          //   width: 200.w,
          //   fontSize: 16.sp,
          //   tabs: ['Menu', 'Meal Plans'],
          //   selected: selectedTab,
          //   onChanged: (tab) => setState(() => selectedTab = tab),
          // ),
          SizedBox(height: 20.w),
          CustomSearchBar(
            controller: searchController,
          ),
          Expanded(
            flex: 4,
            child: ListView.separated(
              padding: const EdgeInsets.all(12),
              itemCount: cookbooks.length,
              separatorBuilder: (_, __) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCookbookIndex = index;
                      _currentPage = 0;
                    });
                  },
                  child: CookbookDetailCard(
                    cookbook: cookbooks[index],
                    isSelected: _selectedCookbookIndex == index, onMenuItemSelected: (value ) {  },
                  ),
                );
              },
            ),
          ),
          const Divider(),
          Padding(
            padding:  EdgeInsets.symmetric(horizontal: 10.w ),
            child: TableCalendar(
              firstDay: DateTime.utc(2020, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: _focusedDay,
              selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDay = selectedDay;
                  _focusedDay = focusedDay;
                });
              },
              calendarStyle: CalendarStyle(
                selectedDecoration: const BoxDecoration(
                  color: Colors.blueAccent,
                  shape: BoxShape.circle,
                ),
                todayDecoration: const BoxDecoration(
                  color: Colors.redAccent,
                  shape: BoxShape.circle,
                ),
                selectedTextStyle: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                todayTextStyle: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                defaultTextStyle: const TextStyle(color: Colors.black87),
                weekendTextStyle: const TextStyle(color: Colors.deepOrange),
                outsideDaysVisible: false,
              ),
              headerStyle: const HeaderStyle(
                titleCentered: true,
                formatButtonVisible: false,
                leftChevronIcon: Icon(Icons.chevron_left, color: Colors.red),
                rightChevronIcon:
                Icon(Icons.chevron_right, color: Colors.red),
              ),
            ),
          ),
          SizedBox(height: 10.h),
        ],
      ),
    );
  }

  Widget _buildRightPanel(Size screenSize) {
    return Expanded(
      flex: 7,
      child: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(AssetsManager.background_img, fit: BoxFit.cover),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: screenSize.width * 0.03,
              vertical: screenSize.width * 0.03,
            ),
            child: Column(
              children: [
                _buildRecipeForm(),
                _buildItemList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecipeForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [BoxShadow(blurRadius: 6, color: Colors.black26)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Row for Title and Dropdown
          Expanded(
            child: Row(
              children: [
                CustomInputField(
                  hintText: "Untitled",
                  controller: recipeNameController,
                  maxLines: 1,
                 // height: 60.h,
                ),
                /// Title Input
                // Expanded(
                //   flex: 3,
                //   child: TextField(
                //     decoration: InputDecoration(
                //       labelText: 'Title:',
                //       hintText: 'Untitled',
                //       border: OutlineInputBorder(),
                //     ),
                //   ),
                // ),

                SizedBox(width: 12),

                /// Dropdown Menu
                DropdownButtonFormField<String>(
                  value: 'Lunch',
                  items: ['Breakfast', 'Lunch', 'Dinner']
                      .map((e) => DropdownMenuItem(
                    value: e,
                    child: Text(e),
                  ))
                      .toList(),
                  onChanged: (val) {},
                  decoration: InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          /// Row for Menu Notes and Portions & Button
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                /// Menu Notes (TextArea)
                TextField(
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'Menu Notes',
                    hintText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                ),

                SizedBox(width: 12),

                /// Portions + Button
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Menu Portions", style: TextStyle(fontSize: 12, color: Colors.black)),
                    SizedBox(height: 8),
                    TextField(
                      decoration: InputDecoration(
                        hintText: "2",
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    SizedBox(height: 12),
                    ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        padding: EdgeInsets.symmetric(vertical: 12),
                        minimumSize: Size(double.infinity, 40),
                      ),
                      child: Text(
                        "Set Amount",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildRecipeForm() {
  //   return Container(
  //     padding:  EdgeInsets.all(16),
  //     margin:  EdgeInsets.all(16),
  //     decoration: BoxDecoration(
  //       color: Colors.white,
  //       borderRadius: BorderRadius.circular(12),
  //       boxShadow: const [BoxShadow(blurRadius: 6, color: Colors.black26)],
  //     ),
  //     child: Row(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Expanded( child: _buildRecipeHeader()),
  //          //Spacer(),
  //         // Column(
  //         //   crossAxisAlignment: CrossAxisAlignment.start,
  //         //   children: [
  //         //     Wrap(
  //         //       spacing: 8,
  //         //       runSpacing: 8,
  //         //       children: _filterOptions.keys
  //         //           .map((filterName) => _buildDropdownFilter(filterName))
  //         //           .toList(),
  //         //     ),
  //         //      SizedBox(height: 12),
  //         //     Row(
  //         //       children: [
  //         //          Text("Menu Portions", style: TextStyle(fontSize: 12,color: Colors.black)),
  //         //          SizedBox(width: 8),
  //         //         IngredientTextField(
  //         //           hintText: "2",
  //         //           controller: recipeNameController,
  //         //           maxLines: 1,
  //         //           height: 70.h,
  //         //         ),
  //         //       ],
  //         //     ),
  //         //      SizedBox(height: 10.h),
  //         //     CustomButton(
  //         //       text: "Set Amount",
  //         //       onPressed: () {},
  //         //       width: 200.w,
  //         //       fontSize: 20.sp,
  //         //       color: AppColors.primaryColor,
  //         //       textColor: Colors.white,
  //         //       height: 30.h,
  //         //     ),
  //         //   ],
  //         // ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildItemList() {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(16),
        decoration: _cardDecoration(),
        child: ListView.separated(
          itemCount: items.length,
          separatorBuilder: (_, __) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final item = items[index];
            final isSelected = selectedIndex == index;

            return Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: isSelected
                    ? Border.all(color: Colors.blueAccent, width: 2)
                    : Border.all(color: Colors.transparent),
              ),
              child: ListTile(
                leading: ClipOval(
                  child: item['imageUrl'] != null
                      ? Image.network(
                    item['imageUrl']!,
                    width: 40,
                    height: 40,
                    fit: BoxFit.cover,
                  )
                      : Container(
                    width: 40,
                    height: 40,
                    color: Colors.grey[300],
                    child: const Icon(Icons.image_not_supported),
                  ),
                ),
                title: Text(item['name'] ?? 'Unnamed'),
                onTap: () {
                  setState(() {
                    selectedIndex = index;
                  });
                },
              ),
            );
          },
        ),
      ),
    );
  }

  // Widget _buildItemList() {
  //   return Expanded(
  //     child: Container(
  //       padding: const EdgeInsets.all(16),
  //       margin: const EdgeInsets.all(16),
  //       decoration: _cardDecoration(),
  //       child: ListView.separated(
  //         itemCount: items.length,
  //         separatorBuilder: (_, __) => const Divider(height: 1),
  //         itemBuilder: (_, index) {
  //           final item = items[index];
  //           return ListTile(
  //             leading: CircleAvatar(child: Text(item['image']!)),
  //             title: Text(item['name']!),
  //             onTap: () {},
  //           );
  //         },
  //       ),
  //     ),
  //   );
  // }

  Widget _buildRecipeHeader() {
    return Column(
      //mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomInputField(
          hintText: "Untitled",
          controller: recipeNameController,
          maxLines: 1,
        //  height: 60.h,
        ),
        SizedBox(height: 16.h),
        CustomInputField(
          hintText: "Description",
          controller: recipeDescController,
          maxLines: 5,
       //   height: 100.h,
        ),
      ],
    );
  }

  Widget _buildDropdownFilter(String filterKey) {
    final selected = _selectedFilters[filterKey]!;
    return PopupMenuButton<String>(
      color: Colors.white,
      onSelected: (value) {
        setState(() => _selectedFilters[filterKey] = value);
      },
      itemBuilder: (_) {
        return _filterOptions[filterKey]!.map((option) {
          final isSelected = option == selected;
          return PopupMenuItem(
            value: option,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
              decoration: isSelected
                  ? BoxDecoration(
                  color: Colors.red, borderRadius: BorderRadius.circular(6))
                  : null,
              child: Text(
                option,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black,
                ),
              ),
            ),
          );
        }).toList();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black38.withOpacity(.2)),
          color: AppColors.secondaryColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            Text(filterKey),
            const SizedBox(width: 5),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black38.withOpacity(.2)),
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(2),
              ),
              child: Icon(
                Icons.keyboard_arrow_down,
                size: 20.w,
                color: AppColors.secondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  BoxDecoration _cardDecoration() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: const [BoxShadow(blurRadius: 6, color: Colors.black26)],
    );
  }






}
