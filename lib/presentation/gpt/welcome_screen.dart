import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/utils/device_utils.dart';

import '../../app/imports/core_imports.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text('Meet Your Personal AI-Powered Kitchen Assistant',
                textAlign: TextAlign.center,
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: Colors.black,
                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 30 : 50.sp,
                  fontWeight: FontWeight.w700,
                )),
            SizedBox(height: 20.h),
            Text(
                'Simply type a recipe idea or some ingredients you have on hand and DishGen\'s AI will\ninstantly generate an all-new recipe on demand…',
                textAlign: TextAlign.center,
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: Colors.black,
                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 16 : 26.sp,
                  fontWeight: FontWeight.w400,
                )),
          ],
        ),
      ),
    );
  }
}