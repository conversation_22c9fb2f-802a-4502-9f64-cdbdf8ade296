// Helper method to convert HTML response to styled text widgets
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/utils/device_utils.dart';

import '../../../app/imports/core_imports.dart';

Widget buildHtmlText(String htmlString, BuildContext context) {
  if (htmlString.isEmpty) {
    return const SizedBox.shrink();
  }

  // Parse HTML and convert to styled text
  return _parseHtmlToWidget(htmlString,context);
}
Widget _parseHtmlToWidget(String htmlString, BuildContext context) {
  List<Widget> widgets = [];

  // Process HTML content sequentially to maintain original order
  _processHtmlSequentially(htmlString, widgets,context);

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: widgets,
  );
}

void _processHtmlSequentially(String html, List<Widget> widgets, BuildContext context) {
  // Split HTML into segments while preserving order
  RegExp htmlElementRegex = RegExp(
      r'(<p[^>]*>.*?</p>|<ul[^>]*>.*?</ul>|<ol[^>]*>.*?</ol>)',
      caseSensitive: false,
      dotAll: true);

  int lastEnd = 0;

  for (Match match in htmlElementRegex.allMatches(html)) {
    // Add any text before this element
    if (match.start > lastEnd) {
      String beforeText = html.substring(lastEnd, match.start).trim();
      if (beforeText.isNotEmpty) {
        String cleanText = Utils().cleanHtmlText(beforeText);
        if (cleanText.isNotEmpty) {
          widgets.add(
            Text(
              cleanText,
              style: context.theme.textTheme.bodyMedium!.copyWith(
                color: AppColors.primaryGreyColor,
                fontWeight: FontWeight.w400,
                fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 22.sp,
              ),
            ),
          );
          widgets.add(SizedBox(height: 8.h));
        }
      }
    }

    String element = match.group(0)!;

    // Process the matched element based on its type
    if (element.toLowerCase().startsWith('<p')) {
      _processParagraphElement(element, widgets,context);
    } else if (element.toLowerCase().startsWith('<ul')) {
      _processUnorderedListElement(element, widgets,context);
    } else if (element.toLowerCase().startsWith('<ol')) {
      _processOrderedListElement(element, widgets,context);
    }

    lastEnd = match.end;
  }

  // Add any remaining text after the last element
  if (lastEnd < html.length) {
    String remainingText = html.substring(lastEnd).trim();
    if (remainingText.isNotEmpty) {
      String cleanText = Utils().cleanHtmlText(remainingText);
      if (cleanText.isNotEmpty) {
        widgets.add(
          Text(
            cleanText,
            style: context.theme.textTheme.bodyMedium!.copyWith(
              color: AppColors.primaryGreyColor,
              fontWeight: FontWeight.w400,
              fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 22.sp,
            ),
          ),
        );
      }
    }
  }

  // Remove the last spacing if it exists
  if (widgets.isNotEmpty && widgets.last is SizedBox) {
    widgets.removeLast();
  }
}

void _processParagraphElement(String element, List<Widget> widgets, BuildContext context) {
  // Extract text from paragraph tags
  String text =
  element.replaceAll(RegExp(r'</?p[^>]*>', caseSensitive: false), '');
  String cleanText = Utils().cleanHtmlText(text);

  if (cleanText.trim().isNotEmpty) {
    widgets.add(
      Text(
        cleanText.trim(),
        style: context.theme.textTheme.bodyMedium!.copyWith(
          color: AppColors.primaryGreyColor,
          fontWeight: FontWeight.w400,
          fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 22.sp,
        ),
      ),
    );
    widgets.add(SizedBox(height: 8.h));
  }
}

void _processUnorderedListElement(String element, List<Widget> widgets, BuildContext context) {
  // Extract list items from ul element
  RegExp liRegex =
  RegExp(r'<li[^>]*>(.*?)</li>', caseSensitive: false, dotAll: true);

  for (Match liMatch in liRegex.allMatches(element)) {
    String itemText = liMatch.group(1) ?? '';
    itemText = Utils().cleanHtmlText(itemText);

    if (itemText.trim().isNotEmpty) {
      widgets.add(
        Padding(
          padding: EdgeInsets.only(left: 16.w, bottom: 4.h),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "• ",
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: AppColors.primaryGreyColor,
                  fontWeight: FontWeight.w400,
                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 22.sp,
                ),
              ),
              Expanded(
                child: Text(
                  itemText.trim(),
                  style: context.theme.textTheme.bodyMedium!.copyWith(
                    color: AppColors.primaryGreyColor,
                    fontWeight: FontWeight.w400,
                    fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 22.sp,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  // Add spacing after list
  widgets.add(SizedBox(height: 8.h));
}

void _processOrderedListElement(String element, List<Widget> widgets, BuildContext context) {
  // Extract list items from ol element
  RegExp liRegex =
  RegExp(r'<li[^>]*>(.*?)</li>', caseSensitive: false, dotAll: true);

  for (Match liMatch in liRegex.allMatches(element)) {
    String itemText = liMatch.group(1) ?? '';
    itemText = Utils().cleanHtmlText(itemText);

    if (itemText.trim().isNotEmpty) {
      widgets.add(
        Padding(
          padding: EdgeInsets.only(left: 16.w, bottom: 4.h),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "• ",
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: AppColors.primaryGreyColor,
                  fontWeight: FontWeight.w400,
                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 22.sp,
                ),
              ),
              Expanded(
                child: Text(
                  itemText.trim(),
                  style: context.theme.textTheme.bodyMedium!.copyWith(
                    color: AppColors.primaryGreyColor,
                    fontWeight: FontWeight.w400,
                    fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 22.sp,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  // Add spacing after list
  widgets.add(SizedBox(height: 8.h));
}
