import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/gpt/Widgets/thinking_loader.dart';

import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/get_ask_ai_thread_messages.dart';
import 'add_reciepe_dialog.dart';
import 'build_html_text.dart';

Widget buildMessagesList(List<UserMessage> messages, bool isAskAiLoading, ScrollController chatScrollController) {
  return ListView.builder(
    controller: chatScrollController,
    padding: const EdgeInsets.all(16),
    itemCount: (messages.length * 2) + (isAskAiLoading ? 1 : 0),
    // Add 1 for thinking loader
    itemBuilder: (context, index) {
      // Show thinking loader at the end when ask<PERSON><PERSON> is loading
      if (isAskAiLoading && index == (messages.length * 2)) {
        return Align(
          alignment: Alignment.centerLeft,
          child: Container(
            margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12.h),
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.7,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey[200]!),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomText(
                 text:  'Preparing Recipe',
                  size: DeviceUtils().isTabletOrIpad(context) ? 12 : 14.sp,
                  color: Colors.grey[700],
                  weight: FontWeight.w500,
                ),
                SizedBox(width: 8.w),
                const ThinkingLoader(
                  size: 20,
                  dotColor: Colors.blue,
                ),
              ],
            ),
          ),
        );
      }

      // Handle regular messages
      final messageIndex = index ~/ 2;
      final isPrompt = index.isEven;
      final message = messages[messageIndex];

      if (isPrompt) {
        // User prompt (right side)
        return Align(
          alignment: Alignment.centerRight,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.lightestGreyColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              message.prompt,
              style: context.theme.textTheme.bodyMedium!.copyWith(
                color: AppColors.primaryGreyColor,
                fontWeight: FontWeight.w400,
                fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 22.sp,
              ),
            ),
          ),
        );
      } else {
        // AI response (left side)
        return Align(
          alignment: Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildHtmlText(message.response,context),
                // Display image if available
                if (message.imageUrl != null &&
                    message.imageUrl!.isNotEmpty) ...[
                  SizedBox(height: 12.h),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      message.imageUrl!,
                      width: 450.w,
                      height: 400.h,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 450.w,
                          height: 400.h,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.broken_image,
                            color: Colors.grey,
                            size: 50,
                          ),
                        );
                      },
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          width: 450.w,
                          height: 400.h,
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            //child: CircularProgressIndicator(),
                            child: LoadingAnimationWidget.fallingDot(
                              color: Colors.black,
                              size: 50.0,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
                SizedBox(height: 8.h),
                // Action icons row
                Visibility(
                  visible: message.response.isNotEmpty,
                  child: GestureDetector(
                    onTap: () {
                      showDialog(
                        context: context,
                        useRootNavigator: true, //<--- add this
                        barrierDismissible: false,
                        builder: (_) => AddReciepeDialog(
                          recipeDetails: message.recipeDetails,
                        ),
                      );
                    },
                    child: SvgPicture.asset(
                      AssetsManager.download,
                      height: 40.h,
                      width: 40.w,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    },
  );
}