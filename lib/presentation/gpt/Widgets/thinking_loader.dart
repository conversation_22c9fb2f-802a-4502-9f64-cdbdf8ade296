// ThinkingLoader Widget
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/utils/device_utils.dart';

import '../../../app/imports/core_imports.dart';

class ThinkingLoader extends StatefulWidget {
  final double size;
  final Color dotColor;

  const ThinkingLoader({
    super.key,
    this.size = 40,
    this.dotColor = Colors.blue,
  });

  @override
  State<ThinkingLoader> createState() => _ThinkingLoaderState();
}

class _ThinkingLoaderState extends State<ThinkingLoader>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      3,
          (index) => AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.2, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    _startAnimation();
  }

  void _startAnimation() async {
    while (mounted) {
      for (int i = 0; i < _controllers.length; i++) {
        if (mounted) {
          _controllers[i].forward();
          await Future.delayed(const Duration(milliseconds: 150));
        }
      }
      await Future.delayed(const Duration(milliseconds: 300));
      for (int i = 0; i < _controllers.length; i++) {
        if (mounted) {
          _controllers[i].reverse();
        }
      }
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.size.h,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(3, (index) {
          return AnimatedBuilder(
            animation: _animations[index],
            builder: (context, child) {
              return Container(
                width: DeviceUtils().isTabletOrIpad(context) ? 8 : 8.w,
                height: DeviceUtils().isTabletOrIpad(context) ? 8 : 8.h,
                margin: EdgeInsets.symmetric(horizontal: 2.w),
                decoration: BoxDecoration(
                  color: widget.dotColor
                      .withValues(alpha: _animations[index].value),
                  shape: BoxShape.circle,
                ),
              );
            },
          );
        }),
      ),
    );
  }
}