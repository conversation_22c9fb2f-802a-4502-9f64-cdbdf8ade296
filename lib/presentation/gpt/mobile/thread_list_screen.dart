import 'package:flutter_svg/svg.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';

import '../../../app/imports/packages_imports.dart';
import '../../../core/data/models/get_ask_ai_response.dart';
import '../../../core/helpers/thread_grouper.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/gpt/ask_ai_notifier.dart';
import '../../../core/utils/Utils.dart';
import '../../../core/widgets/custom_searchbar.dart';
import '../../../core/widgets/no_data_widget.dart';
import '../../cookbook/widgets/custom_desc_text.dart';
import '../../shimer/thread_list_shimmer.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';

class ThreadListScreen extends ConsumerStatefulWidget {
  final Function(Thread)? onThreadSelected;

  const ThreadListScreen({super.key, this.onThreadSelected});

  @override
  ConsumerState<ThreadListScreen> createState() => _ThreadListScreenState();
}

class _ThreadListScreenState extends ConsumerState<ThreadListScreen> {
  int? selectedIndex;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final askAiState = ref.read(askAiNotifierProvider);
      if (askAiState.hasMore && askAiState.status != AppStatus.loadingMore) {
        ref.read(askAiNotifierProvider.notifier).getAskAiThreads(
              context: context,
              loadMore: true,
              search: _searchController.text.trim().isEmpty
                  ? null
                  : _searchController.text.trim(),
            );
      }
    }
  }

  void _onSearchChanged(String query) {
    if (mounted) {
      ref.read(askAiNotifierProvider.notifier).searchThreads(
            context: context,
            query: query,
          );
    }
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _isSearching = false;
    });
    if (mounted) {
      ref.read(askAiNotifierProvider.notifier).clearSearch(context: context);
    }
  }

  Future<void> _handleDeleteThread(Thread thread) async {
    final bool? confirmed = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Delete Thread',
      subtitle: 'Are you sure you want to delete "${thread.title}" thread?',
      confirmText: 'Delete',
      cancelText: 'Cancel',
    );

    if (confirmed != true || !mounted) {
      return;
    }

    await ref.read(askAiNotifierProvider.notifier).deleteThread(
      context: context,
      type: 'CUSTOM',
      threadIds: [thread.id],
    );

    if (mounted) {
      await ref.read(askAiNotifierProvider.notifier).getAskAiThreads(
            context: context,
            loadMore: false,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final askAiState = ref.watch(askAiNotifierProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: "GPT History",
        actions: [
          AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return ScaleTransition(scale: animation, child: child);
                },
                child: _isSearching
                    ? CustomSearchBar(
                        width: 220,
                        height: 54.h,
                        controller: _searchController,
                         onChanged: (value) {
                          setState(() {
                            _isSearching = value.isNotEmpty;
                          });
                          _onSearchChanged(value);
                        },
                        onClear: () {
                          setState(() {
                            _isSearching = false;
                          });
                        },
                      )
                    : IconButton(
                        icon: Icon(
                          Icons.search,
                          size: 24,
                          color: const Color.fromARGB(255, 130, 130, 130),
                        ),
                        onPressed: () {
                          setState(() {
                            _isSearching = true;
                          });
                        },
                        tooltip: 'Search Recipes',
                      ),
              ),
        ],
        // leading: IconButton(
        //   icon: const Icon(Icons.arrow_back),
        //   onPressed: () => Navigator.of(context).pop(),
        // ),
      ),
      body: Container(
        color: Colors.white,
        child: Column(
          children: [
            SizedBox(height: 20.h),
            Expanded(
              child: _buildThreadList(askAiState),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThreadList(AppState<List<Thread>> askAiState) {
    if (askAiState.status == AppStatus.loading &&
        (askAiState.data?.isEmpty ?? true)) {
      return const ThreadListShimmer(itemCount: 8);
    }

    if (askAiState.status == AppStatus.error) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Text(
            askAiState.errorMessage ?? 'Failed to load threads',
            style: TextStyle(
              color: Colors.red,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    if (askAiState.status == AppStatus.empty ||
        (askAiState.data?.isEmpty ?? true)) {
      return Center(
        child: NoDataWidget(
          title: "No threads found",
          subtitle:
              "Try searching again or select a different thread to view the details.",
          width: 250,
          height: 250,
        ),
      );
    }

    final threadSections =
        ThreadGrouper.groupThreadsByTimePeriod(askAiState.data!);
    final totalItemCount =
        ThreadGrouper.calculateTotalItemCount(threadSections);

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 10),
      itemCount: totalItemCount + (askAiState.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= totalItemCount) {
          return askAiState.status == AppStatus.loadingMore
              ? Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  child: Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: LoadingAnimationWidget.fallingDot(
                        color: Colors.black,
                        size: 50.0,
                      ),
                    ),
                  ),
                )
              : const SizedBox.shrink();
        }

        final item = ThreadGrouper.getItemAtIndex(threadSections, index);

        if (item is ThreadSection) {
          return _buildSectionHeader(item.title);
        } else if (item is Thread) {
          return _buildThreadItem(item, index);
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(top: 14.h, bottom: 12.h),
      child: Text(
        title,
        style: context.theme.textTheme.bodyMedium!.copyWith(
          color: AppColors.textGreyColor,
          fontWeight: FontWeight.w400,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildThreadItem(Thread thread, int index) {
    final isSelected = selectedIndex == index;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedIndex = index;
        });
        Navigator.of(context).pop(thread);
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 0, horizontal: 10),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color.fromARGB(255, 68, 68, 68).withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: Container(
                constraints: const BoxConstraints(minHeight: 10),
                padding: EdgeInsets.zero,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: Text(
                    thread.title.trim(),
                    style: context.theme.textTheme.bodyMedium!.copyWith(
                      color: AppColors.blackTextColor,
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                  ),
                ),
              ),
            ),
            // Visibility(
            //   visible:
            //       DeviceUtils().isTabletOrIpad(context) ? true : isSelected,
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_horiz,
                  color: AppColors.blackColor,
                  size: 20,
                ),
                onSelected: (value) async {
                  switch (value) {
                    case 'delete':
                      await _handleDeleteThread(thread);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem<String>(
                    value: 'delete',
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          AssetsManager.dlt_recipe,
                          height: 40.h,
                          width: 40.w,
                        ),
                        SizedBox(width: 15.w),
                        CustomDescText(
                          desc: "Delete",
                          textColor: AppColors.primaryColor,
                          size: 14,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
          //  ),
          ],
        ),
      ),
    );
  }
}
