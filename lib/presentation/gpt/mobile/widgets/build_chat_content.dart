import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/presentation/gpt/mobile/widgets/gpt_welcome_screen.dart';

import '../../../../app/imports/core_imports.dart';
import '../../../../core/data/models/get_ask_ai_thread_messages.dart';
import '../../../../core/network/app_status.dart';
import '../../../../core/providers/gpt/ask_ai_notifier.dart';
import '../../../../core/widgets/no_data_widget.dart';
import 'buildmessages_list.dart';

Widget buildChatContent(AppState<List<UserMessage>> threadMessagesState, ScrollController chatScrollController, WidgetRef ref, bool isNewChat, BuildContext context, bool isSearching) {
  // Show loading state, but not when searching
  if (threadMessagesState.status == AppStatus.loading && !isSearching) {
    return Center(
      child: LoadingAnimationWidget.fallingDot(
        color: Colors.black,
        size: 50.0,
      ),
    );
  }

  // if (threadMessagesState.data?.isEmpty ?? false) {
  //   return Expanded(
  //       child: Center(
  //         child: NoDataWidget(
  //           title: "No message found",
  //           subtitle:
  //           "Try searching again or select a different message to view the details.",
  //           width: 250,
  //           height: 250,
  //         ),
  //       ));
  // }

  if (threadMessagesState.data?.isEmpty ?? false) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom + 20.h), // Keyboard height + extra padding
      child: Center(
        child: SingleChildScrollView( // Make content scrollable when keyboard appears
          child: NoDataWidget(
            title: "No message found",
            subtitle: "Try searching again or select a different message to view the details.",
            width: 250,
            height: 250,
          ),
        ),
      ),
    );
  }

  // Show error state
  if (threadMessagesState.status == AppStatus.error) {
    return Center(
      child: Text(
        threadMessagesState.errorMessage ?? 'Failed to load messages',
        style: TextStyle(
          color: Colors.red,
          fontSize: 14,
        ),
      ),
    );
  }

  // Show empty state or welcome screen only when it's a new chat AND no messages exist AND not currently loading
  final askAiState = ref.watch(askAiNotifierProvider);
  final isAskAiLoading = askAiState.status == AppStatus.creating;

  if (isNewChat &&
      !isAskAiLoading &&
      (threadMessagesState.data?.isEmpty ?? true) &&
      threadMessagesState.status != AppStatus.loading) {
    return const GptWelcomeScreen();
  }

  // Show messages with search functionality
  final messages = threadMessagesState.data ?? [];

  return buildMessagesList(messages, isAskAiLoading,chatScrollController);
}
