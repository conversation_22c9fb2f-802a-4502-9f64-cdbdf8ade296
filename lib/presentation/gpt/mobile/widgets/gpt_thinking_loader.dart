// ThinkingLoader Widget
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';

class GptThinkingLoader extends StatefulWidget {
  final double size;
  final Color dotColor;

  const GptThinkingLoader({
    super.key,
    this.size = 40,
    this.dotColor = Colors.blue,
  });

  @override
  State<GptThinkingLoader> createState() => _GptThinkingLoaderState();
}

class _GptThinkingLoaderState extends State<GptThinkingLoader>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      3,
          (index) => AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.2, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    _startAnimation();
  }

  void _startAnimation() async {
    while (mounted) {
      for (int i = 0; i < _controllers.length; i++) {
        if (mounted) {
          _controllers[i].forward();
          await Future.delayed(const Duration(milliseconds: 150));
        }
      }
      await Future.delayed(const Duration(milliseconds: 300));
      for (int i = 0; i < _controllers.length; i++) {
        if (mounted) {
          _controllers[i].reverse();
        }
      }
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.size.h,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(3, (index) {
          return AnimatedBuilder(
            animation: _animations[index],
            builder: (context, child) {
              return Container(
                width: 8,
                height: 8,
                margin: EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  color: widget.dotColor
                      .withValues(alpha: _animations[index].value),
                  shape: BoxShape.circle,
                ),
              );
            },
          );
        }),
      ),
    );
  }
}