import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

class GptWelcomeScreen extends StatelessWidget {
  const GptWelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text('Meet Your Personal AI-Powered Kitchen Assistant',
                textAlign: TextAlign.center,
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: Colors.black,
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                )),
            SizedBox(height: 20.h),
            Text(
                'Simply type a recipe idea or some ingredients you have on hand and DishGen\'s AI will instantly generate an all-new recipe on demand…',
                textAlign: TextAlign.center,
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                )),
                SizedBox(height: 400.h),
          ],
        ),
      ),
    );
  }
}