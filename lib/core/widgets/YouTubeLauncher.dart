import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

class YouTubeLauncher extends StatelessWidget {
  final Uri _url = Uri.parse('https://www.youtube.com/watch?v=d1q4nwMUegA');

  Future<void> _launch() async {
    if (!await launchUrl(_url, mode: LaunchMode.externalApplication)) {
      throw 'Could not launch $_url';
    }
  }

  @override
  Widget build(BuildContext context) {
    // Workaround: force focus back to the Flutter side
    WidgetsBinding.instance.addPostFrameCallback((_) {
   //   FocusScope.of(context).requestFocus(FocusNode());
    });
    return GestureDetector(
      onTap: _launch,
      child: Container(
        height: 350.h,
        width: 600.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          image: DecorationImage(
            image: NetworkImage(
              'https://img.youtube.com/vi/d1q4nwMUegA/sddefault.jpg', // YouTube video thumbnail
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            Center(
              child: Icon(
                Icons.play_circle_fill,
                color: Colors.white.withOpacity(0.8),
                size: 100.sp,
              ),
            ),
          ],
        ),
      ),





  //     Container(
  //     height: 350.h,
  //     width: 600.w,
  //     child: ElevatedButton(
  //       onPressed: _launch,
  //       child: Text("Watch Video"),
  //     ),
     );
  }
}
