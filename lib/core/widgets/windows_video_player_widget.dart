import 'dart:io';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:video_player/video_player.dart';
import '../../app/imports/core_imports.dart';

/// Windows-optimized video player widget with better error handling and fallbacks
class WindowsVideoPlayerWidget extends StatefulWidget {
  final File? mediaFile;
  final String? mediaUrl;
  final String? thumbnailPath;
  final double height;
  final Map<String, String>? headers;

  const WindowsVideoPlayerWidget({
    super.key,
    this.mediaFile,
    this.mediaUrl,
    this.thumbnailPath,
    this.headers,
    this.height = 200.0,
  });

  @override
  State<WindowsVideoPlayerWidget> createState() => _WindowsVideoPlayerWidgetState();
}

class _WindowsVideoPlayerWidgetState extends State<WindowsVideoPlayerWidget> {
  VideoPlayerController? _controller;
  bool _hasError = false;
  String? _errorMessage;
  bool _isPlaying = false;
  bool _isInitializing = true;
  Duration _lastPosition = Duration.zero;

  @override
  void initState() {
    super.initState();
    _initVideoWithRetry();
  }

  Future<void> _initVideoWithRetry({int retryCount = 0}) async {
    const maxRetries = 3;
    
    try {
      setState(() {
        _isInitializing = true;
        _hasError = false;
        _errorMessage = null;
      });

      await _initVideo();
    } catch (e) {
      debugPrint("Video initialization attempt ${retryCount + 1} failed: $e");
      
      if (retryCount < maxRetries) {
        // Wait before retrying
        await Future.delayed(Duration(seconds: (retryCount + 1) * 2));
        return _initVideoWithRetry(retryCount: retryCount + 1);
      } else {
        // All retries failed
        setState(() {
          _hasError = true;
          _errorMessage = "Failed to load video after $maxRetries attempts: ${e.toString()}";
          _isInitializing = false;
        });
      }
    }
  }

  Future<void> _initVideo() async {
    if (widget.mediaFile != null) {
      _controller = VideoPlayerController.file(widget.mediaFile!);
      debugPrint("Windows: Initializing video from file: ${widget.mediaFile!.path}");
    } else if (widget.mediaUrl != null && widget.mediaUrl!.isNotEmpty) {
      // Windows-specific configuration
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(widget.mediaUrl!),
        httpHeaders: {
          'User-Agent': 'MasterCookAI-Windows/1.0',
          'Accept': 'video/*,*/*;q=0.8',
          'Accept-Encoding': 'identity',
          'Connection': 'keep-alive',
          ...?widget.headers,
        },
        videoPlayerOptions: VideoPlayerOptions(
          mixWithOthers: false, // Better for Windows
          allowBackgroundPlayback: false,
        ),
      );
      debugPrint("Windows: Initializing video from URL: ${widget.mediaUrl}");
    } else {
      throw Exception("No valid media source provided");
    }

    // Initialize with extended timeout for Windows
    await _controller!.initialize().timeout(
      const Duration(seconds: 15),
      onTimeout: () {
        throw Exception("Video initialization timeout (15s)");
      },
    );

    // Listen to controller state changes
    _controller!.addListener(_onControllerUpdate);
    
    setState(() {
      _isInitializing = false;
    });
    
    debugPrint("Windows: Video initialization completed successfully");
  }

  void _onControllerUpdate() {
    if (!mounted) return;
    final controller = _controller;
    if (controller == null || !controller.value.isInitialized) return;

    final newPlayingState = controller.value.isPlaying;
    final newPosition = controller.value.position;

    // Update only if playing state or position changes significantly
    if (newPlayingState != _isPlaying ||
        newPosition.inSeconds != _lastPosition.inSeconds) {
      setState(() {
        _isPlaying = newPlayingState;
        _lastPosition = newPosition;
      });
    }

    // Handle errors
    if (controller.value.hasError) {
      debugPrint("Windows: Video player error: ${controller.value.errorDescription}");
      setState(() {
        _hasError = true;
        _errorMessage = controller.value.errorDescription ?? "Unknown video error";
      });
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_onControllerUpdate);
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isInitializing) {
      return _buildLoadingWidget();
    }

    if (_hasError || _controller == null || !_controller!.value.isInitialized) {
      return _buildErrorWidget();
    }

    return _buildVideoPlayer();
  }

  Widget _buildLoadingWidget() {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          // Background thumbnail
          if (widget.thumbnailPath != null)
            CommonImage(
              imageSource: widget.thumbnailPath!,
              placeholder: AssetsManager.recipe_place_holder,
              fit: BoxFit.cover,
              width: double.infinity,
              height: widget.height,
            ),
          // Dark overlay
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          // Loading indicator
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
                SizedBox(height: 16),
                Text(
                  'Loading video...',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          // Background thumbnail
          if (widget.thumbnailPath != null)
            CommonImage(
              imageSource: widget.thumbnailPath!,
              placeholder: AssetsManager.recipe_place_holder,
              fit: BoxFit.cover,
              width: double.infinity,
              height: widget.height,
            ),
          // Dark overlay
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          // Error content
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, color: Colors.white, size: 48),
                  SizedBox(height: 16),
                  Text(
                    'Video Unavailable',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_errorMessage != null) ...[
                    SizedBox(height: 8),
                    Text(
                      _errorMessage!,
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => _initVideoWithRetry(),
                        icon: Icon(Icons.refresh, size: 18),
                        label: Text('Retry'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        ),
                      ),

                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return AspectRatio(
      aspectRatio: _controller!.value.aspectRatio,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            height: widget.height,
            child: VideoPlayer(_controller!),
          ),
          // Buffer loader
          if (_controller!.value.isBuffering)
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          // Play/Pause button
          Positioned(
            bottom: 40.h,
            child: IconButton(
              icon: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size: 40,
              ),
              onPressed: () {
                if (_controller!.value.isPlaying) {
                  _controller!.pause();
                } else {
                  _controller!.play();
                }
              },
            ),
          ),
          // Progress bar
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: VideoProgressIndicator(
              _controller!,
              allowScrubbing: true,
              colors: const VideoProgressColors(
                playedColor: Colors.blue,
                bufferedColor: Colors.blueGrey,
                backgroundColor: Colors.grey,
              ),
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
            ),
          ),
          // Video position and duration (matching standard video player)
          Positioned(
            bottom: 20.h,
            left: 8.w,
            child: Text(
              _formatDuration(_controller!.value.position),
              style: TextStyle(color: Colors.white, fontSize: 12.sp),
            ),
          ),
          Positioned(
            bottom: 20.h,
            right: 8.w,
            child: Text(
              _formatDuration(_controller!.value.duration),
              style: TextStyle(color: Colors.white, fontSize: 12.sp),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }
}
