// lib/core/widgets/banner_carousel.dart
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import '../../../app/imports/core_imports.dart';
import '../utils/device_utils.dart';
import 'common_image.dart';

class BannerCarouselMobile extends StatefulWidget {
  final List<String> bannerImages;
  final Function(int)? onImageClick;

  const BannerCarouselMobile({
    Key? key,
    required this.bannerImages,
    this.onImageClick,
  }) : super(key: key);

  @override
  State<BannerCarouselMobile> createState() => _BannerCarouselState();
}

class _BannerCarouselState extends State<BannerCarouselMobile> {
  int _activeIndex = 0;
  final CarouselController _controller = CarouselController();


  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CarouselSlider(
          options: CarouselOptions(
            autoPlay: true,
            autoPlayInterval: const Duration(seconds: 5),
            enlargeCenterPage: false, // Set to false to remove centering and take full width
            viewportFraction: 0.9,
            aspectRatio: 2.5,
            onPageChanged: (index, reason) {
              setState(() {
                _activeIndex = index;
              });
            },
          ),
          items: widget.bannerImages.asMap().entries.map((entry) {
            int index = entry.key;
            String item = entry.value;

            return Builder(
              builder: (BuildContext context) {
                return MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      widget.onImageClick?.call(index);
                    },
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      margin: const EdgeInsets.symmetric(horizontal: 5.0), // Remove horizontal margin
                      decoration: BoxDecoration(
                        color: AppColors.blackTextColor,
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2), // Corrected withOpacity
                            blurRadius: 6,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: CommonImage(
                          placeholder: AssetsManager.background_img,
                          fit: BoxFit.contain,
                          imageSource: item,
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: widget.bannerImages.asMap().entries.map((entry) {
            int index = entry.key;
            return Container(
              width: 8.0,
              height: 8.0,
              margin:
              const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _activeIndex == index
                    ? AppColors.primaryColor // Active dot color
                    : AppColors.greyBorderColor, // Inactive dot color
              ),
            );
          }).toList(),
        ),


      ],
    );
  }
}