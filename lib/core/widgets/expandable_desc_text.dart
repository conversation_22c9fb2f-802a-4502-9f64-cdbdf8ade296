import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../app/theme/colors.dart';

class ExpandableDescText extends StatefulWidget {
  final String desc;
  final Color textColor;
  final double size;
  final int maxLines; // Maximum lines to show when collapsed
  final int maxLength; // Optional character limit for truncation

  const ExpandableDescText({
    Key? key,
    required this.desc,
    required this.textColor,
    required this.size,
    this.maxLines = 3,
    this.maxLength = 150, // Default character limit
  }) : super(key: key);

  @override
  _ExpandableDescTextState createState() => _ExpandableDescTextState();
}

class _ExpandableDescTextState extends State<ExpandableDescText> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final isTextLong = widget.desc.length > widget.maxLength ||
        _isTextExceedingMaxLines(widget.desc, widget.maxLines, widget.size);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _isExpanded ? widget.desc : _getTruncatedText(widget.desc),
          style: TextStyle(
            color: widget.textColor,
            fontSize: widget.size,
          ),
          maxLines: _isExpanded ? null : widget.maxLines,
          overflow: _isExpanded ? null : TextOverflow.ellipsis,
        ),
        if (isTextLong)
          GestureDetector(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Text(
                _isExpanded ? 'Read Less' : 'Read More',
                style: TextStyle(
                  color: AppColors.primaryLightTextColor, // Use same color or customize
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ),
      ],
    );
  }

  // Check if text exceeds maxLines (approximation based on text width)
  bool _isTextExceedingMaxLines(String text, int maxLines, double fontSize) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(fontSize: fontSize),
      ),
      maxLines: maxLines,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 40.w); // Adjust for padding

    return textPainter.didExceedMaxLines;
  }

  // Truncate text to maxLength or maxLines
  String _getTruncatedText(String text) {
    if (text.length <= widget.maxLength) return text;
    return '${text.substring(0, widget.maxLength).trim()}...';
  }
}