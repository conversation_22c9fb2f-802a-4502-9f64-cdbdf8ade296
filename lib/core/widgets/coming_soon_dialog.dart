import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

class ComingSoonDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onClose;

  const ComingSoonDialog({
    super.key,
    this.title = 'Coming Soon',
    this.message = 'This feature is under development and will be available soon. Stay tuned!',
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      elevation: 8.0,
      backgroundColor: context.theme.cardColor,
      child: Container(
        padding: EdgeInsets.all(16.w),
        width: 300.w, // Adjustable width
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Close Button
            Align(
              alignment: Alignment.topRight,
              child: IconButton(
                icon: Icon(Icons.close, size: 20.sp, color: AppColors.primaryGreyColor),
                onPressed: () {
                  if (onClose != null) {
                    onClose!();
                  }
                  Navigator.of(context).pop();
                },
              ),
            ),
            SizedBox(height: 10.h),
            // Icon
            Icon(
              Icons.construction,
              size: 50.sp,
              color: Theme.of(context).primaryColor,
            ),
            SizedBox(height: 20.h),
            // Title
            Text(
              title,
              style: context.theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryGreyColor,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 10.h),
            // Message
            Text(
              message,
              style: context.theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.primaryGreyColor,
                fontSize: 16.sp,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20.h),
            // Optional Action Button (if needed)
            // Uncomment and customize if you want a button
            // ElevatedButton(
            //   onPressed: () {
            //     if (onClose != null) onClose!();
            //     Navigator.of(context).pop();
            //   },
            //   child: Text('OK', style: TextStyle(fontSize: 16.sp)),
            // ),
          ],
        ),
      ),
    );
  }

  // Static method to show the dialog
  static Future<void> showComingSoonDialog(
    BuildContext context, {
    String title = 'Coming Soon',
    String message = 'This feature is under development and will be available soon. Stay tuned!',
    VoidCallback? onClose,
  }) async {
    await showDialog(
      context: context,
      barrierDismissible: true, // Allows dismissal by tapping outside
      builder: (BuildContext context) => ComingSoonDialog(
        title: title,
        message: message,
        onClose: onClose,
      ),
    );
  }
}