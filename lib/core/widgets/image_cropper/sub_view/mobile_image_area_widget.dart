import 'dart:io';
import 'package:crop_image/crop_image.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'mobile_grid_overlay_widget.dart';

/// Widget that displays the main image cropping area for mobile
class MobileImageAreaWidget extends StatelessWidget {
  final File pickedImage;
  final CropController controller;
  final bool isImageValid;
  final String? errorMessage;
  final bool showGridLines;
  final double paddingSize;
  final bool alwaysMove;
  final double minimumImageSize;
  final double maximumImageSize;
  final Function(Rect) onCrop;

  const MobileImageAreaWidget({
    super.key,
    required this.pickedImage,
    required this.controller,
    required this.isImageValid,
    this.errorMessage,
    required this.showGridLines,
    required this.paddingSize,
    required this.alwaysMove,
    required this.minimumImageSize,
    required this.maximumImageSize,
    required this.onCrop,
  });

  @override
  Widget build(BuildContext context) {
    if (errorMessage != null) {
      return _buildErrorState(context);
    }

    if (!isImageValid) {
      return _buildLoadingState();
    }

    return _buildCropImageState();
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline,
              size: 64, color: AppColors.primaryColor),
          const SizedBox(height: 16),
          Text(
            errorMessage!,
            textAlign: TextAlign.center,
            style: const TextStyle(color: AppColors.primaryColor),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.primaryColor,
          ),
          SizedBox(height: 16),
          CustomText(
            text: 'Loading image...',
            size: 10,
          ),
        ],
      ),
    );
  }

  Widget _buildCropImageState() {
    return Padding(
      padding: const EdgeInsets.all(0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          children: [
            SizedBox.expand(
              child: CropImage(
                controller: controller,
                image: Image.file(pickedImage),
                paddingSize: paddingSize,
                alwaysMove: alwaysMove,
                minimumImageSize: minimumImageSize,
                maximumImageSize: maximumImageSize,
                onCrop: onCrop,
              ),
            ),
            // Grid overlay - always visible on mobile
            if (showGridLines) const MobileGridOverlayWidget(),
          ],
        ),
      ),
    );
  }
}
