import 'package:mastercookai/app/imports/core_imports.dart';

/// Widget that displays the top control buttons for mobile image cropping
/// Includes zoom in/out, rotate left/right, and reset functionality
class MobileTopControlsWidget extends StatelessWidget {
  final VoidCallback onZoomIn;
  final VoidCallback onZoomOut;
  final VoidCallback onRotateLeft;
  final VoidCallback onRotateRight;
  final VoidCallback onReset;

  const MobileTopControlsWidget({
    super.key,
    required this.onZoomIn,
    required this.onZoomOut,
    required this.onRotateLeft,
    required this.onRotateRight,
    required this.onReset,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildTopControlButton(
            icon: Icons.zoom_in,
            label: 'Zoom In',
            onPressed: onZoomIn,
          ),
          _buildTopControlButton(
            icon: Icons.zoom_out,
            label: 'Zoom Out',
            onPressed: onZoomOut,
          ),
          _buildTopControlButton(
            icon: Icons.rotate_left,
            label: 'Rotate Left',
            onPressed: onRotateLeft,
          ),
          _buildTopControlButton(
            icon: Icons.rotate_right,
            label: 'Rotate Right',
            onPressed: onRotateRight,
          ),
          _buildTopControlButton(
            icon: Icons.refresh,
            label: 'Reset',
            onPressed: onReset,
          ),
        ],
      ),
    );
  }

  Widget _buildTopControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, size: 20, color: Colors.black),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}
