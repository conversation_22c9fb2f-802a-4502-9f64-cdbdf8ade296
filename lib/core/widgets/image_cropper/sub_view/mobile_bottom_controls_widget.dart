import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../data/models/crop_preset.dart';
import 'mobile_crop_presets_widget.dart';
import 'mobile_action_buttons_widget.dart';

/// Widget that displays the fixed bottom controls for mobile image cropping
/// Includes crop presets and action buttons
class MobileBottomControlsWidget extends StatelessWidget {
  final List<CropPreset> cropPresets;
  final double? currentAspectRatio;
  final Function(CropPreset) onPresetSelected;
  final VoidCallback onCropImage;
  final VoidCallback onKeepOriginal;

  const MobileBottomControlsWidget({
    super.key,
    required this.cropPresets,
    required this.currentAspectRatio,
    required this.onPresetSelected,
    required this.onCropImage,
    required this.onKeepOriginal,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Crop Ratios section
              MobileCropPresetsWidget(
                cropPresets: cropPresets,
                currentAspectRatio: currentAspectRatio,
                onPresetSelected: onPresetSelected,
              ),
              const SizedBox(height: 5),
              // Action buttons
              MobileActionButtonsWidget(
                onCropImage: onCropImage,
                onKeepOriginal: onKeepOriginal,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
