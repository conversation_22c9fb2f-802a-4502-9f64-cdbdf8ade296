import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../data/models/crop_preset.dart';

/// Widget that displays the crop ratio presets in a grid layout for mobile
class MobileCropPresetsWidget extends StatelessWidget {
  final List<CropPreset> cropPresets;
  final double? currentAspectRatio;
  final Function(CropPreset) onPresetSelected;

  const MobileCropPresetsWidget({
    super.key,
    required this.cropPresets,
    required this.currentAspectRatio,
    required this.onPresetSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Crop Ratios',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 2),
        // Fixed height for crop presets grid
        SizedBox(
          height: 210, // Fixed height to accommodate 2 rows of presets
          child: _buildCropPresetsGrid(),
        ),
      ],
    );
  }

  Widget _buildCropPresetsGrid() {
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(), // Disable scrolling
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        mainAxisSpacing: 12,
        crossAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: cropPresets.length,
      itemBuilder: (context, index) {
        final preset = cropPresets[index];
        final isSelected = _isPresetSelected(preset);

        return GestureDetector(
          onTap: () => onPresetSelected(preset),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? AppColors.primaryColor : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primaryColor : Colors.transparent,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: isSelected ? AppColors.primaryColor : Colors.grey.shade400,
                      width: 1,
                    ),
                  ),
                  child: preset.aspectRatio == null
                      ? Icon(
                          Icons.crop_free,
                          size: 16,
                          color: isSelected ? Colors.white : Colors.grey.shade600,
                        )
                      : _buildCropRatioIcon(preset.aspectRatio!, isSelected),
                ),
                const SizedBox(height: 4),
                Text(
                  preset.name,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? AppColors.primaryColor : Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCropRatioIcon(double aspectRatio, bool isSelected) {
    // Create a visual representation of the aspect ratio
    double width, height;
    if (aspectRatio >= 1) {
      width = 12;
      height = 12 / aspectRatio;
    } else {
      width = 12 * aspectRatio;
      height = 12;
    }

    return Center(
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.grey.shade600,
          borderRadius: BorderRadius.circular(1),
        ),
      ),
    );
  }

  bool _isPresetSelected(CropPreset preset) {
    if (preset.aspectRatio == null && currentAspectRatio == null) {
      return true;
    }
    if (preset.aspectRatio != null && currentAspectRatio != null) {
      return (preset.aspectRatio! - currentAspectRatio!).abs() < 0.001;
    }
    return false;
  }
}
