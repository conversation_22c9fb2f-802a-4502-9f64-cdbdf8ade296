// import 'dart:io';
// import 'dart:ui' as ui;
// import 'package:flutter/material.dart';
// import 'package:crop_image/crop_image.dart';
// import 'package:mastercookai/app/imports/core_imports.dart';
// import 'package:mastercookai/core/widgets/custom_appbar.dart';
// import 'package:mastercookai/core/widgets/custom_text.dart';

// import '../../../app/assets_manager.dart';
// import '../../../app/imports/packages_imports.dart';
// import '../../data/models/crop_preset.dart';
// import '../../utils/device_utils.dart';
// import '../../utils/image_compression_utils.dart';
// import 'image_cropper_mobile.dart';

// // Custom painter for grid overlay
// class GridPainter extends CustomPainter {
//   @override
//   void paint(Canvas canvas, Size size) {
//     final paint = Paint()
//       ..color = Colors.white.withOpacity(0.5)
//       ..strokeWidth = 1.0;

//     // Draw vertical lines (rule of thirds)
//     final verticalSpacing = size.width / 3;
//     for (int i = 1; i < 3; i++) {
//       final x = verticalSpacing * i;
//       canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
//     }

//     // Draw horizontal lines (rule of thirds)
//     final horizontalSpacing = size.height / 3;
//     for (int i = 1; i < 3; i++) {
//       final y = horizontalSpacing * i;
//       canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
//     }
//   }

//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
// }

// class ImageCropper extends StatefulWidget {
//   final double? initialAspectRatio;
//   final Rect initialCrop;
//   final double paddingSize;
//   final bool alwaysMove;
//   final double minimumImageSize;
//   final double maximumImageSize;
//   final Function(File)? onImageCropped;
//   final File pickedImage;
//   final bool showCropPresets;
//   final bool showGridLines;
//   final bool useDelegate;
//   final bool enableFreeformCrop;

//   const ImageCropper({
//     super.key,
//     this.initialAspectRatio,
//     this.initialCrop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9),
//     this.paddingSize = 25.0,
//     this.alwaysMove = true,
//     this.minimumImageSize = 500,
//     this.maximumImageSize = 500,
//     this.onImageCropped,
//     required this.pickedImage,
//     this.showCropPresets = true,
//     this.showGridLines = false,
//     this.useDelegate = false,
//     this.enableFreeformCrop = true,
//    });

//   @override
//   State<ImageCropper> createState() => _ImageCropperState();
// }

// class _ImageCropperState extends State<ImageCropper> {
//   final CropController controller = CropController();
//   bool _isImageValid = false;
//   String? _errorMessage;
//   bool _showGridLines = false;
//   double? _currentAspectRatio;

//   final List<CropPreset> _cropPresets = [
//     CropPreset(name: 'Free', aspectRatio: null, icon: Icons.crop_free),
//     CropPreset(name: 'Square', aspectRatio: 1.0, icon: Icons.crop_square),
//     CropPreset(name: '4:3', aspectRatio: 4.0 / 3.0, icon: Icons.crop_3_2),
//     CropPreset(name: '16:9', aspectRatio: 16.0 / 9.0, icon: Icons.crop_16_9),
//     CropPreset(name: '3:4', aspectRatio: 3.0 / 4.0, icon: Icons.crop_portrait),
//     CropPreset(
//         name: '9:16', aspectRatio: 9.0 / 16.0, icon: Icons.crop_portrait),
//   ];

//   @override
//   void initState() {
//     super.initState();
//     _currentAspectRatio = widget.initialAspectRatio;
//     _showGridLines = widget.showGridLines;
//     _validateImage();
//   }

//   Future<void> _validateImage() async {
//     if (widget.pickedImage.path.isEmpty || !await widget.pickedImage.exists()) {
//       setState(() {
//         _errorMessage = 'Provided image file does not exist';
//       });
//       return;
//     }
//     try {
//       final bytes = await widget.pickedImage.readAsBytes();
//       final codec = await ui.instantiateImageCodec(bytes);
//       final frameInfo = await codec.getNextFrame();
//       final image = frameInfo.image;
//       if (image.width > 0 && image.height > 0) {
//         setState(() {
//           _isImageValid = true;
//         });
//         WidgetsBinding.instance.addPostFrameCallback((_) {
//           if (mounted) {
//             try {
//               controller.aspectRatio = _currentAspectRatio;
//               controller.crop = widget.initialCrop;
//               controller.rotation = CropRotation.up; // Ensure initial rotation
//             } catch (e) {
//               debugPrint('Error setting initial controller values: $e');
//               Future.delayed(const Duration(milliseconds: 100), () {
//                 if (mounted) {
//                   try {
//                     controller.aspectRatio = _currentAspectRatio;
//                     controller.crop = widget.initialCrop;
//                     controller.rotation = CropRotation.up;
//                   } catch (e) {
//                     debugPrint('Fallback error setting controller values: $e');
//                     controller.aspectRatio = null;
//                     controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
//                     controller.rotation = CropRotation.up;
//                   }
//                 }
//               });
//             }
//           }
//         });
//       }
//     } catch (e) {
//       setState(() {
//         _errorMessage = 'Invalid image data: $e';
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return getDeviceType(context).name == 'mobile'
//         ? ImageCropperMobile(
//             pickedImage: widget.pickedImage,
//             showCropPresets: true,
//             showGridLines: false,
//             useDelegate: true,
//             enableFreeformCrop: true,
//              onImageCropped: widget.onImageCropped)
//         : Scaffold(
//             appBar: CustomAppBar(title: 'Crop Image'),
//             body: LayoutBuilder(
//               builder: (context, constraints) {
//                 final isTablet = getDeviceType(context) == DeviceType.tablet;
//                 return isTablet
//                     ? Column(
//                         children: [
//                           Expanded(flex: 7, child: _buildBody(constraints)),
//                           Expanded(
//                               flex: 3,
//                               child: _buildBottomControls(constraints)),
//                         ],
//                       )
//                     : Row(
//                         children: [
//                           Expanded(
//                               flex: 3,
//                               child: _buildBottomControls(constraints)),
//                           Expanded(flex: 7, child: _buildBody(constraints)),
//                         ],
//                       );
//               },
//             ),
//           );
//   }

//   Widget _buildBody(BoxConstraints constraints) {
//     if (_errorMessage != null) {
//       return Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             const Icon(Icons.error_outline, size: 64, color: Colors.red),
//             const SizedBox(height: 16),
//             Text(
//               _errorMessage!,
//               textAlign: TextAlign.center,
//               style: const TextStyle(color: Colors.red),
//             ),
//             const SizedBox(height: 16),
//             ElevatedButton(
//               onPressed: () => Navigator.of(context).pop(),
//               child: const Text('Go Back'),
//             ),
//           ],
//         ),
//       );
//     }

//     if (!_isImageValid) {
//       return const Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             CircularProgressIndicator(),
//             SizedBox(height: 16),
//             Text('Loading image...'),
//           ],
//         ),
//       );
//     }

//     final isTablet = getDeviceType(context) == DeviceType.tablet;
//     final maxWidth = constraints.maxWidth;
//     final maxHeight = constraints.maxHeight;
//     final cropAreaWidth = isTablet ? maxWidth * 0.9 : maxWidth * 0.8;
//     final cropAreaHeight = isTablet ? maxHeight * 0.6 : maxHeight * 0.7;
//     final horizontalPadding = isTablet ? maxWidth * 0.05 : maxWidth * 0.1;

//     return Stack(
//       children: [
//         Image.asset(
//           height: maxHeight,
//           AssetsManager.background_img,
//           fit: BoxFit.cover,
//         ),
//         Center(
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Container(
//                 margin: EdgeInsets.symmetric(horizontal: horizontalPadding),
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.circular(8),
//                 ),
//                 width: cropAreaWidth,
//                 height: cropAreaHeight,
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     SizedBox(
//                       width: cropAreaWidth,
//                       height: cropAreaHeight * 0.85,
//                       child: CropImage(
//                         controller: controller,
//                         image: Image.file(widget.pickedImage),
//                         paddingSize: widget.paddingSize,
//                         alwaysMove: widget.alwaysMove,
//                         minimumImageSize: widget.minimumImageSize,
//                         maximumImageSize: widget.maximumImageSize,
//                         onCrop: (rect) {
//                           controller.crop = rect;
//                         },
//                       ),
//                     ),
//                     SizedBox(height: cropAreaHeight * 0.02),
//                     _buildActionButtons(cropAreaWidth),
//                   ],
//                 ),
//               ),
//               SizedBox(height: maxHeight * 0.02),
//               SizedBox(
//                 width: cropAreaWidth * 0.5,
//                 child: ElevatedButton.icon(
//                   onPressed: _finished,
//                   icon: const Icon(Icons.crop, size: 20),
//                   label: const Text('Crop Image'),
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: Theme.of(context).primaryColor,
//                     foregroundColor: Colors.white,
//                     padding: const EdgeInsets.symmetric(vertical: 14),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(8),
//                     ),
//                   ),
//                 ),
//               ),
//               SizedBox(height: maxHeight * 0.03),
//             ],
//           ),
//         ),
//         if (_showGridLines) _buildGridOverlay(),
//       ],
//     );
//   }

//   Widget _buildGridOverlay() {
//     return Positioned.fill(
//       child: IgnorePointer(
//         child: CustomPaint(
//           painter: GridPainter(),
//         ),
//       ),
//     );
//   }

//   Widget _buildBottomControls(BoxConstraints constraints) {
//     final isTablet = getDeviceType(context) == DeviceType.tablet;
//     final maxWidth = constraints.maxWidth;
//     final maxHeight = constraints.maxHeight;
//     final padding = isTablet ? maxWidth * 0.02 : 16.0;

//     return Container(
//       padding: EdgeInsets.all(padding),
//       decoration: BoxDecoration(
//         color: Theme.of(context).scaffoldBackgroundColor,
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.1),
//             blurRadius: 4,
//             offset: isTablet ? const Offset(0, -2) : const Offset(-2, 0),
//           ),
//         ],
//       ),
//       // Wrap the Column with a SingleChildScrollView to prevent overflow
//       child: SingleChildScrollView(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.start,
//           children: [
//             if (widget.showCropPresets) _buildCropPresets(maxWidth, maxHeight),
//             SizedBox(height: 10),
//             ElevatedButton.icon(
//               onPressed: _showHelpDialog,
//               icon: const Icon(Icons.help_outline, size: 20),
//               label: const Text('Help'),
//               style: ElevatedButton.styleFrom(
//                 backgroundColor: Colors.grey[300],
//                 foregroundColor: Colors.black,
//                 padding: EdgeInsets.symmetric(
//                     vertical: 14,
//                     horizontal: (getDeviceType(context) == DeviceType.tablet)
//                         ? 100
//                         : 0),
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(8),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildCropPresets(double maxWidth, double maxHeight) {
//     final isTablet = getDeviceType(context) == DeviceType.tablet;
//     final gridHeight = isTablet ? maxHeight * 0.5 : 400.0;
//     final crossAxisCount = isTablet ? 6 : 3;

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Crop Ratios',
//           style: Theme.of(context).textTheme.titleSmall?.copyWith(
//                 fontWeight: FontWeight.w600,
//               ),
//         ),
//         SizedBox(height: 10),
//         SizedBox(
//           //  height: gridHeight,
//           child: GridView.builder(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//               crossAxisCount: crossAxisCount,
//               mainAxisSpacing: isTablet ? 16 : 32,
//               crossAxisSpacing: isTablet ? 16 : 32,
//               childAspectRatio: 1,
//             ),
//             itemCount: _cropPresets.length,
//             itemBuilder: (context, index) {
//               final preset = _cropPresets[index];
//               final isSelected = _isPresetSelected(preset);

//               return GestureDetector(
//                 onTap: () => _selectCropPreset(preset),
//                 child: Container(
//                   decoration: BoxDecoration(
//                     color: isSelected
//                         ? Theme.of(context).primaryColor.withOpacity(0.3)
//                         : Colors.grey.withOpacity(0.25),
//                     borderRadius: BorderRadius.circular(8),
//                     border: Border.all(
//                       color: isSelected
//                           ? Theme.of(context).primaryColor
//                           : Colors.grey.withOpacity(0.8),
//                       width: isSelected ? 2 : 1,
//                     ),
//                   ),
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       Icon(
//                         preset.icon,
//                         color: isSelected
//                             ? Theme.of(context).primaryColor
//                             : Colors.grey[600],
//                         size: 20,
//                       ),
//                       const SizedBox(height: 4),
//                       Text(
//                         preset.name,
//                         style: TextStyle(
//                           fontSize: 10,
//                           color: isSelected
//                               ? Theme.of(context).primaryColor
//                               : Colors.grey[600],
//                           fontWeight:
//                               isSelected ? FontWeight.w600 : FontWeight.normal,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               );
//             },
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildActionButtons(double cropAreaWidth) {
//     final buttonPadding = cropAreaWidth * 0.05;

//     return Padding(
//       padding: EdgeInsets.symmetric(horizontal: buttonPadding),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//         children: [
//           _buildActionButton(
//             icon: Icons.zoom_in,
//             label: 'Zoom In',
//             onPressed: _zoomIn,
//           ),
//           _buildActionButton(
//             icon: Icons.zoom_out,
//             label: 'Zoom Out',
//             onPressed: _zoomOut,
//           ),
//           _buildActionButton(
//             icon: Icons.rotate_90_degrees_ccw_outlined,
//             label: 'Rotate Left',
//             onPressed: _rotateLeft,
//           ),
//           _buildActionButton(
//             icon: Icons.rotate_90_degrees_cw_outlined,
//             label: 'Rotate Right',
//             onPressed: _rotateRight,
//           ),
//           _buildActionButton(
//             icon: Icons.refresh,
//             label: 'Reset',
//             onPressed: _resetCrop,
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildActionButton({
//     required IconData icon,
//     required String label,
//     required VoidCallback onPressed,
//   }) {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         IconButton(
//           onPressed: onPressed,
//           icon: Icon(icon),
//           style: IconButton.styleFrom(
//             backgroundColor: Colors.grey.withOpacity(0.1),
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(8),
//             ),
//           ),
//         ),
//         const SizedBox(height: 4),
//         CustomText(
//           text: label,
//           size: 14,
//           align: TextAlign.center,
//         ),
//       ],
//     );
//   }

//   bool _isPresetSelected(CropPreset preset) {
//     if (preset.aspectRatio == null && _currentAspectRatio == null) {
//       return true;
//     }
//     if (preset.aspectRatio != null && _currentAspectRatio != null) {
//       return (preset.aspectRatio! - _currentAspectRatio!).abs() < 0.001;
//     }
//     return false;
//   }

//   void _selectCropPreset(CropPreset preset) {
//     setState(() {
//       _currentAspectRatio = preset.aspectRatio;
//     });
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       if (mounted && _isImageValid) {
//         try {
//           controller.aspectRatio = preset.aspectRatio;
//           controller.crop = widget.initialCrop;
//         } catch (e) {
//           ScaffoldMessenger.of(context).showSnackBar(
//             SnackBar(content: Text('Error applying crop preset: $e')),
//           );
//         }
//       }
//     });
//   }

//   void _resetCrop() {
//     if (!_isImageValid) return;
//     try {
//       controller.rotation = CropRotation.up;
//       controller.crop = widget.initialCrop;
//       controller.aspectRatio = _currentAspectRatio;
//       setState(() {
//         _showGridLines = widget.showGridLines; // Reset grid lines
//       });
//     } catch (e) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text('Error resetting crop: $e')),
//       );
//     }
//   }

//   void _zoomIn() {
//     if (!_isImageValid) return;
//     try {
//       final currentCrop = controller.crop;
//       final zoomFactor = 0.9;
//       final centerX = (currentCrop.left + currentCrop.right) / 2;
//       final centerY = (currentCrop.top + currentCrop.bottom) / 2;
//       final newWidth = (currentCrop.width * zoomFactor).clamp(0.1, 1.0);
//       final newHeight = (currentCrop.height * zoomFactor).clamp(0.1, 1.0);
//       final newLeft = (centerX - newWidth / 2).clamp(0.0, 1.0 - newWidth);
//       final newTop = (centerY - newHeight / 2).clamp(0.0, 1.0 - newHeight);
//       controller.crop = Rect.fromLTWH(newLeft, newTop, newWidth, newHeight);
//     } catch (e) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text('Error zooming in: $e')),
//       );
//     }
//   }

//   void _zoomOut() {
//     if (!_isImageValid) return;
//     try {
//       final currentCrop = controller.crop;
//       final zoomFactor = 1.1;
//       final centerX = (currentCrop.left + currentCrop.right) / 2;
//       final centerY = (currentCrop.top + currentCrop.bottom) / 2;
//       final newWidth = (currentCrop.width * zoomFactor).clamp(0.1, 1.0);
//       final newHeight = (currentCrop.height * zoomFactor).clamp(0.1, 1.0);
//       final newLeft = (centerX - newWidth / 2).clamp(0.0, 1.0 - newWidth);
//       final newTop = (centerY - newHeight / 2).clamp(0.0, 1.0 - newHeight);
//       controller.crop = Rect.fromLTWH(newLeft, newTop, newWidth, newHeight);
//     } catch (e) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text('Error zooming out: $e')),
//       );
//     }
//   }

//   void _showHelpDialog() {
//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: CustomText(
//           text: 'How to Crop',
//           size: 18,
//           weight: FontWeight.w600,
//           color: AppColors.primaryLightTextColor,
//         ),
//         content: const Column(
//           mainAxisSize: MainAxisSize.min,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             CustomText(
//               text: '• Drag to move the image',
//               size: 16,
//               weight: FontWeight.w400,
//               color: AppColors.primaryLightTextColor,
//             ),
//             CustomText(
//               text: '• Pinch to zoom in/out',
//               size: 16,
//               weight: FontWeight.w400,
//               color: AppColors.primaryLightTextColor,
//             ),
//             CustomText(
//               text: '• Use crop ratios for specific dimensions',
//               size: 16,
//               weight: FontWeight.w400,
//               color: AppColors.primaryLightTextColor,
//             ),
//             CustomText(
//               text: '• Rotate using the rotation buttons',
//               size: 16,
//               weight: FontWeight.w400,
//               color: AppColors.primaryLightTextColor,
//             ),
//             CustomText(
//               text: '• Reset to start over',
//               size: 16,
//               weight: FontWeight.w400,
//               color: AppColors.primaryLightTextColor,
//             ),
//           ],
//         ),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: const CustomText(
//               text: 'Got it',
//               size: 22,
//               weight: FontWeight.w800,
//               color: AppColors.primaryLightTextColor,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Future<void> _rotateLeft() async {
//     if (!_isImageValid) return;
//     try {
//       controller.rotateLeft();
//       setState(() {}); // Force UI refresh
//     } catch (e) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text('Error rotating left: $e')),
//       );
//     }
//   }

//   Future<void> _rotateRight() async {
//     if (!_isImageValid) return;
//     try {
//       controller.rotateRight();
//       setState(() {}); // Force UI refresh
//     } catch (e) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text('Error rotating right: $e')),
//       );
//     }
//   }

//   Future<void> _finished() async {
//     if (!mounted || !_isImageValid) return;

//     try {
//       // Get the original image bytes
//       final originalBytes = await widget.pickedImage.readAsBytes();
//       final codec = await ui.instantiateImageCodec(originalBytes);
//       final frameInfo = await codec.getNextFrame();
//       final ui.Image originalImage = frameInfo.image;

//       // Get the crop rectangle and rotation
//       final cropRect = controller.crop;
//       final rotation = controller.rotation;
//       final imageWidth = originalImage.width.toDouble();
//       final imageHeight = originalImage.height.toDouble();

//       // Adjust crop rectangle based on rotation
//       Rect pixelRect;
//       double outputWidth, outputHeight;
//       switch (rotation) {
//         case CropRotation.up:
//         case CropRotation.down:
//           pixelRect = Rect.fromLTRB(
//             cropRect.left * imageWidth,
//             cropRect.top * imageHeight,
//             cropRect.right * imageWidth,
//             cropRect.bottom * imageHeight,
//           );
//           outputWidth = pixelRect.width;
//           outputHeight = pixelRect.height;
//           break;
//         case CropRotation.left:
//         case CropRotation.right:
//           // Swap width and height for 90/270 degree rotations
//           pixelRect = Rect.fromLTRB(
//             cropRect.left * imageHeight,
//             cropRect.top * imageWidth,
//             cropRect.right * imageHeight,
//             cropRect.bottom * imageWidth,
//           );
//           outputWidth = pixelRect.height;
//           outputHeight = pixelRect.width;
//           break;
//       }

//       // Create a new picture with the correct dimensions
//       final recorder = ui.PictureRecorder();
//       final canvas = Canvas(recorder);

//       // Apply rotation transformation
//       canvas.save();
//       switch (rotation) {
//         case CropRotation.up:
//           // No rotation needed
//           break;
//         case CropRotation.right:
//           canvas.translate(outputWidth, 0);
//           canvas.rotate(90 * 3.141592653589793 / 180);
//           break;
//         case CropRotation.down:
//           canvas.translate(outputWidth, outputHeight);
//           canvas.rotate(180 * 3.141592653589793 / 180);
//           break;
//         case CropRotation.left:
//           canvas.translate(0, outputHeight);
//           canvas.rotate(270 * 3.141592653589793 / 180);
//           break;
//       }

//       // Draw the cropped portion
//       canvas.drawImageRect(
//         originalImage,
//         pixelRect,
//         Rect.fromLTWH(0, 0, outputWidth, outputHeight),
//         Paint(),
//       );
//       canvas.restore();

//       final picture = recorder.endRecording();
//       final croppedImage = await picture.toImage(
//         outputWidth.toInt(),
//         outputHeight.toInt(),
//       );

//       // Compress image using the new utility
//       final tempFile = await ImageCompressionUtils.compressImageToSize(
//         croppedImage,
//         maxSizeKB: 250,
//         customFileName: 'cropped_author_image_${DateTime.now().millisecondsSinceEpoch}.jpg',
//       );

//       // Log file size
//       final fileSizeKB = (await tempFile.length()) / 1024;
//       debugPrint('Cropped image size: ${fileSizeKB.toStringAsFixed(1)} KB');
//       debugPrint('useDelegate: ${widget.useDelegate}');

//       if (mounted) {
//         if (widget.useDelegate) {
//           widget.onImageCropped?.call(tempFile);
//           Navigator.pop(context, true);
//         } else {
//           Navigator.pop(context, tempFile);
//         }
//       }
//     } catch (e) {
//        debugPrint('e $e');
//     }
//   }


// }

import 'dart:io';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:crop_image/crop_image.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

import '../../../app/assets_manager.dart';
import '../../../app/imports/packages_imports.dart';
import '../../data/models/crop_preset.dart';
import '../../utils/device_utils.dart';
import '../../utils/image_compression_utils.dart';
import 'image_cropper_mobile.dart';

// Custom painter for grid overlay
class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.5)
      ..strokeWidth = 1.0;

    // Draw vertical lines (rule of thirds)
    final verticalSpacing = size.width / 3;
    for (int i = 1; i < 3; i++) {
      final x = verticalSpacing * i;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines (rule of thirds)
    final horizontalSpacing = size.height / 3;
    for (int i = 1; i < 3; i++) {
      final y = horizontalSpacing * i;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class ImageCropper extends StatefulWidget {
  final double? initialAspectRatio;
  final Rect initialCrop;
  final double paddingSize;
  final bool alwaysMove;
  final double minimumImageSize;
  final double maximumImageSize;
  final Function(File)? onImageCropped;
  final File pickedImage;
  final bool showCropPresets;
  final bool showGridLines;
  final bool useDelegate;
  final bool enableFreeformCrop;

  const ImageCropper({
    super.key,
    this.initialAspectRatio,
    this.initialCrop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9),
    this.paddingSize = 25.0,
    this.alwaysMove = true,
    this.minimumImageSize = 500,
    this.maximumImageSize = 500,
    this.onImageCropped,
    required this.pickedImage,
    this.showCropPresets = true,
    this.showGridLines = false,
    this.useDelegate = false,
    this.enableFreeformCrop = true,
   });

  @override
  State<ImageCropper> createState() => _ImageCropperState();
}

class _ImageCropperState extends State<ImageCropper> {
  final CropController controller = CropController();
  bool _isImageValid = false;
  String? _errorMessage;
  bool _showGridLines = false;
  double? _currentAspectRatio;

  final List<CropPreset> _cropPresets = [
    CropPreset(name: 'Free', aspectRatio: null, icon: Icons.crop_free),
    CropPreset(name: 'Square', aspectRatio: 1.0, icon: Icons.crop_square),
    CropPreset(name: '4:3', aspectRatio: 4.0 / 3.0, icon: Icons.crop_3_2),
    CropPreset(name: '16:9', aspectRatio: 16.0 / 9.0, icon: Icons.crop_16_9),
    CropPreset(name: '3:4', aspectRatio: 3.0 / 4.0, icon: Icons.crop_portrait),
    CropPreset(
        name: '9:16', aspectRatio: 9.0 / 16.0, icon: Icons.crop_portrait),
  ];

  @override
  void initState() {
    super.initState();
    _currentAspectRatio = widget.initialAspectRatio;
    _showGridLines = widget.showGridLines;
    _validateImage();
  }

  Future<void> _validateImage() async {
    if (widget.pickedImage.path.isEmpty || !await widget.pickedImage.exists()) {
      setState(() {
        _errorMessage = 'Provided image file does not exist';
      });
      return;
    }
    try {
      final bytes = await widget.pickedImage.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frameInfo = await codec.getNextFrame();
      final image = frameInfo.image;
      if (image.width > 0 && image.height > 0) {
        setState(() {
          _isImageValid = true;
        });
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            try {
              controller.aspectRatio = _currentAspectRatio;
              controller.crop = widget.initialCrop;
              controller.rotation = CropRotation.up; // Ensure initial rotation
            } catch (e) {
              debugPrint('Error setting initial controller values: $e');
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  try {
                    controller.aspectRatio = _currentAspectRatio;
                    controller.crop = widget.initialCrop;
                    controller.rotation = CropRotation.up;
                  } catch (e) {
                    debugPrint('Fallback error setting controller values: $e');
                    controller.aspectRatio = null;
                    controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                    controller.rotation = CropRotation.up;
                  }
                }
              });
            }
          }
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid image data: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return getDeviceType(context).name == 'mobile'
        ? ImageCropperMobile(
            pickedImage: widget.pickedImage,
            showCropPresets: widget.showCropPresets,
            showGridLines: widget.showGridLines,
            useDelegate: widget.useDelegate,
            enableFreeformCrop: widget.enableFreeformCrop,
             onImageCropped: widget.onImageCropped)
        : Scaffold(
            appBar: CustomAppBar(title: 'Crop Image'),
            body: LayoutBuilder(
              builder: (context, constraints) {
                final isTablet = getDeviceType(context) == DeviceType.tablet;
                return isTablet
                    ? Column(
                        children: [
                          Expanded(flex: 7, child: _buildBody(constraints)),
                          Expanded(
                              flex: 3,
                              child: _buildBottomControls(constraints)),
                        ],
                      )
                    : Row(
                        children: [
                          Expanded(
                              flex: 3,
                              child: _buildBottomControls(constraints)),
                          Expanded(flex: 7, child: _buildBody(constraints)),
                        ],
                      );
              },
            ),
          );
  }

  Widget _buildBody(BoxConstraints constraints) {
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      );
    }

    if (!_isImageValid) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading image...'),
          ],
        ),
      );
    }

    final isTablet = getDeviceType(context) == DeviceType.tablet;
    final maxWidth = constraints.maxWidth;
    final maxHeight = constraints.maxHeight;
    final cropAreaWidth = isTablet ? maxWidth * 0.9 : maxWidth * 0.8;
    final cropAreaHeight = isTablet ? maxHeight * 0.6 : maxHeight * 0.7;
    final horizontalPadding = isTablet ? maxWidth * 0.05 : maxWidth * 0.1;

    return Stack(
      children: [
        Image.asset(
          height: maxHeight,
          AssetsManager.background_img,
          fit: BoxFit.cover,
        ),
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.symmetric(horizontal: horizontalPadding),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                width: cropAreaWidth,
                height: cropAreaHeight,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: cropAreaWidth,
                      height: cropAreaHeight * 0.83,
                      child: CropImage(
                        controller: controller,
                        image: Image.file(widget.pickedImage),
                        paddingSize: widget.paddingSize,
                        alwaysMove: widget.alwaysMove,
                        minimumImageSize: widget.minimumImageSize,
                        maximumImageSize: widget.maximumImageSize,
                        onCrop: (rect) {
                          controller.crop = rect;
                        },
                      ),
                    ),
                    SizedBox(height: isTablet ? 2 : cropAreaHeight * 0.02),
                    //SizedBox(height: 2),
                    _buildActionButtons(cropAreaWidth),
                  ],
                ),
              ),
              SizedBox(height: isTablet ? 10 : maxHeight * 0.02),
             // SizedBox(height: 10),
              SizedBox(
                width: cropAreaWidth * 0.5,
                child: ElevatedButton.icon(
                  onPressed: _finished,
                  icon: const Icon(Icons.crop, size: 20),
                  label: const Text('Crop Image'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              SizedBox(height: isTablet ? 4 : maxHeight * 0.03),
            // SizedBox(height: 4),
            ],
          ),
        ),
        if (_showGridLines) _buildGridOverlay(),
      ],
    );
  }

  Widget _buildGridOverlay() {
    return Positioned.fill(
      child: IgnorePointer(
        child: CustomPaint(
          painter: GridPainter(),
        ),
      ),
    );
  }

  Widget _buildBottomControls(BoxConstraints constraints) {
    final isTablet = getDeviceType(context) == DeviceType.tablet;
    final maxWidth = constraints.maxWidth;
    final maxHeight = constraints.maxHeight;
    final padding = isTablet ? maxWidth * 0.02 : 16.0;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: isTablet ? const Offset(0, -2) : const Offset(-2, 0),
          ),
        ],
      ),
      // Wrap the Column with a SingleChildScrollView to prevent overflow
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (widget.showCropPresets) _buildCropPresets(maxWidth, maxHeight),
            SizedBox(height: 10),
            ElevatedButton.icon(
              onPressed: _showHelpDialog,
              icon: const Icon(Icons.help_outline, size: 20),
              label: const Text('Help'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[300],
                foregroundColor: Colors.black,
                padding: EdgeInsets.symmetric(
                    vertical: 14,
                    horizontal: (getDeviceType(context) == DeviceType.tablet)
                        ? 100
                        : 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCropPresets(double maxWidth, double maxHeight) {
    final isTablet = getDeviceType(context) == DeviceType.tablet;
    final gridHeight = isTablet ? maxHeight * 0.5 : 400.0;
    final crossAxisCount = isTablet ? 6 : 3;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Crop Ratios',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 10),
        SizedBox(
          //  height: gridHeight,
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              mainAxisSpacing: isTablet ? 16 : 32,
              crossAxisSpacing: isTablet ? 16 : 32,
              childAspectRatio: 1,
            ),
            itemCount: _cropPresets.length,
            itemBuilder: (context, index) {
              final preset = _cropPresets[index];
              final isSelected = _isPresetSelected(preset);

              return GestureDetector(
                onTap: () => _selectCropPreset(preset),
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Theme.of(context).primaryColor.withOpacity(0.3)
                        : Colors.grey.withOpacity(0.25),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey.withOpacity(0.8),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        preset.icon,
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[600],
                        size: 20,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        preset.name,
                        style: TextStyle(
                          fontSize: 10,
                          color: isSelected
                              ? Theme.of(context).primaryColor
                              : Colors.grey[600],
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(double cropAreaWidth) {
    final buttonPadding = cropAreaWidth * 0.05;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: buttonPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            icon: Icons.zoom_in,
            label: 'Zoom In',
            onPressed: _zoomIn,
          ),
          _buildActionButton(
            icon: Icons.zoom_out,
            label: 'Zoom Out',
            onPressed: _zoomOut,
          ),
          _buildActionButton(
            icon: Icons.rotate_90_degrees_ccw_outlined,
            label: 'Rotate Left',
            onPressed: _rotateLeft,
          ),
          _buildActionButton(
            icon: Icons.rotate_90_degrees_cw_outlined,
            label: 'Rotate Right',
            onPressed: _rotateRight,
          ),
          _buildActionButton(
            icon: Icons.refresh,
            label: 'Reset',
            onPressed: _resetCrop,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon),
          style: IconButton.styleFrom(
            backgroundColor: Colors.grey.withOpacity(0.1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        const SizedBox(height: 4),
        CustomText(
          text: label,
          size: 14,
          align: TextAlign.center,
        ),
      ],
    );
  }

  bool _isPresetSelected(CropPreset preset) {
    if (preset.aspectRatio == null && _currentAspectRatio == null) {
      return true;
    }
    if (preset.aspectRatio != null && _currentAspectRatio != null) {
      return (preset.aspectRatio! - _currentAspectRatio!).abs() < 0.001;
    }
    return false;
  }

  void _selectCropPreset(CropPreset preset) {
    setState(() {
      _currentAspectRatio = preset.aspectRatio;
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _isImageValid) {
        try {
          controller.aspectRatio = preset.aspectRatio;
          controller.crop = widget.initialCrop;
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error applying crop preset: $e')),
          );
        }
      }
    });
  }

  void _resetCrop() {
    if (!_isImageValid) return;
    try {
      controller.rotation = CropRotation.up;
      controller.crop = widget.initialCrop;
      controller.aspectRatio = _currentAspectRatio;
      setState(() {
        _showGridLines = widget.showGridLines; // Reset grid lines
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error resetting crop: $e')),
      );
    }
  }

  void _zoomIn() {
    if (!_isImageValid) return;
    try {
      final currentCrop = controller.crop;
      final zoomFactor = 0.9;
      final centerX = (currentCrop.left + currentCrop.right) / 2;
      final centerY = (currentCrop.top + currentCrop.bottom) / 2;
      final newWidth = (currentCrop.width * zoomFactor).clamp(0.1, 1.0);
      final newHeight = (currentCrop.height * zoomFactor).clamp(0.1, 1.0);
      final newLeft = (centerX - newWidth / 2).clamp(0.0, 1.0 - newWidth);
      final newTop = (centerY - newHeight / 2).clamp(0.0, 1.0 - newHeight);
      controller.crop = Rect.fromLTWH(newLeft, newTop, newWidth, newHeight);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error zooming in: $e')),
      );
    }
  }

  void _zoomOut() {
    if (!_isImageValid) return;
    try {
      final currentCrop = controller.crop;
      final zoomFactor = 1.1;
      final centerX = (currentCrop.left + currentCrop.right) / 2;
      final centerY = (currentCrop.top + currentCrop.bottom) / 2;
      final newWidth = (currentCrop.width * zoomFactor).clamp(0.1, 1.0);
      final newHeight = (currentCrop.height * zoomFactor).clamp(0.1, 1.0);
      final newLeft = (centerX - newWidth / 2).clamp(0.0, 1.0 - newWidth);
      final newTop = (centerY - newHeight / 2).clamp(0.0, 1.0 - newHeight);
      controller.crop = Rect.fromLTWH(newLeft, newTop, newWidth, newHeight);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error zooming out: $e')),
      );
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: CustomText(
          text: 'How to Crop',
          size: 18,
          weight: FontWeight.w600,
          color: AppColors.primaryLightTextColor,
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              text: '• Drag to move the image',
              size: 16,
              weight: FontWeight.w400,
              color: AppColors.primaryLightTextColor,
            ),
            CustomText(
              text: '• Pinch to zoom in/out',
              size: 16,
              weight: FontWeight.w400,
              color: AppColors.primaryLightTextColor,
            ),
            CustomText(
              text: '• Use crop ratios for specific dimensions',
              size: 16,
              weight: FontWeight.w400,
              color: AppColors.primaryLightTextColor,
            ),
            CustomText(
              text: '• Rotate using the rotation buttons',
              size: 16,
              weight: FontWeight.w400,
              color: AppColors.primaryLightTextColor,
            ),
            CustomText(
              text: '• Reset to start over',
              size: 16,
              weight: FontWeight.w400,
              color: AppColors.primaryLightTextColor,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const CustomText(
              text: 'Got it',
              size: 22,
              weight: FontWeight.w800,
              color: AppColors.primaryLightTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _rotateLeft() async {
    if (!_isImageValid) return;
    try {
      controller.rotateLeft();
      setState(() {}); // Force UI refresh
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error rotating left: $e')),
      );
    }
  }

  Future<void> _rotateRight() async {
    if (!_isImageValid) return;
    try {
      controller.rotateRight();
      setState(() {}); // Force UI refresh
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error rotating right: $e')),
      );
    }
  }

  Future<void> _finished() async {
  if (!mounted || !_isImageValid) return;

  try {
    // Use the crop_image package's built-in functionality to get the cropped image
    final ui.Image croppedUiImage = await controller.croppedBitmap();
    
    // Validate the cropped image dimensions
    if (croppedUiImage.width <= 0 || croppedUiImage.height <= 0) {
      throw Exception('Cropped image has invalid dimensions: ${croppedUiImage.width}x${croppedUiImage.height}');
    }
    
    // Log the cropped image dimensions for debugging
    debugPrint('Cropped image dimensions: ${croppedUiImage.width}x${croppedUiImage.height}');
    
    // Check if image meets minimum size requirements
    if (croppedUiImage.width < 200 || croppedUiImage.height < 200) {
      // Resize the image to meet minimum requirements
      final ui.Image resizedImage = await _resizeImage(croppedUiImage, 200, 200);
      debugPrint('Resized image dimensions: ${resizedImage.width}x${resizedImage.height}');
      
      // Use the resized image for compression
      final compressedFile = await ImageCompressionUtils.compressImageToSize(
        resizedImage,
        maxSizeKB: 250,
        customFileName: 'cropped_author_image_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      
      // Log file size
      final fileSizeKB = (await compressedFile.length()) / 1024;
      debugPrint('Cropped image size: ${fileSizeKB.toStringAsFixed(1)} KB');
      
      if (mounted) {
        if (widget.useDelegate) {
          widget.onImageCropped?.call(compressedFile);
          Navigator.pop(context, true);
        } else {
          Navigator.pop(context, compressedFile);
        }
      }
    } else {
      // Original image is large enough, proceed normally
      final compressedFile = await ImageCompressionUtils.compressImageToSize(
        croppedUiImage,
        maxSizeKB: 250,
        customFileName: 'cropped_author_image_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      
      final fileSizeKB = (await compressedFile.length()) / 1024;
      debugPrint('Cropped image size: ${fileSizeKB.toStringAsFixed(1)} KB');
      
      if (mounted) {
        if (widget.useDelegate) {
          widget.onImageCropped?.call(compressedFile);
          Navigator.pop(context, true);
        } else {
          Navigator.pop(context, compressedFile);
        }
      }
    }
  } catch (e) {
    debugPrint('Error in _finished: $e');
    
    String errorMessage = 'Error cropping image';
    if (e.toString().contains('200')) {
      errorMessage = 'The cropped area is too small. Please zoom out or select a larger area.';
    }
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}

  // Helper method to resize images that are too small
  Future<ui.Image> _resizeImage(ui.Image image, int minWidth, int minHeight) async {
    final int originalWidth = image.width;
    final int originalHeight = image.height;
    
    // Calculate scaling factors to maintain aspect ratio
    double widthScale = minWidth / originalWidth;
    double heightScale = minHeight / originalHeight;
    double scale = math.max(widthScale, heightScale);
    
    final int newWidth = (originalWidth * scale).round();
    final int newHeight = (originalHeight * scale).round();
    
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    
    // Draw the image scaled up
    final paint = Paint()..filterQuality = FilterQuality.high;
    canvas.scale(scale, scale);
    canvas.drawImage(image, Offset.zero, paint);
    
    final picture = recorder.endRecording();
    return await picture.toImage(newWidth, newHeight);
  }
}