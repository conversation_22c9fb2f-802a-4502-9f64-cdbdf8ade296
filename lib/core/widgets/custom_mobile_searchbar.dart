import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../app/imports/core_imports.dart';
import '../../app/imports/packages_imports.dart';

class CustomSearchMobileBar extends StatelessWidget {
  final VoidCallback? onTap; // Handle tap on search bar
  final double? height;
  final double? width;
  final TextEditingController controller;
  final String? hintText;
  final bool autoFocus;
  final int maxLines;
  final TextInputType keyboardType;
  final Function(String)? onChanged;
  final List<TextInputFormatter> formats;
  final VoidCallback? onClear; // New callback for clear action

  const CustomSearchMobileBar({
    super.key,
    this.onTap,
    this.height,
    this.width,
    required this.controller,
    this.hintText,
    this.maxLines = 1,
    this.autoFocus = false,
    this.keyboardType = TextInputType.text,
    this.onChanged,
    this.formats = const [],
    this.onClear, // Added onClear parameter
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 25),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(55.r),
            border: Border.all(color: AppColors.lightestGreyColor),
          ),
          child: TextField(
            maxLines: maxLines,
            controller: controller,
            keyboardType: keyboardType,
            autofocus: autoFocus,
            inputFormatters: formats,
            textAlignVertical: TextAlignVertical.center,
            style: context.theme.textTheme.labelMedium!.copyWith(
              fontWeight: FontWeight.w400,
              fontSize: 16,
              color: AppColors.texGreyColor,
            ),
            decoration: InputDecoration(
              isDense: true,
              filled: true,
              fillColor: Colors.transparent,
              hintText: hintText ?? "search for recipe ",
              hintStyle: context.theme.inputDecorationTheme.hintStyle!.copyWith(
                color: Colors.grey.withOpacity(.6),
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
              errorStyle: context.theme.inputDecorationTheme.errorStyle,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(50.r),
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(50.r),
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(50.r),
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              prefixIcon: Padding(
                padding: EdgeInsets.only(left: 20.w, right: 10.w),
                child: Icon(
                  Icons.search,
                  color: Colors.grey.withOpacity(.6),
                  size: 24,
                ),
              ),

              suffixIcon: Padding(
                padding: EdgeInsets.only(right: 20.w),
                child: IconButton(
                  icon: Icon(
                    Icons.mic,
                    color: Colors.red,
                    size: 24,
                  ),
                  onPressed: () {},
                ),
              ),
            ),
            onChanged: (value) {
              if (onChanged != null) {
                onChanged!(value);
              }
              (context as Element).markNeedsBuild();
            },
          ),
        ),
      ),
    );
  }
}