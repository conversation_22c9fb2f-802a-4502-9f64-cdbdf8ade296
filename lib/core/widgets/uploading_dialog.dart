import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../app/theme/colors.dart';

class UploadingDialog extends StatefulWidget {
  final String title;
  final String subtitle;
  final double progress;

  const UploadingDialog({
    required this.title,
    required this.subtitle,
    required this.progress,
    super.key,
  });

  @override
  _UploadingDialogState createState() => _UploadingDialogState();
}

class _UploadingDialogState extends State<UploadingDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 6), // Total animation duration
      vsync: this,
    );
    _progressAnimation =
        Tween<double>(begin: 0.0, end: widget.progress.clamp(0.0, 1.0))
            .animate(_controller)
          ..addListener(() {
            setState(() {}); // Update UI with animation value
          });
    _controller.forward();
  }

  @override
  void didUpdateWidget(UploadingDialog oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value, // Start from current value
        end: widget.progress.clamp(0.0, 1.0),
      ).animate(_controller);
      _controller
        ..reset()
        ..forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        margin: EdgeInsets.all(16),
        width: 400,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1), // Replaced withValues
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              text: widget.title,
              size: 16,
              weight: FontWeight.w600,
              color: AppColors.primaryLightTextColor,
            ),
            SizedBox(height: 8),
            CustomText(
              text: widget.subtitle,
              size: 14,
              weight: FontWeight.w400,
              color: AppColors.primaryLightTextColor,
            ),
            SizedBox(height: 12),
            Padding(
              padding: EdgeInsets.all(15.0),
              child: LinearPercentIndicator(
                lineHeight: 14.0,
                percent: _progressAnimation.value,
                center: CustomText(
                  text:
                      "${(_progressAnimation.value * 100).toStringAsFixed(1)}%",
                  size: 12,
                  color: Colors.white,
                ),
                linearStrokeCap: LinearStrokeCap.roundAll,
                backgroundColor: Colors.grey,
                progressColor: AppColors.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
