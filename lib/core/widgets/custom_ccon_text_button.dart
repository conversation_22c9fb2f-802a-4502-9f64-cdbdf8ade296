import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

class CustomIconTextButton extends StatelessWidget {
  final String iconPath;
  final String text;
  final VoidCallback? onTap;
  final Color iconColor;
  final Color textColor;
  final Color backgroundColor;
  final double? height;
  final Color borderColor;

  const CustomIconTextButton({
    super.key,
    required this.iconPath,
    required this.text,
    this.onTap,
    this.height,
    this.iconColor = Colors.red,
    this.textColor = const Color(0xFF6E6E6E), // AppColors.primaryGreyColor
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFE0E0E0), // AppColors.lightestGreyColor
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: height,
        padding:   EdgeInsets.symmetric(horizontal:getDeviceType(context).name=='mobile'?8: 12.0, vertical:getDeviceType(context).name=='mobile'?8: 12.0),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(10.0),
          border: Border.all(
            color: borderColor,
            width: 1.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              iconPath,
              width:getDeviceType(context).name=='mobile'? 14:24,
              colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
            ),
            const SizedBox(width: 8.0),
            CustomText(
              text: text,
              color: textColor,
              size: 12,
              weight  : FontWeight.w400,
            ),
          ],
        ),
      ),
    );
  }
}
