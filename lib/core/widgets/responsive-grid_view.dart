import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_grid/responsive_grid.dart';

import '../utils/device_utils.dart';

class ResponsivePaginatedGridView extends StatelessWidget {
  final PageController pageController;
  final dynamic state;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final String currentSearchQuery;
  final bool isHighRes;
  final WidgetRef ref;
  final BuildContext context;
  final void Function(int) onPageChanged;
  final Widget Function(BuildContext, int, int) itemBuilder;

  const ResponsivePaginatedGridView({
    Key? key,
    required this.pageController,
    required this.state,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.currentSearchQuery,
    required this.isHighRes,
    required this.ref,
    required this.context,
    required this.onPageChanged,
    required this.itemBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerSignal: (pointerSignal) {
        if (pointerSignal is PointerScrollEvent) {
          final delta = pointerSignal.scrollDelta.dx != 0
              ? pointerSignal.scrollDelta.dx
              : pointerSignal.scrollDelta.dy;
          if (delta > 0 &&
              pageController.page! < (state.hasMore ? totalPages : totalPages - 1)) {
            pageController.nextPage(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          } else if (delta < 0 && pageController.page! > 0) {
            pageController.previousPage(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        }
      },
      child: PageView.builder(
        controller: pageController,
        onPageChanged: onPageChanged,
        itemCount: state.hasMore ? totalPages + 1 : totalPages,
        itemBuilder: (context, pageIndex) {
          if (pageIndex >= totalPages && state.hasMore) {
            return Center(
              child: LoadingAnimationWidget.fallingDot(
                color: Colors.white,
                size: 50.0,
              ),
            );
          }

          final startIndex = pageIndex * itemsPerPage;
          var endIndex = startIndex + itemsPerPage;
          if (endIndex > totalItems) endIndex = totalItems;

          return LayoutBuilder(
            builder: (context, constraints) {
              debugPrint("GridView constraints: maxWidth=${constraints.maxWidth}, maxHeight=${constraints.maxHeight}");

              // Calculate desired item width based on screen size
              final screenWidth = MediaQuery.of(context).size.width;
              final desiredItemWidth = _calculateDesiredItemWidth(screenWidth);

              return SingleChildScrollView(
                child: ResponsiveGridList(
                  desiredItemWidth: desiredItemWidth,
                  minSpacing: DeviceUtils().isTabletOrIpad(context) ? 8 : 12,
                  children: List.generate(endIndex - startIndex, (index) {
                    final itemIndex = startIndex + index;
                    if (itemIndex >= totalItems) return const SizedBox.shrink();
                    return itemBuilder(context, index, itemIndex);
                  }),
                ),
              );
            },
          );
        },
      ),
    );
  }

  double _calculateDesiredItemWidth(double screenWidth) {
    if (screenWidth > 1200) {
      return 200; // Large screens (desktop)
    } else if (screenWidth > 900) {
      return 180; // Medium-large screens
    } else if (screenWidth > 600) {
      return 160; // Tablets
    } else {
      return 140; // Phones
    }
  }
}