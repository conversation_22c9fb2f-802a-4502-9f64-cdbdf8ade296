import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'custom_searchbar.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class CustomAppbarMobile extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final VoidCallback? onBackPressed; // Renamed for clarity
  final ValueChanged<String>? onSearchChanged; // Corrected to ValueChanged
  final VoidCallback? onViewTypePressed; // Callback for view toggle
  final VoidCallback? onSearchClearPressed; // Callback for clearing search
  final bool isLeading;

  const CustomAppbarMobile({
    super.key,
    required this.title,
    this.actions,
    this.onBackPressed,
    this.onSearchChanged,
    this.onViewTypePressed,
    this.onSearchClearPressed,
    this.isLeading = false,
  });

  @override
  State<CustomAppbarMobile> createState() => _CustomAppbarMobileState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _CustomAppbarMobileState extends State<CustomAppbarMobile>
    with SingleTickerProviderStateMixin {
  bool _isGridView = false;
  bool _isSearching = false;
  final TextEditingController recipeSearchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  final GlobalKey _searchIconKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0), // Start from right
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    recipeSearchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    return AppBar(
      centerTitle: false,
      elevation: 2,
      titleSpacing: 0,
      automaticallyImplyLeading: !kIsWeb,
      leading: widget.isLeading
          ? IconButton(
              icon: SvgPicture.asset(
                AssetsManager.ic_back,
                height: 20,
                width: 20,
                color: theme.iconTheme.color,
              ),
              onPressed: widget.onBackPressed ??
                  () {
                    if (Navigator.of(context).canPop()) {
                      Navigator.of(context).pop();
                    }
                  },
            )
          : null,
      title: SizedBox(
        width: double.infinity,
        child: Padding(
          padding: EdgeInsets.only(left: widget.isLeading ? 0.0 : 16.0),
          child: Row(
            children: [
              Flexible(
                child: CustomText(
                  text: widget.title,
                  color: theme.textTheme.titleLarge?.color ?? Colors.black,
                  weight: FontWeight.w400,
                  size: 18,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        ...?widget.actions, // Include any additional actions
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return _isSearching
                  ? FadeTransition(
                      opacity: _fadeAnimation,
                      child: SlideTransition(
                        position: _slideAnimation,
                        child: CustomSearchBar(
                          width: screenWidth * 0.6,
                          // Responsive width
                          height: 40,
                          // Fixed height for consistency
                          controller: recipeSearchController,
                          onClear: () {
                            _animationController.reverse();
                            setState(() {
                              _isSearching = false;
                              recipeSearchController.clear();
                            });
                            widget.onSearchClearPressed?.call();
                            debugPrint('Search cleared');
                          },
                          onChanged: (query) {
                            widget.onSearchChanged?.call(query);
                          },
                        ),
                      ),
                    )
                  : IconButton(
                      key: _searchIconKey,
                      icon: SvgPicture.asset(
                        AssetsManager.search,
                        height: 20,
                        width: 20,
                      ),
                      onPressed: () {
                        setState(() {
                          _isSearching = true;
                        });
                        _animationController.forward();
                      },
                      tooltip: 'Search Recipes',
                    );
            },
          ),
        ),
        IconButton(
          icon: SvgPicture.asset(
            _isGridView ? AssetsManager.ic_grid : AssetsManager.ic_list,
            height: 20,
            width: 20,
          ),
          onPressed: () {
            setState(() {
              _isGridView = !_isGridView;
            });
            widget.onViewTypePressed?.call();
            debugPrint('Toggled to ${_isGridView ? "grid" : "list"} view');
          },
          tooltip: _isGridView ? 'Switch to List View' : 'Switch to Grid View',
        ),
      ],
    );
  }
}
