import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../app/imports/core_imports.dart';
import '../utils/device_utils.dart';
import 'custom_input_field.dart';
import 'custom_text.dart';

Widget buildTimeField(String label, TextEditingController controller,
    BuildContext context, Function(String)? onChanged) {
  return getDeviceType(context).name == 'mobile'
      ? Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                flex: 1,
                child: CustomText(
                  text: label,
                  color: AppColors.primaryLightTextColor,
                  size: 12,
                  weight: FontWeight.w400,
                )),
            SizedBox(width: 16.w),
            Expanded(
              flex: 2,
              child: SizedBox(
                height: 45, // Match text field height
                child: CustomInputField(
                    editable: false, hintText: controller.text),
              ),
            ),
          ],
        )
      : Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
                // color: Colors.red,
                width: DeviceUtils().isTabletOrIpad(context) ? 70 : 180.w,
                child: CustomText(
                  text: label,
                  color: AppColors.primaryLightTextColor,
                  size: 12,
                  weight: FontWeight.w400,
                  fontFamily: 'Inter',
                )),
            SizedBox(width: 5),
            Expanded(
              flex: 2,
              child:
                  CustomInputField(editable: false, hintText: controller.text),
            ),
          ],
        );
}
