import 'package:flutter/material.dart';

class DashedDivider extends StatelessWidget {
  final double thickness;
  final double dashWidth;
  final double dashSpacing;
  final Color color;

  const DashedDivider({
    Key? key,
    this.thickness = 1.0,
    this.dashWidth = 5.0,
    this.dashSpacing = 5.0,
    this.color = Colors.grey,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _DashedLinePainter(
        color: color,
        thickness: thickness,
        dashWidth: dashWidth,
        dashSpacing: dashSpacing,
      ),
      child: SizedBox(
        height: thickness,
        width: double.infinity,
      ),
    );
  }
}

class _DashedLinePainter extends CustomPainter {
  final double thickness;
  final double dashWidth;
  final double dashSpacing;
  final Color color;

  _DashedLinePainter({
    required this.thickness,
    required this.dashWidth,
    required this.dashSpacing,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = thickness
      ..style = PaintingStyle.stroke;

    double startX = 0;
    final y = size.height / 2;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, y),
        Offset(startX + dashWidth, y),
        paint,
      );
      startX += dashWidth + dashSpacing;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
