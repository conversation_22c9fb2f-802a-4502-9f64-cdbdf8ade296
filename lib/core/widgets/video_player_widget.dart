import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:mastercookai/core/widgets/windows_media_kit_player.dart';
import 'package:video_player/video_player.dart';
import '../../app/imports/core_imports.dart';

class VideoPlayerWidget extends StatelessWidget {
  final File? mediaFile;
  final String? mediaUrl;
  final String? thumbnailPath;
  final double height;
  final Map<String, String>? headers;

  const VideoPlayerWidget({
    super.key,
    this.mediaFile,
    this.mediaUrl,
    this.thumbnailPath,
    this.headers,
    this.height = 200.0,
  });

  @override
  Widget build(BuildContext context) {
    // Use Windows MediaKit player on Windows platform for better compatibility
    if (Platform.isWindows) {
      return WindowsMediaKitPlayer(
        mediaFile: mediaFile,
        mediaUrl: mediaUrl,
        thumbnailPath: thumbnailPath,
        height: height,
        headers: headers,
      );
    }

    // Use standard video player for other platforms
    return _StandardVideoPlayerWidget(
      mediaFile: mediaFile,
      mediaUrl: mediaUrl,
      thumbnailPath: thumbnailPath,
      height: height,
      headers: headers,
    );
  }
}

class _StandardVideoPlayerWidget extends StatefulWidget {
  final File? mediaFile;
  final String? mediaUrl;
  final String? thumbnailPath;
  final double height;
  final Map<String, String>? headers;

  const _StandardVideoPlayerWidget({
    this.mediaFile,
    this.mediaUrl,
    this.thumbnailPath,
    this.headers,
    this.height = 200.0,
  });

  @override
  State<_StandardVideoPlayerWidget> createState() => _StandardVideoPlayerWidgetState();
}

class _StandardVideoPlayerWidgetState extends State<_StandardVideoPlayerWidget> {
  VideoPlayerController? _controller;
  bool _hasError = false;
  String? _errorMessage;
  bool _isPlaying = false;
  Duration _lastPosition = Duration.zero;


  @override
  void initState() {
    super.initState();
     _initVideo();
  }

  Future<void> _initVideo() async {
    try {
      // Skip initialization on iOS Simulator
      if (Platform.isIOS &&
          !kIsWeb &&
          Platform.environment.containsKey('SIMULATOR_DEVICE_NAME')) {
        setState(() {
          _hasError = true;
          _errorMessage = "Video playback not supported on iOS Simulator";
        });
        return;
      }

      // Windows-specific checks and fallbacks
      if (Platform.isWindows) {
        debugPrint("Initializing video player on Windows platform");

        // Check if video URL is accessible
        if (widget.mediaUrl != null && widget.mediaUrl!.isNotEmpty) {
          try {
            final uri = Uri.parse(widget.mediaUrl!);
            debugPrint("Parsed video URL: $uri");
          } catch (e) {
            debugPrint("Invalid video URL format: ${widget.mediaUrl}");
            setState(() {
              _hasError = true;
              _errorMessage = "Invalid video URL format";
            });
            return;
          }
        }
      }

      if (widget.mediaFile != null) {
        _controller = VideoPlayerController.file(widget.mediaFile!);
        debugPrint("Initializing video from file: ${widget.mediaFile!.path}");
      } else if (widget.mediaUrl != null && widget.mediaUrl!.isNotEmpty) {
        // Create controller with proper configuration for Windows
        if (Platform.isWindows) {
          _controller = VideoPlayerController.networkUrl(
            Uri.parse(widget.mediaUrl!),
            httpHeaders: widget.headers ?? {
              'User-Agent': 'MasterCookAI/1.0',
              'Accept': '*/*',
            },
            videoPlayerOptions: VideoPlayerOptions(
              mixWithOthers: false, // Better for Windows
              allowBackgroundPlayback: false,
            ),
          );
        } else {
          _controller = VideoPlayerController.networkUrl(
            Uri.parse(widget.mediaUrl!),
            httpHeaders: widget.headers ?? {},
            videoPlayerOptions: VideoPlayerOptions(
              mixWithOthers: true,
              allowBackgroundPlayback: false,
            ),
          );
        }
        debugPrint("Initializing video from URL: ${widget.mediaUrl}");
      } else {
        setState(() {
          _hasError = true;
          _errorMessage = "No valid media source provided";
        });
        return;
      }

      // Windows-specific initialization with timeout
      if (Platform.isWindows) {
        debugPrint("Starting Windows-specific video initialization");
        await _controller!.initialize().timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            throw Exception("Video initialization timeout on Windows");
          },
        );
      } else {
        await _controller!.initialize();
      }

      // Listen to controller state changes
      _controller!.addListener(_onControllerUpdate);

      // Windows-specific: Don't auto-play test on Windows as it can cause issues
      if (!Platform.isWindows) {
        // Attempt to play the video briefly to test playback
        await _controller!.play();
        await Future.delayed(const Duration(milliseconds: 100));
        await _controller!.pause();
      }

      setState(() {});
      debugPrint("Video initialization completed successfully");
    } catch (e, stackTrace) {
      debugPrint("Video initialization failed: $e\nStackTrace: $stackTrace");
      setState(() {
        _hasError = true;
        if (Platform.isWindows) {
          _errorMessage = _getWindowsSpecificErrorMessage(e.toString());
        } else {
          _errorMessage = e.toString().contains("401")
              ? "Unauthorized access to video. Please check authentication."
              : "Failed to load video: $e";
        }
      });
    }
  }

  String _getWindowsSpecificErrorMessage(String error) {
    if (error.contains("timeout")) {
      return "Video loading timeout. Please check your internet connection.";
    } else if (error.contains("UnimplementedError") || error.contains("init() has not been implemented")) {
      return "Video playback not supported on Windows with current player. Please try a different video format or use a different device.";
    } else if (error.contains("codec") || error.contains("format")) {
      return "Video format not supported on Windows. Please try a different video.";
    } else if (error.contains("network") || error.contains("connection")) {
      return "Network error. Please check your internet connection.";
    } else if (error.contains("401")) {
      return "Unauthorized access to video. Please check authentication.";
    } else if (error.contains("404")) {
      return "Video not found. The video may have been moved or deleted.";
    } else {
      return "Video playback error on Windows: ${error.length > 100 ? '${error.substring(0, 100)}...' : error}";
    }
  }

  void _onControllerUpdate() {
    if (!mounted) return;
    final controller = _controller;
    if (controller == null || !controller.value.isInitialized) return;

    final newPlayingState = controller.value.isPlaying;
    final newPosition = controller.value.position;
    final isBuffering = controller.value.isBuffering;

    // Update only if playing state, position, or buffering state changes
    if (newPlayingState != _isPlaying ||
        newPosition.inSeconds != _lastPosition.inSeconds ||
        controller.value.isBuffering) {
      setState(() {
        _isPlaying = newPlayingState;
        _lastPosition = newPosition;
      });
      debugPrint(
          "Video position: ${_formatDuration(newPosition)}, isPlaying: $_isPlaying, isBuffering: $isBuffering");
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_onControllerUpdate);
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError || _controller == null || !_controller!.value.isInitialized) {
      return Container(
        height: widget.height,
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            // Background thumbnail or placeholder
            CommonImage(
              imageSource: widget.thumbnailPath ?? '',
              placeholder: AssetsManager.recipe_place_holder,
              fit: BoxFit.cover,
              width: double.infinity,
              height: widget.height,
            ),
            // Dark overlay
            Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            // Error message and retry button (matching screenshot design)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.warning_rounded,
                    color: Colors.white,
                    size: 48,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Video Unavailable',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  if (_errorMessage != null)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _hasError = false;
                        _errorMessage = null;
                      });
                      _initVideo();
                    },
                    icon: Icon(Icons.refresh, size: 18),
                    label: Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return AspectRatio(
      aspectRatio: _controller!.value.aspectRatio,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            height: widget.height,
            child: VideoPlayer(_controller!),
          ),
          // Buffer loader
          if (_controller!.value.isBuffering)
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          // Play/Pause button
          Positioned(
            bottom: 40.h,
            child: IconButton(
              icon: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size: 40,
              ),
              onPressed: () {
                if (_controller!.value.isPlaying) {
                  _controller!.pause();
                } else {
                  _controller!.play();
                }
                debugPrint(
                    "Video ${_controller!.value.isPlaying ? 'paused' : 'playing'}");
              },
            ),
          ),
          // Seek bar
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: VideoProgressIndicator(
              _controller!,
              allowScrubbing: true,
              colors: const VideoProgressColors(
                playedColor: Colors.blue,
                bufferedColor: Colors.blueGrey,
                backgroundColor: Colors.grey,
              ),
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
            ),
          ),
          // Video position and duration
          Positioned(
            bottom: 20.h,
            left: 8.w,
            child: Text(
              _formatDuration(_controller!.value.position),
              style: TextStyle(color: Colors.white, fontSize: 12.sp),
            ),
          ),
          Positioned(
            bottom: 20.h,
            right: 8.w,
            child: Text(
              _formatDuration(_controller!.value.duration),
              style: TextStyle(color: Colors.white, fontSize: 12.sp),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }
}