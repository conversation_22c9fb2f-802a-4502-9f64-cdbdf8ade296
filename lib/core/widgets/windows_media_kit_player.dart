import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import '../../app/imports/core_imports.dart';

/// Windows-compatible video player fallback
/// Shows a user-friendly error message when MediaKit is not available or has issues
class WindowsMediaKitPlayer extends StatefulWidget {
  final File? mediaFile;
  final String? mediaUrl;
  final String? thumbnailPath;
  final double height;
  final Map<String, String>? headers;

  const WindowsMediaKitPlayer({
    super.key,
    this.mediaFile,
    this.mediaUrl,
    this.thumbnailPath,
    this.headers,
    this.height = 200.0,
  });

  @override
  State<WindowsMediaKitPlayer> createState() => _WindowsMediaKitPlayerState();
}

class _WindowsMediaKitPlayerState extends State<WindowsMediaKitPlayer> {
  @override
  Widget build(BuildContext context) {
    // For now, show a Windows-compatible error message
    // This can be replaced with actual MediaKit implementation when imports are resolved
    return _buildWindowsErrorWidget();
  }

  Widget _buildWindowsErrorWidget() {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          // Background thumbnail
          if (widget.thumbnailPath != null)
            CommonImage(
              imageSource: widget.thumbnailPath!,
              placeholder: AssetsManager.recipe_place_holder,
              fit: BoxFit.cover,
              width: double.infinity,
              height: widget.height,
            ),
          // Dark overlay
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          // Error content matching the screenshot design
          Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.warning_rounded,
                    color: Colors.white,
                    size: 48,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Video Unavailable',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Video playback not supported on this platform with current configuration. Please try a different device or video format.',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Could implement retry logic here
                      debugPrint("Retry button pressed - MediaKit not available");
                    },
                    icon: Icon(Icons.refresh, size: 18),
                    label: Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
