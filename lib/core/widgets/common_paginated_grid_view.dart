import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/core/utils/Utils.dart';

import '../utils/device_utils.dart';
import '../utils/screen_sizer.dart';

class CommonPaginatedGridView extends StatelessWidget {
  final PageController pageController;
  final dynamic state;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final int crossAxisCount;
  final String currentSearchQuery;
  final bool isHighRes;
  final WidgetRef ref;
  final BuildContext context;
  final void Function(int) onPageChanged;
  final Widget Function(BuildContext, int, int) itemBuilder;

  const CommonPaginatedGridView({
    Key? key,
    required this.pageController,
    required this.state,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.crossAxisCount,
    required this.currentSearchQuery,
    required this.isHighRes,
    required this.ref,
    required this.context,
    required this.onPageChanged,
    required this.itemBuilder,
   }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerSignal: (pointerSignal) {
        if (pointerSignal is PointerScrollEvent) {
          final delta = pointerSignal.scrollDelta.dx != 0
              ? pointerSignal.scrollDelta.dx
              : pointerSignal.scrollDelta.dy;
          if (delta > 0 &&
              pageController.page! < (state.hasMore ? totalPages : totalPages - 1)) {
            pageController.nextPage(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          } else if (delta < 0 && pageController.page! > 0) {
            pageController.previousPage(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        }
      },
      child: PageView.builder(
        controller: pageController,
        onPageChanged: onPageChanged,
        itemCount: state.hasMore ? totalPages + 1 : totalPages,
        itemBuilder: (context, pageIndex) {
          if (pageIndex >= totalPages && state.hasMore) {
            return Center(
              // child: LoadingAnimationWidget.fallingDot(
              //   color: Colors.white,
              //   size: 50.0,
              // ),
            );
          }

          final startIndex = pageIndex * itemsPerPage;
          var endIndex = startIndex + itemsPerPage;
          if (endIndex > totalItems) endIndex = totalItems;

          return LayoutBuilder(
            builder: (context, constraints) {
              // Calculate item height
              final itemHeight = Utils().calculateGridItemHeight(
                constraints,
                context,
                crossAxisCount,
              );
              // Call the callback to pass the height to the parent

              // Optionally print or use the height
              debugPrint('Grid item height: $itemHeight');

              return GridView.builder(
                padding: EdgeInsets.symmetric(
                  horizontal: DeviceUtils().isTabletOrIpad(context) ? 8.w : 16.w,
                  vertical: DeviceUtils().isTabletOrIpad(context) ? 8.h : 16.h,
                ),
                // gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                //   crossAxisCount: 2,
                //   childAspectRatio: 1.0,
                //   crossAxisSpacing: 16,
                //   mainAxisSpacing: 16,
                // ),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  mainAxisSpacing: DeviceUtils().isTabletOrIpad(context) ? 8.h : 12.h,
                  crossAxisSpacing: DeviceUtils().isTabletOrIpad(context) ? 8.w : 12.w,
                  childAspectRatio: DeviceUtils().isTabletOrIpad(context)
                      ? 0.8
                      : ScreenSizer().calculateChildAspectRatio(
                    MediaQuery.of(context).size.width,
                    MediaQuery.of(context).size.height,
                  ),
                ),
                itemCount: endIndex - startIndex,
                itemBuilder: (context, index) {
                  final itemIndex = startIndex + index;
                  if (itemIndex >= totalItems) return const SizedBox.shrink();
                  return itemBuilder(context, index, itemIndex);
                },
              );
            },
          );
        },
      ),
    );
  }
}