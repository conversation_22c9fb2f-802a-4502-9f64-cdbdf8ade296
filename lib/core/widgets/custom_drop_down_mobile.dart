import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';

import 'custom_text.dart';

class CustomDropDownMobile<T> extends StatelessWidget {
  final String label;
  final String? value;
  final List<T> options;
  final ValueChanged<String?> onChanged;
  final String Function(T)
      getDisplayString; // Function to extract display string from model

  const CustomDropDownMobile({
    super.key,
    required this.label,
    required this.value,
    required this.options,
    required this.onChanged,
    required this.getDisplayString,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          flex: 1,
          child: SizedBox(
            width: DeviceUtils().isTabletOrIpad(context) ? 70 : 180.w,
            child: Row(
              children: [
                CustomText(
                  text: label,
                  color: AppColors.primaryGreyColor,
                  weight: FontWeight.w400,
                  size: 12, //context.theme.textTheme.titleMedium!.fontSize,
                ),
                CustomText(
                  text: ' *',
                  color: AppColors.primaryColor,
                  weight: FontWeight.w400,
                  size: 12, //context.theme.textTheme.titleMedium!.fontSize,
                ),
              ],
            ),
          ),
        ),
        SizedBox(width: 10),
        Expanded(
          flex: 2,
          child: PopupMenuButton<String>(
            onSelected: onChanged,
            itemBuilder: (context) {
              return options.map((option) {
                final displayString = getDisplayString(option);
                return PopupMenuItem<String>(
                  value: displayString,
                  child: CustomText(
                    text: displayString,
                    size: 12, //context.theme.textTheme.titleMedium!.fontSize,
                    weight: displayString == value
                        ? FontWeight.w600
                        : FontWeight.w400,
                    color: displayString == value
                        ? AppColors.primaryColor
                        : AppColors.primaryGreyColor,
                  ),
                );
              }).toList();
            },
            color: Colors.white,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.grey[300]!,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
                color: AppColors.whiteColor,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: value ?? 'Select',
                    color: AppColors.primaryGreyColor,
                    weight: FontWeight.w400,
                    size: 12, //context.theme.textTheme.titleMedium!.fontSize,
                  ),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 5.w),
                    decoration: BoxDecoration(
                      border: Border.all(
                          color: Colors.black38.withValues(alpha: .2)),
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      size: 16,
                      color: AppColors.secondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
