import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../app/imports/packages_imports.dart';

class CustomSearchBar extends StatefulWidget {
  final VoidCallback? onTap; // Handle tap on search bar
  final double? height;
  final double? width;
  final TextEditingController controller;
  final String? hintText;
  final bool autoFocus;
  final int maxLines;
  final TextInputType keyboardType;
  final Function(String)? onChanged;
  final List<TextInputFormatter> formats;
  final VoidCallback? onClear; // New callback for clear action

  const CustomSearchBar({
    super.key,
    this.onTap,
    this.height,
    this.width,
    required this.controller,
    this.hintText,
    this.maxLines = 1,
    this.autoFocus = false,
    this.keyboardType = TextInputType.text,
    this.onChanged,
    this.formats = const [],
    this.onClear, // Added onClear parameter
  });

  @override
  State<CustomSearchBar> createState() => _CustomSearchBarState();
}

class _CustomSearchBarState extends State<CustomSearchBar> {
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _hasText = widget.controller.text.isNotEmpty;
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = widget.controller.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          width: widget.width ?? 500.w,
          height: widget.height ?? 50.h,
          margin: EdgeInsets.only(left: 20.w, right: 20.w),
          decoration: BoxDecoration(
            color: AppColors.lightestGreyColor.withValues(alpha: .2),
            borderRadius: BorderRadius.circular(10),
          ),
          child: TextField(
            maxLines: widget.maxLines,
            controller: widget.controller,
            keyboardType: widget.keyboardType,
            autofocus: widget.autoFocus,
            inputFormatters: widget.formats,
            textAlignVertical: TextAlignVertical.center,
            style: context.theme.textTheme.labelMedium!.copyWith(
              fontWeight: FontWeight.w400,
              fontSize: headingSixFontSize,
            ),
            decoration: InputDecoration(
              isDense: true,
              contentPadding: EdgeInsets.zero,
              filled: context.theme.inputDecorationTheme.filled,
              fillColor: AppColors.lightestGreyColor.withOpacity(.6),
              hintText: widget.hintText ?? "Search",
              hintStyle: context.theme.inputDecorationTheme.hintStyle!.copyWith(
                color: Colors.black.withAlpha(70),
                fontSize: headingSixFontSize,
                fontWeight: FontWeight.w400,
              ),
              errorStyle: context.theme.inputDecorationTheme.errorStyle,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(
                  color: AppColors.lightestGreyColor.withOpacity(.2),
                ),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              prefixIcon: Icon(
                Icons.search,
                color: AppColors.textGreyColor.withOpacity(.3),
              ),
              suffixIcon: _hasText
                  ? IconButton(
                      icon: const Icon(
                        Icons.cancel,
                        size: 18,
                        color: Colors.grey,
                      ),
                      onPressed: () {
                        widget.controller.clear();
                        if (widget.onClear != null) {
                          widget.onClear!(); // Call the onClear callback
                        }
                        if (widget.onChanged != null) {
                          widget.onChanged!(''); // Notify onChanged with empty string
                        }
                      },
                    )
                  : null,
            ),
            onChanged: (value) {
              if (widget.onChanged != null) {
                widget.onChanged!(value);
              }
            },
          ),
        ),
      ),
    );
  }
}
