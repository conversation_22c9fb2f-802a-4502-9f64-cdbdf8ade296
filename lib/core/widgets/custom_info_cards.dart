import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:dotted_border/dotted_border.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../core/utils/device_utils.dart';

class CustomInfoCard extends StatelessWidget {
  final String title;
  final String subheading;
  final String actionText;
  final VoidCallback onActionPressed;

  const CustomInfoCard({
    super.key,
    required this.title,
    required this.subheading,
    required this.actionText,
    required this.onActionPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
      child: DottedBorder(
        borderType: BorderType.RRect,
        radius: const Radius.circular(15),
        padding: const EdgeInsets.all(30),
        dashPattern: [8, 4],
        strokeWidth: 1,
        color: AppColors.selctBoarderColor,
        child: Center( // ✅ Center widget ensures content is centered in available space
          child: Column(
            mainAxisSize: MainAxisSize.min, // ✅ Prevents Column from taking full height
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CustomText(
                text: title,
                color: AppColors.primaryLightTextColor,
                size:  20,
                weight: FontWeight.w500,
                align: TextAlign.center,
              ),
              SizedBox(height: 10),
              CustomText(
                text: subheading,
                color: AppColors.primaryLightTextColor,
                size:  12,
                weight: FontWeight.w400,
                align: TextAlign.center,
              ),
              SizedBox(height: 30),
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: onActionPressed,
                  child: CustomText(
                    text: actionText,
                    color: AppColors.primaryBorderColor,
                    size:  14,
                    weight: FontWeight.w400,
                    decoration: TextDecoration.underline,
                    decorationThickness: 1.0,
                    decorationColor: AppColors.primaryBorderColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
