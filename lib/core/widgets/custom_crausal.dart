// lib/core/widgets/banner_carousel.dart
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import '../../../app/imports/core_imports.dart';
import '../utils/device_utils.dart';
import 'common_image.dart';

class BannerCarousel extends StatefulWidget {
  final List<String> bannerImages;
  final Function(int)? onImageClick;

  const BannerCarousel({
    Key? key,
    required this.bannerImages,
    this.onImageClick,
  }) : super(key: key);

  @override
  State<BannerCarousel> createState() => _BannerCarouselState();
}

class _BannerCarouselState extends State<BannerCarousel> {
  int _activeIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CarouselSlider(
          options: CarouselOptions(
            autoPlay: true,
            autoPlayInterval: const Duration(seconds: 5),
            enlargeCenterPage: true,
            viewportFraction: 0.5,
            aspectRatio:DeviceUtils().isTabletOrIpad(context) ?16/4: 16 / 2,
            // Adjusted aspect ratio for taller images
            onPageChanged: (index, reason) {
              setState(() {
                _activeIndex = index;
              });
            },
          ),
          items: widget.bannerImages.asMap().entries.map((entry) {
            int index = entry.key;
            String item = entry.value;

            return Builder(
              builder: (BuildContext context) {
                return MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      widget.onImageClick?.call(index);
                    },
                    child: Container(
                      width: 800,
                    //  height: 300,
                      // Increased height
                      margin: const EdgeInsets.symmetric(horizontal: 5.0),
                      decoration: BoxDecoration(
                        color: AppColors.greyBorderColor,
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 6,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: CommonImage(
                          placeholder: AssetsManager.cb_place_holder,
                          width: 800,
                          //height: 300,
                          // Increased height
                          fit: BoxFit.cover,
                          imageSource: item,
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ),
        SizedBox(height: 10), // Spacing between carousel and dots
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: widget.bannerImages.asMap().entries.map((entry) {
            int index = entry.key;
            return Container(
              width: 8.0,
              height: 8.0,
              margin:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _activeIndex == index
                    ? AppColors.primaryColor // Active dot color
                    : AppColors.greyBorderColor, // Inactive dot color
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
