import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CustomDrawer extends ConsumerWidget {
  final String title;
  final dynamic state;
  final Widget Function(dynamic) buildContent;
  final double? width;
  final Color? backgroundColor;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const CustomDrawer({
    super.key,
    required this.title,
    required this.state,
    required this.buildContent,
    this.width = 400,
    this.backgroundColor = Colors.white,
    this.showBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Drawer(
      width: width,
      backgroundColor: backgroundColor,
      child: SafeArea(
        child: Column(
          children: [
            // Header with back arrow and title
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  if (showBackButton)
                    IconButton(
                      icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
                      onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                    ),
                  if (showBackButton) const SizedBox(width: 8),
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            // Content area
            Expanded(
              child: buildContent(state),
            ),
          ],
        ),
      ),
    );
  }
}
