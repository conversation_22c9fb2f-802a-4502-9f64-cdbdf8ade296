import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../app/imports/packages_imports.dart';

class QuotaExhaustedDialog extends ConsumerWidget {
  final int? recipeCreateGPT;
  final int? recipeImageGPT;
  final String? recipeStorageInMB;
  final String? fileSizeInMB;

  const QuotaExhaustedDialog({
    super.key,
    this.recipeCreateGPT,
    this.recipeImageGPT,
    this.recipeStorageInMB,
    this.fileSizeInMB,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bool isStorageQuota =
        recipeStorageInMB != null && fileSizeInMB != null;
    final bool hasRecipeQuota = (recipeCreateGPT ?? 0) > 0;
    final bool hasImageQuota = (recipeImageGPT ?? 0) > 0;
    final bool hasAnyQuota = hasRecipeQuota || hasImageQuota;

    String getTitle() {
      if (isStorageQuota) {
        return 'Storage Quota Exhausted';
      }
      if (!hasAnyQuota) {
        return 'GPT Recipe Quota Exhausted';
      }
      return 'GPT Recipe Quota';
    }

    String getMessage() {
      if (isStorageQuota) {
        return 'File size (${fileSizeInMB!} MB) '
            'exceeds your storage limit ($recipeStorageInMB).';
      }

      if (!(hasRecipeQuota || hasImageQuota)) {
        return 'You have reached your monthly limits';
      }

      String message = 'Your current monthly usage:\n';
      if (hasRecipeQuota) {
        message += ' • Recipe Creation: $recipeCreateGPT remaining\n';
      } else {
        message += ' • Recipe Creation: Limit reached\n';
      }
      if (hasImageQuota) {
        message += ' • Recipe Image: $recipeImageGPT remaining';
      } else {
        message += ' • Recipe Image: Limit reached';
      }
      return message;
    }

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getDeviceType(context).name == 'mobile'
            ? 0
            : getDeviceType(context).name == 'tablet'
                ? MediaQuery.of(context).size.width * 0.17
                : MediaQuery.of(context).size.width * 0.3,
      ),
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.white,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(
                    AssetsManager.premiumlock,
                    height: 150,
                  ),
                  const SizedBox(height: 20),
                  CustomText(
                    text: getTitle(),
                    size: getDeviceType(context).name == 'mobile' ? 18 : 24,
                    weight: FontWeight.bold,
                    color: AppColors.blackTextColor,
                    align: TextAlign.center,
                  ),
                  const SizedBox(height: 10),
                  CustomText(
                    text: getMessage(),
                    size: getDeviceType(context).name == 'mobile' ? 14 : 16,
                    weight: FontWeight.w400,
                    color: AppColors.textGreyColor,
                    align: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 35.0),
                    child: CustomButton(
                      text: 'Got it',
                      onPressed: () => Navigator.pop(context),
                      borderRadius: 8,
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 10,
              right: 10,
              child: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(
                  Icons.close,
                  color: AppColors.textGreyColor,
                  size: 24,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Show quota exhausted dialog
void showQuotaExhaustedDialog(
  BuildContext context, {
  int? recipeCreateGPT,
  int? recipeImageGPT,
  String? recipeStorageInMB,
  String? fileSizeInMB,
}) {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => QuotaExhaustedDialog(
      recipeCreateGPT: recipeCreateGPT,
      recipeImageGPT: recipeImageGPT,
      recipeStorageInMB: recipeStorageInMB,
      fileSizeInMB: fileSizeInMB,
    ),
  );
}

// import 'package:mastercookai/app/imports/core_imports.dart';
// import 'package:mastercookai/core/utils/device_utils.dart';
// import 'package:mastercookai/core/widgets/custom_button.dart';
// import 'package:mastercookai/core/widgets/custom_text.dart';
// import '../../app/imports/packages_imports.dart';

class RecipeQuotaExhaustedDialog extends StatelessWidget {
  final int totalRecipes;
  final int remainingRecipes;

  const RecipeQuotaExhaustedDialog({
    super.key,
    required this.totalRecipes,
    required this.remainingRecipes,
  });

  @override
  Widget build(BuildContext context) {
    final bool hasRemaining = remainingRecipes > 0;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getDeviceType(context).name == 'mobile'
            ? 0
            : getDeviceType(context).name == 'tablet'
                ? MediaQuery.of(context).size.width * 0.17
                : MediaQuery.of(context).size.width * 0.3,
      ),
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.white,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Illustration / Icon
                  Image.asset(
                    AssetsManager.premiumlock, // replace with your asset
                    height: 150,
                  ),
                  const SizedBox(height: 20),

                  // Title
                  CustomText(
                    text: "Recipe Quota Exhausted",
                    size: getDeviceType(context).name == 'mobile' ? 18 : 24,
                    weight: FontWeight.bold,
                    color: AppColors.blackTextColor,
                    align: TextAlign.center,
                  ),
                  const SizedBox(height: 10),

                  // Description
                  CustomText(
                    text:
                        "File contains $totalRecipes recipes and remaining recipes is $remainingRecipes",
                    size: getDeviceType(context).name == 'mobile' ? 14 : 16,
                    weight: FontWeight.w400,
                    color: AppColors.textGreyColor,
                    align: TextAlign.center,
                  ),
                  const SizedBox(height: 20),

                  // Buttons
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 35.0),
                    child: hasRemaining
                        ? Column(
                            children: [
                              CustomButton(
                                text: "Proceed with remaining quota",
                                onPressed: () {
                                  Navigator.pop(context, "proceed");
                                },
                                borderRadius: 8,
                              ),
                              // const SizedBox(height: 10),
                              // CustomButton(
                              //   text: "Skip",
                              //   onPressed: () {
                              //     Navigator.pop(context, "skip");
                              //   },
                              //   borderRadius: 8,
                              //   textColor: Colors.black,
                              // ),
                            ],
                          )
                        : CustomButton(
                            text: "Got it",
                            onPressed: () => Navigator.pop(context),
                            borderRadius: 8,
                          ),
                  ),
                ],
              ),
            ),

            // Close icon
            Positioned(
              top: 10,
              right: 10,
              child: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(
                  Icons.close,
                  color: AppColors.textGreyColor,
                  size: 24,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Show recipe quota exhausted dialog
Future<String?> showRecipeQuotaExhaustedDialog(
  BuildContext context, {
  required int totalRecipes,
  required int remainingRecipes,
}) {
  return showDialog<String>(
    context: context,
    barrierDismissible: true,
    builder: (context) => RecipeQuotaExhaustedDialog(
      totalRecipes: totalRecipes,
      remainingRecipes: remainingRecipes,
    ),
  );
}
