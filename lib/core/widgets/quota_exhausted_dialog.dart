import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../app/imports/packages_imports.dart';

class QuotaExhaustedDialog extends ConsumerWidget {
  final int recipeCreateGPT;
  final int recipeImageGPT;

  const QuotaExhaustedDialog({
    super.key,
    required this.recipeCreateGPT,
    required this.recipeImageGPT,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Determine the message and title based on quota values
    final bool hasRecipeQuota = recipeCreateGPT > 0;
    final bool hasImageQuota = recipeImageGPT > 0;
    final bool hasAnyQuota = hasRecipeQuota || hasImageQuota;

    String getTitle() {
      if (!hasAnyQuota) {
        return 'GPT Recipe Quota Exhausted';
      } else {
        return 'GPT Recipe Quota';
      }
    }

    String getMessage() {
      if (!hasRecipeQuota && !hasImageQuota) {
        return 'You have reached your monthly limits';
      } else {
        String message = 'Your current monthly usage:\n';
        if (hasRecipeQuota) {
          message += ' • Recipe Creation: $recipeCreateGPT remaining\n';
        } else {
          message += ' • Recipe Creation: Limit reached\n';
        }
        if (hasImageQuota) {
          message += ' • Recipe Image: $recipeImageGPT remaining';
        } else {
          message += ' • Recipe Image: Limit reached';
        }
        return message;
      }
    }

    return Container(
      margin: EdgeInsets.symmetric(
          horizontal: getDeviceType(context).name == 'mobile'
              ? 0
              : getDeviceType(context).name == 'tablet'
                  ? MediaQuery.of(context).size.width * 0.17
                  : MediaQuery.of(context).size.width * 0.3),
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.white,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Image / Illustration
                  Image.asset(
                    AssetsManager.premiumlock, // Replace with your asset
                    height: 150,
                  ),
                  const SizedBox(height: 20),

                  // Title
                  CustomText(
                    text: getTitle(),
                    size: getDeviceType(context).name == 'mobile' ? 18 : 24,
                    weight: FontWeight.bold,
                    color: AppColors.blackTextColor,
                    align: TextAlign.center,
                  ),
                  const SizedBox(height: 10),

                  // Description
                  CustomText(
                    text: getMessage(),
                    size: getDeviceType(context).name == 'mobile' ? 14 : 16,
                    weight: FontWeight.w400,
                    color: AppColors.textGreyColor,
                    align: TextAlign.center,
                  ),
                  const SizedBox(height: 20),

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 35.0),
                    child: CustomButton(
                     text: 'Got it',
                      onPressed: () => Navigator.pop(context),
                      borderRadius: 8,
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 10,
              right: 10,
              child: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(
                  Icons.close,
                  color: AppColors.textGreyColor,
                  size: 24,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Show quota exhausted dialog
void showQuotaExhaustedDialog(
  BuildContext context, {
  required int recipeCreateGPT,
  required int recipeImageGPT,
}) {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => QuotaExhaustedDialog(
      recipeCreateGPT: recipeCreateGPT,
      recipeImageGPT: recipeImageGPT,
    ),
  );
}
