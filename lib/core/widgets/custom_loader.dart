import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';



class CustomLoader extends ConsumerWidget {
  const CustomLoader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      color: Colors.black54,
      child: Center(
        child: LoadingAnimationWidget.fallingDot(
          color: Colors.white,
          size: 50.0,
        ),
      ),
    );
  }
}