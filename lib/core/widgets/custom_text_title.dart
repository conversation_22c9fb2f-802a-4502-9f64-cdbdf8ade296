import '../../app/imports/core_imports.dart';

class CustomTextTitle extends StatelessWidget {
  final String title;
  final double? size;
  final Color? textColor;
  const CustomTextTitle({super.key, required this.title, this.size , this.textColor});

  @override
  Widget build(BuildContext context) {
    return Text(title,
        style: context.theme.textTheme.displaySmall!.copyWith(
          color: textColor ?? AppColors.primaryGreyColor,
          fontWeight: FontWeight.w400,
          fontSize: size ?? headingThreeFontSize ,
        ));
  }
}
