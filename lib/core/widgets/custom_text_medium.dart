import '../../app/imports/core_imports.dart';

class CustomTextMedium extends StatelessWidget {
  final String title;
  final double? size;
  final Color? textColor;
  final int? maxLines;

  const CustomTextMedium({super.key, required this.title, this.size , this.textColor , this.maxLines});

  @override
  Widget build(BuildContext context) {
    return Text(title,
        maxLines: maxLines ,
        style: context.theme.textTheme.bodySmall!.copyWith(
          color: textColor ?? AppColors.textGreyColor,
          fontWeight: FontWeight.w400,
          fontSize: size ?? headingSixFontSize ,
        ));
  }
}
