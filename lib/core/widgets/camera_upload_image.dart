import 'package:flutter/cupertino.dart';

import '../../app/imports/core_imports.dart';

Widget cameraUploadImage({
  required VoidCallback onTap,
}) {
  return Align(
      alignment: Alignment.topRight,
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: CircleAvatar(
          backgroundColor: Colors.white.withValues(alpha: 0.8), // Light background for contrast
          radius: 20,
          child: IconButton(
            onPressed: onTap,
            icon: Icon(Icons.camera_alt_rounded, color: Colors.red),
            iconSize: 20,
          ),
        ),
      )
  );
}