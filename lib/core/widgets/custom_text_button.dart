import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../app/imports/core_imports.dart';
import '../utils/device_utils.dart';

class CustomTextButton extends StatefulWidget {
  final String text;
  final Color color;
  final double? size;
  final VoidCallback onPressed;
  final bool underline;
  final Color? hoverColor;

  const CustomTextButton({
    required this.text,
    required this.onPressed,
    required this.color,
    this.size,
    this.underline = false,
    this.hoverColor,
    super.key,
  });

  @override
  State<CustomTextButton> createState() => _CustomTextButtonState();
}

class _CustomTextButtonState extends State<CustomTextButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onPressed,
        child: Text(
          widget.text,
          style: theme.textTheme.labelMedium!.copyWith(
            fontSize: widget.size ?? responsiveFont(24).sp,
            color: _isHovered
                ? (widget.hoverColor ?? widget.color.withValues(alpha: 0.7))
                : widget.color,
            letterSpacing: 1,
            fontWeight: FontWeight.w400,
            decoration: widget.underline ? TextDecoration.underline : TextDecoration.none,
          ),
        ),
      ),
    );
  }
}
