import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../app/theme/colors.dart';

class CustomText extends StatelessWidget {
  final String? text;
  final double? size;
  final Color? color;
  final Color? decorationColor;
  final String? fontFamily;
  final FontWeight? weight;
  final TextAlign? align;
  final double? height;
  final double? decorationThickness;
  final int? maxLine;
  final TextDecoration? decoration;
  final TextOverflow? overflow;
  final bool? underline;

  const CustomText({
    Key? key,
    this.text,
    this.size,
    this.color,
    this.decorationColor,
    this.fontFamily,
    this.weight,
    this.align,
    this.maxLine,
    this.overflow,
    this.decoration,
    this.decorationThickness,
    this.height,
    this.underline = false, // Default to false for optional underline
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Combine underline with any provided decoration
    TextDecoration? finalDecoration = decoration;
    if (underline == true) {
      if (decoration == null) {
        finalDecoration = TextDecoration.underline;
      } else if (decoration != TextDecoration.underline) {
        finalDecoration = TextDecoration.combine([
          decoration!,
          TextDecoration.underline,
        ]);
      }
    }

    return Text(
      text!,
      textAlign: align,
      maxLines: maxLine,
      overflow: overflow,
      style: GoogleFonts.inter(
        fontSize: size ?? 14,
        color: color ?? AppColors.primaryColor,
        fontWeight: weight ?? FontWeight.normal,
        fontStyle: FontStyle.normal,
        decoration: finalDecoration,
        decorationColor: decorationColor ?? Colors.transparent,
        decorationThickness: decorationThickness ?? 0.0,
        height: height,
      ).copyWith(
        fontFamilyFallback: ['Inter-Regular', 'Inter-Regular'],
      ),
    );
  }
}