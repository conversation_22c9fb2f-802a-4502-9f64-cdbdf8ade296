import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/theme/colors.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/providers/recipe_clipper_cookbook_notifier.dart';

import '../../app/imports/packages_imports.dart';
import '../network/app_status.dart';

class ClipperDropDpwn extends ConsumerStatefulWidget {
  final String hint;
  final Cookbook? selectedValue;
  final List<Cookbook> items;
  final void Function(Cookbook?) onChanged;
  final Widget? icon;
  final AppState<List<Cookbook>> cookbookState;
  final void Function(bool)? onOpenStateChanged;

  const ClipperDropDpwn({
    super.key,
    required this.hint,
    required this.selectedValue,
    required this.items,
    required this.onChanged,
    this.icon,
    required this.cookbookState,
    this.onOpenStateChanged,
  });

  @override
  ConsumerState<ClipperDropDpwn> createState() => _CustomDropdownWithCreateState();
}

class _CustomDropdownWithCreateState extends ConsumerState<ClipperDropDpwn> {
  final TextEditingController _controller = TextEditingController();
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  bool _isOpen = false;
  List<Cookbook> _filteredItems = [];
  Timer? _debounce;
  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
    _controller.text = widget.selectedValue?.name ?? '';
    _controller.addListener(() {
      _filterItems(_controller.text);
    });
  }

  @override
  void didUpdateWidget(covariant ClipperDropDpwn oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.items != oldWidget.items) {
      _filteredItems = widget.items;
      if (_isOpen) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _overlayEntry?.markNeedsBuild();
        });
      }
    }
    if (widget.selectedValue != oldWidget.selectedValue) {
      final newText = widget.selectedValue?.name ?? widget.hint;
      if (_controller.text != newText) {
        _controller.text = newText;
      }
    }
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _overlayEntry?.remove();
    _overlayEntry = null;
    _controller.dispose();
    super.dispose();
  }

  void _filterItems(String query) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      ref.read(recipeClipperCookbookNotifierProvider.notifier).fetchCookbooks(
            context: context,
            loadMore: false,
            sort: 'Name',
            search: query,
          );
    });
  }

  void _toggleDropdown() {
    if (_isOpen) {
      _closeOverlay();
    } else {
      _openOverlay();
    }
  }

  void _openOverlay() {
    if (_isOpen) return;
    _updateOverlay(opening: true);
  }

  void _updateOverlay({bool opening = false}) {
    if (opening) {
      final renderBox = context.findRenderObject() as RenderBox;
      final size = renderBox.size;
      _overlayEntry = OverlayEntry(
        builder: (context) => GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: _closeOverlay,
          child: Stack(
            children: [
              // Positioned(
              //   width: size.width,
              //   height: 240,
              //   child: CompositedTransformFollower(
              //     link: _layerLink,
              //     showWhenUnlinked: false,
              //     offset: Offset(0, size.height + 5.0),
              //     child: Material(
              //       elevation: 4,
              //       borderRadius: BorderRadius.circular(8),
              //       child: GestureDetector(
              //         onTap: () {},
              //         child: Container(
              //           constraints: BoxConstraints(maxHeight: 200.h),
              //           decoration: BoxDecoration(
              //             color: Colors.white,
              //             borderRadius: BorderRadius.circular(8),
              //             border: Border.all(color: AppColors.lightestGreyColor),
              //           ),
              //           child: NotificationListener<ScrollNotification>(
              //             onNotification: (scrollNotification) {
              //               if (scrollNotification is ScrollEndNotification &&
              //                   scrollNotification.metrics.pixels == scrollNotification.metrics.maxScrollExtent) {
              //                 final cookbookState = ref.read(recipeClipperCookbookNotifierProvider);
              //                 if (cookbookState.hasMore && cookbookState.status != AppStatus.loadingMore) {
              //                   ref.read(recipeClipperCookbookNotifierProvider.notifier).fetchCookbooks(context: context, loadMore: true, sort: 'Name', search: '');
              //                 }
              //               }
              //               return false;
              //             },
              //             child: Builder(builder: (context) {
              //               if (widget.cookbookState.status == AppStatus.loading) {
              //                 return Center(
              //                   child: LoadingAnimationWidget.fallingDot(
              //                     color: Colors.black54,
              //                     size: 30.0,
              //                   ),
              //                 );
              //               }
              //               if (widget.items.isEmpty) {
              //                 return const Center(child: Text('No cookbooks found', style: TextStyle(fontSize: 18)));
              //               }
              //               return ListView.separated(
              //                 shrinkWrap: true,
              //                 itemCount: widget.items.length + (widget.cookbookState.hasMore ? 1 : 0),
              //                 separatorBuilder: (context, index) => Divider(height: 1),
              //                 itemBuilder: (context, index) {
              //                   if (index < widget.items.length) {
              //                     final item = widget.items[index];
              //                     return ListTile(
              //                       title: Text(
              //                         item.name,
              //                         style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              //                               fontSize: 16,
              //                               fontWeight: FontWeight.w400,
              //                               color: Colors.black,
              //                             ),
              //                       ),
              //                       onTap: () {
              //                         widget.onChanged(item);
              //                         _controller.text = item.name;
              //                         _closeOverlay();
              //                       },
              //                     );
              //                   } else {
              //                     return Center(
              //                       child: Padding(
              //                         padding: EdgeInsets.symmetric(vertical: 8.h),
              //                         child: LoadingAnimationWidget.fallingDot(
              //                           color: Colors.black54,
              //                           size: 30.0,
              //                         ),
              //                       ),
              //                     );
              //                   }
              //                 },
              //               );
              //             }),
              //           ),
              //         ),
              //       ),
              //     ),
              //   ),
              // ),
             Positioned(
                width: size.width,
                child: CompositedTransformFollower(
                  link: _layerLink,
                  showWhenUnlinked: false,
                  offset: Offset(0, size.height + 5.0),
                  child: Material(
                    elevation: 4,
                    borderRadius: BorderRadius.circular(8),
                    child: SizedBox(  // Use SizedBox instead of Container with constraints
                      height: widget.items.length == 1 
                          ? null  // Auto height for single item
                          : 240,  // Fixed height for multiple items
                      width: size.width,
                      child: NotificationListener<ScrollNotification>(
                        onNotification: (scrollNotification) {
                          if (scrollNotification is ScrollEndNotification &&
                              scrollNotification.metrics.pixels == 
                              scrollNotification.metrics.maxScrollExtent) {
                            final cookbookState = ref.read(recipeClipperCookbookNotifierProvider);
                            if (cookbookState.hasMore && cookbookState.status != AppStatus.loadingMore) {
                              ref.read(recipeClipperCookbookNotifierProvider.notifier)
                                  .fetchCookbooks(context: context, loadMore: true, sort: 'Name', search: '');
                            }
                          }
                          return false;
                        },
                        child: Builder(builder: (context) {
                          if (widget.cookbookState.status == AppStatus.loading) {
                            return Center(
                              child: LoadingAnimationWidget.fallingDot(
                                color: Colors.black54,
                                size: 30.0,
                              ),
                            );
                          }
                          if (widget.items.isEmpty) {
                            return const Center(child: Text('No cookbooks found'));
                          }
                          
                          return ListView.separated(
                            padding: EdgeInsets.zero,
                            shrinkWrap: widget.items.length == 1,  // Only shrinkWrap for single item
                            physics: widget.items.length == 1
                                ? NeverScrollableScrollPhysics()  // No scroll for single item
                                : const AlwaysScrollableScrollPhysics(),  // Scroll for multiple
                            itemCount: widget.items.length + (widget.cookbookState.hasMore ? 1 : 0),
                            separatorBuilder: (context, index) => Divider(height: 1),
                            itemBuilder: (context, index) {
                              if (index < widget.items.length) {
                                final item = widget.items[index];
                                return ListTile(
                                  dense: true,
                                  contentPadding: EdgeInsets.symmetric(horizontal: 16.0),
                                  title: Text(
                                    item.name,
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                        ),
                                  ),
                                  onTap: () {
                                    widget.onChanged(item);
                                    _controller.text = item.name;
                                    _closeOverlay();
                                  },
                                );
                              } else {
                                return Center(
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(vertical: 8.0),
                                    child: LoadingAnimationWidget.fallingDot(
                                      color: Colors.black54,
                                      size: 30.0,
                                    ),
                                  ),
                                );
                              }
                            },
                          );
                        }),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
      Overlay.of(context).insert(_overlayEntry!);
      if (mounted) {
        setState(() {
          _isOpen = true;
          widget.onOpenStateChanged?.call(true);
        });
      }
    } else if (_isOpen) {
      _overlayEntry?.markNeedsBuild();
    }
  }

  void _closeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    if (mounted) {
      setState(() {
        _isOpen = false;
        widget.onOpenStateChanged?.call(false);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextFormField(
        controller: _controller,
        onTap: _openOverlay,
        decoration: InputDecoration(
          isDense: true,
          contentPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12),
          fillColor: Colors.white,
          filled: true,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
            borderSide: BorderSide(color: AppColors.lightestGreyColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
            borderSide: BorderSide(color: Colors.blue),
          ),
          hintText: widget.hint,
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: Colors.grey,
              ),
          suffixIcon: widget.icon ??
              GestureDetector(
                onTap: _toggleDropdown,
                child: Icon(_isOpen ? Icons.arrow_drop_up : Icons.arrow_drop_down),
              ),
          suffixIconConstraints: BoxConstraints(
            minHeight: 24,
            minWidth: 40,
          ),
        ),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: Colors.black,
            ),
      ),
    );
  }
}
