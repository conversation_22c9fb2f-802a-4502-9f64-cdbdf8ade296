import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../app/imports/core_imports.dart';
import 'custom_appbar.dart';

class ComingSoonScreen extends StatelessWidget {
  final String title;
  final String message;

  const ComingSoonScreen({
    super.key,
    this.title = 'Coming Soon',
    this.message =
    'This feature is under development and will be available soon. Stay tuned!',
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: "Coming Soon"),
      body: Stack(
        fit: StackFit.expand,
        children: [
          // Background Image
          Image.asset(
            AssetsManager.background_img,
            fit: BoxFit.cover,
          ),

          // Card with content
          Center(
            child: SizedBox(
              height: 450.h, // Adjust height as needed
              child: Card(
                elevation: 8,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.r),
                ),
                color: Colors.white.withOpacity(0.9),
                child: Padding(
                  padding: EdgeInsets.all(24.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Icon(
                        Icons.construction,
                        size: 70.sp,
                        color: Theme.of(context).primaryColor,
                      ),
                      SizedBox(height: 24.h),
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        message,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),        ],
      ),
    );
  }
}
