import 'package:flutter/material.dart';

class CustomBottomSheet<T> {
  final BuildContext context;
  final T? data;
  final double? height;
  final bool isDismissible;
  final bool isScrollable;
  final VoidCallback? onDismiss;
  final Widget Function(BuildContext) builder;
  final Widget? title;

  CustomBottomSheet({
    required this.context,
    required this.builder,
    this.data,
    this.height,
    this.isDismissible = true,
    this.isScrollable = true,
    this.onDismiss,
    this.title,
  });

  void show() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: isDismissible,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: height != null
              ? (height! / MediaQuery.of(context).size.height)
              : 0.5,
          minChildSize: 0.1,
          maxChildSize: 0.9,
          expand: false,
          builder: (BuildContext context, ScrollController scrollController) {
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
              ),
              child: Column(
                children: [
                  // Drag handle
                  Container(
                    margin: const EdgeInsets.only(top: 10.0),
                    width: 40.0,
                    height: 5.0,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2.5),
                    ),
                  ),
                  // Optional title
                  if (title != null)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 16.0,
                        horizontal: 16.0,
                      ),
                      child: DefaultTextStyle(
                        style: const TextStyle(
                          fontSize: 18.0,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        child: title!,
                      ),
                    ),
                  // Content
                  Expanded(
                    child: isScrollable
                        ? SingleChildScrollView(
                      controller: scrollController,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: builder(context),
                      ),
                    )
                        : Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: builder(context),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    ).whenComplete(() {
      if (onDismiss != null) onDismiss!();
    });
  }
}
