import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../app/imports/core_imports.dart';
import '../../core/utils/device_utils.dart';

class CustomAppBarWithDrawer extends StatelessWidget
    implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final List<DrawerMenuItem> menuItems;

  const CustomAppBarWithDrawer({
    super.key,
    required this.title,
    this.actions,
    this.menuItems = const [],
  });

  @override
  Size get preferredSize => Size.fromHeight(60.h);

  @override
  Widget build(BuildContext context) {
    final scaffoldKey = GlobalKey<ScaffoldState>();
    final screenSize = MediaQuery.of(context).size;
    final isTablet = DeviceUtils().isTabletOrIpad(context);

    return Scaffold(
      key: scaffoldKey,
      appBar: AppBar(
        title: Text(
          title,
          style: TextStyle(
            fontSize: isTablet ? 24.sp : 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        elevation: 2,
        actions: actions,
        leading: IconButton(
          icon: Icon(
            Icons.menu,
            size: isTablet ? 28.r : 20.r,
          ),
          onPressed: () => scaffoldKey.currentState?.openDrawer(),
        ),
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
              ),
              child: Center(
                child: Text(
                  'Menu',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isTablet ? 28.sp : 20.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            ...menuItems.map((item) => ListTile(
                  leading: Icon(
                    item.icon,
                    size: isTablet ? 24.r : 18.r,
                  ),
                  title: Text(
                    item.title,
                    style: TextStyle(
                      fontSize: isTablet ? 18.sp : 14.sp,
                    ),
                  ),
                  onTap: () {
                    item.onTap();
                    Navigator.pop(context); // Close drawer
                  },
                )),
          ],
        ),
      ),
      body:
          const SizedBox.shrink(), // Placeholder, to be replaced by the parent
    );
  }
}

// Model for drawer menu items
class DrawerMenuItem {
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  const DrawerMenuItem({
    required this.title,
    required this.icon,
    required this.onTap,
  });
}
