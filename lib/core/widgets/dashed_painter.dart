// Custom Dashed Painter for DottedBorder (if not already defined elsewhere)
import 'dart:ui';

import '../../app/imports/core_imports.dart';

class DashedPainter extends CustomPainter {
  final double strokeWidth;
  final List<double> dashPattern;
  final Color color;
  final StrokeCap strokeCap;
  final Radius radius;

  DashedPainter({
    required this.strokeWidth,
    required this.dashPattern,
    required this.color,
    required this.strokeCap,
    required this.radius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = strokeCap;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
          Rect.fromLTWH(0, 0, size.width, size.height), radius));

    final dashPath = _dashPath(path, dashPattern);
    canvas.drawPath(dashPath, paint);
  }

  Path _dashPath(Path source, List<double> dashPattern) {
    final dashedPath = Path();
    final dashLength = dashPattern[0];
    final gapLength = dashPattern[1];
    var distance = 0.0;

    for (PathMetric pathMetric in source.computeMetrics()) {
      while (distance < pathMetric.length) {
        dashedPath.addPath(
          pathMetric.extractPath(distance, distance + dashLength),
          Offset.zero,
        );
        distance += dashLength + gapLength;
      }
    }
    return dashedPath;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}