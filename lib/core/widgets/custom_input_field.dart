import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_remix/flutter_remix.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../app/theme/colors.dart';
import '../utils/device_utils.dart';

class CustomInputField extends StatefulWidget {
  final String hintText;
  final TextEditingController? controller;
  final String? iconPath;
  final Color? iconColor;
  final TextInputAction? textInputAction;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final bool editable;
  final void Function(String)? onChanged;
  final bool isPassword;
  final bool isMoreLines;
  final double passwordIconSize;
  final double verticalPadding;
  final int? maxLength;
  final String prefix;
  final bool addPrefix;
  final VoidCallback? onClear;
  final bool isUpdateMode;
  final int? maxLines;
  final double borderRadius;
  final double fontSize;

  const CustomInputField({
    super.key,
    required this.hintText,
    this.controller,
    this.iconPath,
    this.iconColor,
    this.textInputAction,
    this.keyboardType,
    this.inputFormatters,
    this.validator,
    this.verticalPadding = 12,
    this.editable = true,
    this.isMoreLines = false,
    this.onChanged,
    this.isPassword = false,
    this.passwordIconSize = 20,
    this.maxLength,
    this.prefix = "+",
    this.addPrefix = false,
    this.onClear,
    this.isUpdateMode = false,
    this.maxLines,
    this.borderRadius = 8.0,
    this.fontSize = 14.0,
  });

  @override
  _CustomTextInputState createState() => _CustomTextInputState();
}

class _CustomTextInputState extends State<CustomInputField> {
  bool _isObscured = true;

  @override
  Widget build(BuildContext context) {
    // Determine if the input is multi-line
    final isMultiLine = widget.isMoreLines
        ? (widget.maxLines ?? 1) > 1
        : widget.maxLines != null && widget.maxLines! > 1;

    return TextFormField(
      controller: widget.controller,
      cursorColor: AppColors.primaryLightTextColor,
      textInputAction: isMultiLine
          ? TextInputAction.newline // Allow Enter to create new line
          : widget.textInputAction ?? TextInputAction.done,
      style: TextStyle(
        color: AppColors.primaryLightTextColor,
        fontSize: widget.fontSize,
        fontFamily: "Inter-Regular",
        fontWeight: FontWeight.w400,
      ),
      decoration: InputDecoration(
        hintText: widget.hintText,
        hintStyle: GoogleFonts.inter(
          fontSize: widget.fontSize ?? 14,
          color: AppColors.primaryLightTextColor,
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
        ).copyWith(
          fontFamilyFallback: ['Inter-Regular', 'Inter-Regular'],
        ),
        // TextStyle(
        //   color: AppColors.primaryLightTextColor,
        //   fontSize: widget.fontSize.sp,
        //   fontFamily: "Inter-Regular",
        //   fontWeight: FontWeight.w400,
        // ),
        contentPadding: EdgeInsets.symmetric(
          vertical: isMultiLine ? 12 : widget.verticalPadding,
          horizontal: 12,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          borderSide: const BorderSide(
            color: AppColors.primaryBorderColor,
            width: 2,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          borderSide: BorderSide(
            color: Colors.grey[300]!,
            width: 2,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          borderSide: BorderSide(
            color: Colors.grey[300]!,
            width: 2,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          borderSide: const BorderSide(
            color: AppColors.primaryBorderColor,
            width: 2,
          ),
        ),
        isDense: true,
        // Keep the field compact
        suffixIcon: _buildSuffixIcon(context),
        errorStyle: GoogleFonts.inter(
          fontSize: 12, // Change this size as needed
          color: Colors.red,
          fontWeight: FontWeight.w400,
        ),
      ),
      enabled: widget.editable,
      keyboardType: isMultiLine
          ? TextInputType.multiline // Support multi-line input
          : widget.keyboardType ?? TextInputType.text,
      inputFormatters: widget.inputFormatters ?? [],
      validator: widget.validator,
      onChanged: (value) {
        String newValue = value;
        if (widget.addPrefix) {
          if (newValue.isNotEmpty && !newValue.startsWith(widget.prefix)) {
            newValue = "${widget.prefix}$newValue";
            // Only update controller if we need to add prefix
            if (widget.controller != null) {
              final currentSelection = widget.controller!.selection;
              widget.controller!.value = widget.controller!.value.copyWith(
                text: newValue,
                selection: TextSelection.collapsed(offset: currentSelection.baseOffset + widget.prefix.length),
              );
            }
          }
        }

        if (widget.onChanged != null) {
          widget.onChanged!(newValue);
        }

        setState(() {}); // Trigger rebuild to update suffix icon visibility
      },
      textCapitalization: TextCapitalization.words,
      obscureText: widget.isPassword ? _isObscured : false,
      maxLength: widget.maxLength,
      maxLines: widget.isMoreLines
          ? widget.maxLines ?? 1 // Use provided maxLines for direction input
          : widget.maxLines == null
              ? 1
              : widget.maxLines == 1
                  ? 1
                  : 3,
      // Limit to 3 for non-direction multi-line inputs
      minLines: widget.isMoreLines
          ? widget.maxLines ?? 1 // Use provided maxLines for direction input
          : widget.maxLines ?? 1,
      // Use provided maxLines for non-direction inputs
      // maxLines: widget.maxLines == null ? 1 : widget.maxLines == 1 ? 1 : 3, // Limit to 3 visible lines for multi-line
      // minLines: widget.maxLines == null ? 1 : widget.maxLines == 1 ? 1 : widget.maxLines, // Start with specified lines
      // minLines: (widget.isPassword && _isObscured) ? 1 : (widget.maxLines == null ? 1 : widget.maxLines == 1 ? 1 : widget.maxLines),
      buildCounter: (context,
              {required currentLength, required isFocused, maxLength}) =>
          null,
    );
  }

  Widget? _buildSuffixIcon(BuildContext context) {
    List<Widget> icons = [];

    // Add password toggle icon if isPassword is true
    if (widget.isPassword) {
      icons.add(
        IconButton(
          padding: EdgeInsets.zero, // Remove default padding
          icon: Icon(
            _isObscured ? FlutterRemix.eye_off_fill : FlutterRemix.eye_fill,
            color: context.theme.disabledColor,
            size: widget.passwordIconSize,
          ),
          onPressed: () {
            setState(() {
              _isObscured = !_isObscured;
            });
          },
        ),
      );
    } else {
      // Add clear button if text is present and not a password field
      if (widget.editable &&
          widget.controller != null &&
          widget.controller!.text.isNotEmpty) {
        icons.add(
          IconButton(
            padding: EdgeInsets.zero, // Remove default padding
            icon: Icon(
              FlutterRemix.close_circle_fill,
              color: context.theme.disabledColor,
              size: widget.passwordIconSize,
            ),
            onPressed: () {
              widget.controller!.clear();
              if (widget.onClear != null) {
                widget.onClear!();
              }
              setState(() {}); // Update UI to hide clear button
            },
          ),
        );
      }
    }

    // Return null if no icons
    if (icons.isEmpty) {
      return null;
    }

    // Constrain the suffix icon area to avoid extra vertical padding
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: widget.passwordIconSize.sp, // Match icon size
        maxWidth: (widget.passwordIconSize + 8)
            .sp, // Add minimal padding for touch area
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        // Center icons horizontally
        children: icons,
      ),
    );
  }
}
