
import '../../app/imports/core_imports.dart';
import '../../app/imports/packages_imports.dart';

class LoadingIndicator extends StatelessWidget {
  const LoadingIndicator({
    super.key,
    this.size = 20,
    this.strokeWidth = 2,
    this.color,
    this.backgroundColor,
    this.center = true,
  });

  final double size;
  final double strokeWidth;
  final Color? color;
  final Color? backgroundColor;
  final bool center;

  @override
  Widget build(BuildContext context) {
    final indicator = SizedBox(
      height: size.h,
      width: size.h,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? context.theme.primaryColor,
        ),
        backgroundColor: backgroundColor,
      ),
    );

    return center ? Center(child: indicator) : indicator;
  }
}
