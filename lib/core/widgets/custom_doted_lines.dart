import 'package:flutter/material.dart';

class DottedLinePainter extends CustomPainter {
  final double strokeWidth;
  final double dashWidth;
  final Color color;

  DottedLinePainter({
    this.strokeWidth = 2,
    this.dashWidth = 6,
    this.color = Colors.black,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth * 2; // space between dots
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

