import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../app/imports/core_imports.dart';
import '../../core/utils/device_utils.dart';
import '../../core/utils/screen_sizer.dart';

class CommonGridView<T> extends StatelessWidget {
  final List<T> list;
  final int itemCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final int crossAxisCount;
  final double childAspectRatio;
  final Widget Function(BuildContext, int) itemBuilder;

  const CommonGridView({
    super.key,
    required this.list,
    required this.itemCount,
    required this.mainAxisSpacing,
    required this.crossAxisSpacing,
    required this.crossAxisCount,
    required this.childAspectRatio,
    required this.itemBuilder,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = DeviceUtils().isTabletOrIpad(context);

    // Adjust crossAxisCount for different platforms
    final effectiveCrossAxisCount = isTablet
        ? 3 // 3 items per row for tablets/iPads
        :   (screenSize.width > 1200 ? 5 : 4); // 5 for large screens, 4 for medium
            // 4 for phones > 600px, 2 for smaller

    // Adjust childAspectRatio to prevent overflow
    final effectiveChildAspectRatio = isTablet
        ? 0.959 // Ensures sufficient height for tablets (~264px content)
        :   ScreenSizer().calculateChildAspectRatio(
                screenSize.width,
                screenSize.height,
              ); // Use ScreenSizer for phones

    // Responsive spacing
    final effectiveMainAxisSpacing = mainAxisSpacing.h *
        (isTablet ? 0.04   : 0.01);
    final effectiveCrossAxisSpacing = crossAxisSpacing.w *
        (isTablet ? 0.04  : 0.01);

    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: screenSize.width * (isTablet ? 0.007 : 0.03),
          vertical: screenSize.width * (isTablet ? 0.007 : 0.03),
        ),
        child: SingleChildScrollView(
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount != 0
                  ? crossAxisCount
                  : effectiveCrossAxisCount,
              mainAxisSpacing: mainAxisSpacing != 0
                  ? mainAxisSpacing.h
                  : effectiveMainAxisSpacing,
              crossAxisSpacing: crossAxisSpacing != 0
                  ? crossAxisSpacing.w
                  : effectiveCrossAxisSpacing,
              childAspectRatio: childAspectRatio != 0
                  ? childAspectRatio
                  : effectiveChildAspectRatio,
            ),
            itemCount: itemCount != 0 ? itemCount : list.length,
            itemBuilder: itemBuilder,
          ),
        ),
      ),
    );
  }
}