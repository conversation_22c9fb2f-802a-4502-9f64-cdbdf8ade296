import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

final loadingProvider = StateProvider<bool>((ref) => false);

class CustomLoading extends ConsumerWidget {
  const CustomLoading({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = ref.watch(loadingProvider);
    return Visibility(
      visible: isLoading,
      child: Container(
        color: Colors.black54,
        child: Center(
          child: LoadingAnimationWidget.fallingDot(
            color: Colors.white,
            size: 50.0,
          ),
        ),
      ),
    );
  }
}