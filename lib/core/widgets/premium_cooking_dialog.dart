import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/profile/myAccount.dart';

import '../../app/imports/packages_imports.dart';
import '../../presentation/profile/mobileui/subscription_view_mobile.dart';

class PremiumCookingDialog extends ConsumerWidget {
  const PremiumCookingDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(
          horizontal: getDeviceType(context).name == 'mobile'
              ? 0
              : getDeviceType(context).name == 'tablet'
                  ? MediaQuery.of(context).size.width * 0.17
                  : MediaQuery.of(context).size.width * 0.3),
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.white,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Image / Illustration
                  Image.asset(
                    AssetsManager.premiumlock, // Replace with your asset
                    height: 150,
                  ),
                  const SizedBox(height: 20),

                  // Title
                  const CustomText(
                    text: "Unlock Premium Cooking Magic!",
                    align: TextAlign.center,
                    size: 20,
                    color: AppColors.primaryLightTextColor,
                    weight: FontWeight.w600,
                  ),
                  const SizedBox(height: 8),

                  // Subtitle
                  const CustomText(
                    text:
                        "Don’t miss out on AI-powered cooking magic.\nUpgrade now to enjoy:",
                    align: TextAlign.center,
                    weight: FontWeight.w400,
                    size: 12.4,
                    color: AppColors.primaryLightTextColor,
                  ),
                  const SizedBox(height: 16),

                  // Gradient Feature Box
                  Image.asset(
                    AssetsManager.premiumbanner, // Replace with your asset
                    width: MediaQuery.of(context).size.width,
                  ),
                  const SizedBox(height: 20),

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 35.0),
                    child: CustomButton(
                      text: 'Upgrade to Unlock Features',
                      onPressed: () {
                        Navigator.pop(context);
                        if (getDeviceType(context).name == 'mobile') {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const SubscriptionViewMobile()),
                          );
                        } else {
                          //context.go("${Routes.myAccount}?tab=1");
                           Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const Myaccount(initialTabIndex: "1")),
                          );
                        }
                      },
                      borderRadius: 8,
                    ),
                  ),
                  // Upgrade Button

                  // Maybe Later
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const CustomText(
                      text: "Maybe later",
                      underline: true,
                      decorationThickness: 1,
                      decorationColor: Colors.grey,
                      decoration: TextDecoration.underline,
                      color: AppColors.primaryLightTextColor,
                      weight: FontWeight.w400,
                      size: 14,
                    ),
                  ),
                ],
              ),
            ),

            // Close Icon
            Positioned(
                right: 8,
                top: 8,
                child: Align(
                  alignment: Alignment.topRight,
                  child: IconButton(
                    icon: const Icon(IconsaxPlusBold.close_circle,
                        color: Colors.red),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
