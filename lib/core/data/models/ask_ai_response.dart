import 'package:mastercookai/core/data/models/recipe_detail_response.dart';

class AskAiResponse {
  final bool success;
  final Message message;
  final ThreadData data;
  final int status;

  AskAiResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory AskAiResponse.fromJson(Map<String, dynamic> json) {
    return AskAiResponse(
      success: json['success'],
      message: Message.fromJson(json['message']),
      data: ThreadData.fromJson(json['data']),
      status: json['status'],
    );
  }
}

class Message {
  final String? error;
  final List<String> general;

  Message({
    required this.error,
    required this.general,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: json['error'],
      general: List<String>.from(json['general']),
    );
  }
}

class ThreadData {
  final int threadId;
  final int messageId;
  final String prompt;
  final String response;
  final DateTime askedAt;
  final String? imageUrl; 
  final RecipeDetails? recipeDetails;

  ThreadData({
    required this.threadId,
    required this.messageId,
    required this.prompt,
    required this.response,
    required this.askedAt,
    this.imageUrl,
    this.recipeDetails,
  });

  factory ThreadData.fromJson(Map<String, dynamic> json) {
    return ThreadData(
      threadId: json['threadId'],
      messageId : json['messageId'],
      prompt: json['prompt'],
      response: json['response'],
      askedAt: DateTime.parse(json['askedAt']),
      imageUrl: json['imageUrl'],
      recipeDetails: json['recipeDetails'] != null
          ? RecipeDetails.fromJson(json['recipeDetails'] as Map<String, dynamic>)
          : null,
    );
  }
}
