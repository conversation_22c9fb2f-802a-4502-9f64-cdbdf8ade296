import 'base_response.dart';

class PlansResponse {
  PlansResponse({
    bool? success,
    Message? message,
    PlansData? data,
    int? status,
  }) {
    _success = success;
    _message = message;
    _data = data;
    _status = status;
  }

  PlansResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? PlansData.fromJson(json['data']) : null;
    _status = json['status'];
  }
  
  bool? _success;
  Message? _message;
  PlansData? _data;
  int? _status;
  
  PlansResponse copyWith({
    bool? success,
    Message? message,
    PlansData? data,
    int? status,
  }) => PlansResponse(
    success: success ?? _success,
    message: message ?? _message,
    data: data ?? _data,
    status: status ?? _status,
  );
  
  bool? get success => _success;
  Message? get message => _message;
  PlansData? get data => _data;
  int? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }
}

class PlansData {
  PlansData({
    List<Plan>? plans,
  }) {
    _plans = plans;
  }

  PlansData.fromJson(dynamic json) {
    if (json['plans'] != null) {
      _plans = [];
      json['plans'].forEach((v) {
        _plans?.add(Plan.fromJson(v));
      });
    }
  }
  
  List<Plan>? _plans;
  
  PlansData copyWith({
    List<Plan>? plans,
  }) => PlansData(
    plans: plans ?? _plans,
  );
  
  List<Plan>? get plans => _plans;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_plans != null) {
      map['plans'] = _plans?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class Plan {
  Plan({
    int? id,
    String? planName,
    dynamic description,
    dynamic monthlyFee,
    String? yearlyFee,
    List<Feature>? features,
  }) {
    _id = id;
    _planName = planName;
    _description = description;
    _monthlyFee = monthlyFee;
    _yearlyFee = yearlyFee;
    _features = features;
  }

  Plan.fromJson(dynamic json) {
    _id = json['id'];
    _planName = json['planName'];
    _description = json['description'];
    _monthlyFee = json['monthlyFee'];
    _yearlyFee = json['yearlyFee'];
    if (json['features'] != null) {
      _features = [];
      json['features'].forEach((v) {
        _features?.add(Feature.fromJson(v));
      });
    }
  }
  
  int? _id;
  String? _planName;
  dynamic _description;
  dynamic _monthlyFee;
  String? _yearlyFee;
  List<Feature>? _features;
  
  Plan copyWith({
    int? id,
    String? planName,
    dynamic description,
    dynamic monthlyFee,
    String? yearlyFee,
    List<Feature>? features,
  }) => Plan(
    id: id ?? _id,
    planName: planName ?? _planName,
    description: description ?? _description,
    monthlyFee: monthlyFee ?? _monthlyFee,
    yearlyFee: yearlyFee ?? _yearlyFee,
    features: features ?? _features,
  );
  
  int? get id => _id;
  String? get planName => _planName;
  dynamic get description => _description;
  dynamic get monthlyFee => _monthlyFee;
  String? get yearlyFee => _yearlyFee;
  List<Feature>? get features => _features;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['planName'] = _planName;
    map['description'] = _description;
    map['monthlyFee'] = _monthlyFee;
    map['yearlyFee'] = _yearlyFee;
    if (_features != null) {
      map['features'] = _features?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class Feature {
  Feature({
    int? id,
    String? featureName,
  }) {
    _id = id;
    _featureName = featureName;
  }

  Feature.fromJson(dynamic json) {
    _id = json['id'];
    _featureName = json['featureName'];
  }
  
  int? _id;
  String? _featureName;
  
  Feature copyWith({
    int? id,
    String? featureName,
  }) => Feature(
    id: id ?? _id,
    featureName: featureName ?? _featureName,
  );
  
  int? get id => _id;
  String? get featureName => _featureName;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['featureName'] = _featureName;
    return map;
  }
}

