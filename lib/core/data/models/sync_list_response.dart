class SyncListResponse {
  SyncListResponse({
      bool? success, 
      Message? message,
    SyncListResponseData? data,
      int? status,}){
    _success = success;
    _message = message;
    _data = data;
    _status = status;
}

  SyncListResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? SyncListResponseData.fromJson(json['data']) : null;
    _status = json['status'];
  }
  bool? _success;
  Message? _message;
  SyncListResponseData? _data;
  int? _status;
SyncListResponse copyWith({  bool? success,
  Message? message,
  SyncListResponseData? data,
  int? status,
}) => SyncListResponse(  success: success ?? _success,
  message: message ?? _message,
  data: data ?? _data,
  status: status ?? _status,
);
  bool? get success => _success;
  Message? get message => _message;
  SyncListResponseData? get data => _data;
  int? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }

}

class SyncListResponseData {
  SyncListResponseData({
      List<Cookbooks>? cookbooks, 
      List<ShoppingLists>? shoppingLists,}){
    _cookbooks = cookbooks;
    _shoppingLists = shoppingLists;
}

  SyncListResponseData.fromJson(dynamic json) {
    if (json['cookbooks'] != null) {
      _cookbooks = [];
      json['cookbooks'].forEach((v) {
        _cookbooks?.add(Cookbooks.fromJson(v));
      });
    }
    if (json['shoppingLists'] != null) {
      _shoppingLists = [];
      json['shoppingLists'].forEach((v) {
        _shoppingLists?.add(ShoppingLists.fromJson(v));
      });
    }
  }
  List<Cookbooks>? _cookbooks;
  List<ShoppingLists>? _shoppingLists;
  SyncListResponseData copyWith({  List<Cookbooks>? cookbooks,
  List<ShoppingLists>? shoppingLists,
}) => SyncListResponseData(  cookbooks: cookbooks ?? _cookbooks,
  shoppingLists: shoppingLists ?? _shoppingLists,
);
  List<Cookbooks>? get cookbooks => _cookbooks;
  List<ShoppingLists>? get shoppingLists => _shoppingLists;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_cookbooks != null) {
      map['cookbooks'] = _cookbooks?.map((v) => v.toJson()).toList();
    }
    if (_shoppingLists != null) {
      map['shoppingLists'] = _shoppingLists?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class ShoppingLists {
  ShoppingLists({
      int? id, 
      String? name, 
      int? shoppingItemCount,}){
    _id = id;
    _name = name;
    _shoppingItemCount = shoppingItemCount;
}

  ShoppingLists.fromJson(dynamic json) {
    _id = json['id'];
    _name = json['name'];
    _shoppingItemCount = json['shoppingItemCount'];
  }
  int? _id;
  String? _name;
  int? _shoppingItemCount;
ShoppingLists copyWith({  int? id,
  String? name,
  int? shoppingItemCount,
}) => ShoppingLists(  id: id ?? _id,
  name: name ?? _name,
  shoppingItemCount: shoppingItemCount ?? _shoppingItemCount,
);
  int? get id => _id;
  String? get name => _name;
  int? get shoppingItemCount => _shoppingItemCount;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['name'] = _name;
    map['shoppingItemCount'] = _shoppingItemCount;
    return map;
  }

}

class Cookbooks {
  Cookbooks({
      int? id, 
      String? name, 
      int? recipeCount,}){
    _id = id;
    _name = name;
    _recipeCount = recipeCount;
}

  Cookbooks.fromJson(dynamic json) {
    _id = json['id'];
    _name = json['name'];
    _recipeCount = json['recipeCount'];
  }
  int? _id;
  String? _name;
  int? _recipeCount;
Cookbooks copyWith({  int? id,
  String? name,
  int? recipeCount,
}) => Cookbooks(  id: id ?? _id,
  name: name ?? _name,
  recipeCount: recipeCount ?? _recipeCount,
);
  int? get id => _id;
  String? get name => _name;
  int? get recipeCount => _recipeCount;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['name'] = _name;
    map['recipeCount'] = _recipeCount;
    return map;
  }

}

class Message {
  Message({
      dynamic error, 
      List<String>? general,}){
    _error = error;
    _general = general;
}

  Message.fromJson(dynamic json) {
    _error = json['error'];
    _general = json['general'] != null ? json['general'].cast<String>() : [];
  }
  dynamic _error;
  List<String>? _general;
Message copyWith({  dynamic error,
  List<String>? general,
}) => Message(  error: error ?? _error,
  general: general ?? _general,
);
  dynamic get error => _error;
  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }

}