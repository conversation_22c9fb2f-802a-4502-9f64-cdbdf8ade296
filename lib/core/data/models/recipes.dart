import 'dart:convert';

import 'package:mastercookai/core/data/models/recipe_detail_response.dart';

RecipesResponse recipeResponseFromJson(String str) =>
    RecipesResponse.fromJson(json.decode(str));

String recipeResponseToJson(RecipesResponse data) =>
    json.encode(data.toJson());

class RecipesResponse {
  final bool success;
  final Message message;
  final RecipeData? data;
  final int status;

  RecipesResponse({
    required this.success,
    required this.message,
    this.data,
    required this.status,
  });

  factory RecipesResponse.fromJson(Map<String, dynamic> json) => RecipesResponse(
    success: json["success"] ?? false,
    message: Message.fromJson(json["message"] ?? {}),
    data: json["data"] != null ? RecipeData.fromJson(json["data"]) : null,
    status: json["status"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "message": message.toJson(),
    "data": data?.toJson(),
    "status": status,
  };
}

class Message {
  final dynamic error;
  final List<String> general;

  Message({
    this.error,
    required this.general,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
    error: json["error"],
    general: List<String>.from(json["general"] ?? []),
  );

  Map<String, dynamic> toJson() => {
    "error": error,
    "general": general,
  };
}

class RecipeData {
  final RecipeDetails? recipeDetails;

  RecipeData({
    this.recipeDetails,
  });

  factory RecipeData.fromJson(Map<String, dynamic> json) => RecipeData(
    recipeDetails: json["recipeDetails"] != null
        ? RecipeDetails.fromJson(json["recipeDetails"])
        : null,
  );

  Map<String, dynamic> toJson() => {
    "recipeDetails": recipeDetails?.toJson(),
  };
}

class RecipeDetails {
  final int id;
  final String name;
  final String description;
  final int? categoryId;
  final String? category;
  final int? cuisineId;
  final String? cuisine;
  final String? yield;
  final String? yieldUnit;
  final int? servings;
  final String? prepTime;
  final String? totalTime;
  final String? cookTime;
  final String? author;
  final int? authorMediaFileId;
  final String? authorMediaUrl;
  final String? source;
  final String? copyright;
  final String? notes;
  final String? servingIdeas;
  final int reviewsCount;
  final double rating;
  final String? wine;
  final List<Direction>? directions;
  final List<RecipeMedia>? recipeMedia;
  final List<String>? ingredients;

  RecipeDetails({
    required this.id,
    required this.name,
    required this.description,
    this.categoryId,
    this.category,
    this.cuisineId,
    this.cuisine,
    this.yield,
    this.yieldUnit,
    this.servings,
    this.prepTime,
    this.totalTime,
    this.cookTime,
    this.author,
    this.authorMediaFileId,
    this.authorMediaUrl,
    this.source,
    this.copyright,
    this.notes,
    this.servingIdeas,
    required this.reviewsCount,
    required this.rating,
    this.wine,
    this.directions,
    this.recipeMedia,
    required this.ingredients,
  });

  factory RecipeDetails.fromJson(Map<String, dynamic> json) => RecipeDetails(
    id: json["id"] ?? 0,
    name: json["name"] ?? '',
    description: json["description"] ?? '',
    categoryId: json["categoryId"],
    category: json["category"],
    cuisineId: json["cuisineId"],
    cuisine: json["cuisine"] ?? '',
    yield: json["yield"] ?? '',
    yieldUnit: json["yieldUnit"] ?? '',
    servings: json["servings"] ??  0,
    prepTime: json["prepTime"] ?? '',
    totalTime: json["totalTime"] ?? '',
    cookTime: json["cookTime"] ?? '',
    author: json["author"] ?? '',
    authorMediaFileId: json["authorMediaFileId"],
    authorMediaUrl: json["authorMediaUrl"] ?? '',
    source: json["source"] ?? '',
    copyright: json["copyright"] ?? '',
    notes: json["notes"] ?? '',
    servingIdeas: json["servingIdeas"] ?? '',
    reviewsCount: json["reviewsCount"] ?? 0,
    rating: (json["rating"] as num?)?.toDouble() ?? 0.0,
    wine: json["wine"] ?? '',
    directions: json["directions"] != null
        ? List<Direction>.from(
        json["directions"].map((x) => Direction.fromJson(x)))
        : [],
    recipeMedia: json["recipeMedia"] != null
        ? List<RecipeMedia>.from(
        json["recipeMedia"].map((x) => RecipeMedia.fromJson(x)))
        : [],
    ingredients: List<String>.from(json["ingredients"] ?? []),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "description": description,
    "categoryId": categoryId,
    "category": category,
    "cuisineId": cuisineId,
    "cuisine": cuisine,
    "yield": yield,
    "yieldUnit": yieldUnit,
    "servings": servings,
    "prepTime": prepTime,
    "totalTime": totalTime,
    "cookTime": cookTime,
    "author": author,
    "authorMediaFileId": authorMediaFileId,
    "authorMediaUrl": authorMediaUrl,
    "source": source,
    "copyright": copyright,
    "notes": notes,
    "servingIdeas": servingIdeas,
    "reviewsCount": reviewsCount,
    "rating": rating,
    "wine": wine,
    "directions": directions,
    "recipeMedia": recipeMedia,
    "ingredients": ingredients,
  };
}

class RecipeMedia {
  final int mediaFileId;
  final String mediaType;
  final String mediaUrl;

  RecipeMedia({
    required this.mediaFileId,
    required this.mediaType,
    required this.mediaUrl,
  });

  factory RecipeMedia.fromJson(Map<String, dynamic> json) => RecipeMedia(
    mediaFileId: json["mediaFileId"],
    mediaType: json["mediaType"],
    mediaUrl: json["mediaUrl"],
  );

  Map<String, dynamic> toJson() => {
    "mediaFileId": mediaFileId,
    "mediaType": mediaType,
    "mediaUrl": mediaUrl,
  };
}


class Direction {
  final String title;
  final String description;
  final String mediaFileId;
  final String mediaType;
  final String mediaUrl;

  Direction({
    required this.title,
    required this.description,
    required this.mediaFileId,
    required this.mediaType,
    required this.mediaUrl,
  });

  factory Direction.fromJson(Map<String, dynamic> json) => Direction(
    title: json["title"] ?? '',
    description: json["description"] ?? '',
    mediaFileId: json["mediaFileId"] ?? '',
    mediaType: json["mediaType"] ?? '',
    mediaUrl: json["mediaUrl"] ?? '',
  );

  Map<String, dynamic> toJson() => {
    "title": title,
    "description": description,
    "mediaFileId": mediaFileId,
    "mediaType": mediaType,
    "mediaUrl": mediaUrl,
  };
}
