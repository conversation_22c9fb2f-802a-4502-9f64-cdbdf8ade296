import 'dart:convert';

UserTypesResponse userTypesResponseFromJson(String str) => 
    UserTypesResponse.fromJson(json.decode(str));

String userTypesResponseToJson(UserTypesResponse data) => 
    json.encode(data.toJson());

class UserTypesResponse {
  final bool success;
  final Message message;
  final UserTypesData? data;
  final int status;

  UserTypesResponse({
    required this.success,
    required this.message,
    this.data,
    required this.status,
  });

  factory UserTypesResponse.fromJson(Map<String, dynamic> json) => UserTypesResponse(
    success: json["success"] ?? false,
    message: Message.fromJson(json["message"] ?? {}),
    data: json["data"] != null ? UserTypesData.fromJson(json["data"]) : null,
    status: json["status"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "message": message.toJson(),
    "data": data?.toJson(),
    "status": status,
  };
}

class UserTypesData {
  final List<UserType> userTypes;

  UserTypesData({
    required this.userTypes,
  });

  factory UserTypesData.fromJson(Map<String, dynamic> json) => UserTypesData(
    userTypes: List<UserType>.from(
      (json["userTypes"] ?? []).map((x) => UserType.fromJson(x)),
    ),
  );

  Map<String, dynamic> toJson() => {
    "userTypes": List<dynamic>.from(userTypes.map((x) => x.toJson())),
  };
}

class UserType {
  final int id;
  final String type;

  UserType({
    required this.id,
    required this.type,
  });

  factory UserType.fromJson(Map<String, dynamic> json) => UserType(
    id: json["id"] ?? 0,
    type: json["type"] ?? '',
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "type": type,
  };
}

// Reusing the existing Message class from your reference
class Message {
  final dynamic error;
  final List<String> general;

  Message({
    this.error,
    required this.general,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
    error: json["error"],
    general: List<String>.from(json["general"] ?? []),
  );

  Map<String, dynamic> toJson() => {
    "error": error,
    "general": general,
  };
}