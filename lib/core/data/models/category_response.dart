/// success : true
/// message : {"error":null,"general":["Fetched successfully"]}
/// data : {"categories":[{"id":1,"name":"Appetizer","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":2,"name":"Beverage","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":3,"name":"Bread","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":4,"name":"Breakfast","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":5,"name":"Condiment","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":6,"name":"Dessert","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":7,"name":"Main Dish","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":8,"name":"Salad","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":9,"name":"Sandwich","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":10,"name":"Side Dish","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":11,"name":"Snack","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":12,"name":"Soup","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"}]}
/// status : 200

class CategoryResponse {
  CategoryResponse({
      bool? success, 
      Message? message, 
      Data? data, 
      int? status,}){
    _success = success;
    _message = message;
    _data = data;
    _status = status;
}

  CategoryResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
    _status = json['status'];
  }
  bool? _success;
  Message? _message;
  Data? _data;
  int? _status;
CategoryResponse copyWith({  bool? success,
  Message? message,
  Data? data,
  int? status,
}) => CategoryResponse(  success: success ?? _success,
  message: message ?? _message,
  data: data ?? _data,
  status: status ?? _status,
);
  bool? get success => _success;
  Message? get message => _message;
  Data? get data => _data;
  int? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }

}

/// categories : [{"id":1,"name":"Appetizer","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":2,"name":"Beverage","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":3,"name":"Bread","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":4,"name":"Breakfast","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":5,"name":"Condiment","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":6,"name":"Dessert","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":7,"name":"Main Dish","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":8,"name":"Salad","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":9,"name":"Sandwich","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":10,"name":"Side Dish","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":11,"name":"Snack","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"},{"id":12,"name":"Soup","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:34:32.8866667"}]

class Data {
  Data({
      List<Categories>? categories,}){
    _categories = categories;
}

  Data.fromJson(dynamic json) {
    if (json['categories'] != null) {
      _categories = [];
      json['categories'].forEach((v) {
        _categories?.add(Categories.fromJson(v));
      });
    }
  }
  List<Categories>? _categories;
Data copyWith({  List<Categories>? categories,
}) => Data(  categories: categories ?? _categories,
);
  List<Categories>? get categories => _categories;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_categories != null) {
      map['categories'] = _categories?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// id : 1
/// name : "Appetizer"
/// user : null
/// system : true
/// recipes : null
/// lastModified : "2025-05-30T04:34:32.8866667"

class Categories {
  Categories({
      int? id, 
      String? name, 
      dynamic user, 
      bool? system, 
      dynamic recipes, 
      String? lastModified,}){
    _id = id;
    _name = name;
    _user = user;
    _system = system;
    _recipes = recipes;
    _lastModified = lastModified;
}

  Categories.fromJson(dynamic json) {
    _id = json['id'];
    _name = json['name'];
    _user = json['user'];
    _system = json['system'];
    _recipes = json['recipes'];
    _lastModified = json['lastModified'];
  }
  int? _id;
  String? _name;
  dynamic _user;
  bool? _system;
  dynamic _recipes;
  String? _lastModified;
Categories copyWith({  int? id,
  String? name,
  dynamic user,
  bool? system,
  dynamic recipes,
  String? lastModified,
}) => Categories(  id: id ?? _id,
  name: name ?? _name,
  user: user ?? _user,
  system: system ?? _system,
  recipes: recipes ?? _recipes,
  lastModified: lastModified ?? _lastModified,
);
  int? get id => _id;
  String? get name => _name;
  dynamic get user => _user;
  bool? get system => _system;
  dynamic get recipes => _recipes;
  String? get lastModified => _lastModified;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['name'] = _name;
    map['user'] = _user;
    map['system'] = _system;
    map['recipes'] = _recipes;
    map['lastModified'] = _lastModified;
    return map;
  }

}

/// error : null
/// general : ["Fetched successfully"]

class Message {
  Message({
      dynamic error, 
      List<String>? general,}){
    _error = error;
    _general = general;
}

  Message.fromJson(dynamic json) {
    _error = json['error'];
    _general = json['general'] != null ? json['general'].cast<String>() : [];
  }
  dynamic _error;
  List<String>? _general;
Message copyWith({  dynamic error,
  List<String>? general,
}) => Message(  error: error ?? _error,
  general: general ?? _general,
);
  dynamic get error => _error;
  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }

}