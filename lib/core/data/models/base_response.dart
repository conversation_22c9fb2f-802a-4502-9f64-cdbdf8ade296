/// success : true
/// message : {"error":null,"general":["Added successfully"]}
/// data : {"cookbookId":159}
/// status : 200

class BaseResponse {
  BaseResponse({
    bool? success,
    Message? message,
    Data? data,
    num? status,
  }) {
    _success = success;
    _message = message;
    _data = data;
    _status = status;
  }

  BaseResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message =
        json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
    _status = json['status'];
  }

  bool? _success;
  Message? _message;
  Data? _data;
  num? _status;

  BaseResponse copyWith({
    bool? success,
    Message? message,
    Data? data,
    num? status,
  }) =>
      BaseResponse(
        success: success ?? _success,
        message: message ?? _message,
        data: data ?? _data,
        status: status ?? _status,
      );

  bool? get success => _success;

  Message? get message => _message;

  Data? get data => _data;

  num? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }
}

/// cookbookId : 159

class Data {
  Data({
    num? cookbookId,
  }) {
    _cookbookId = cookbookId;
  }

  Data.fromJson(dynamic json) {
    _cookbookId = json['cookbookId'];
  }

  num? _cookbookId;

  Data copyWith({
    num? cookbookId,
  }) =>
      Data(
        cookbookId: cookbookId ?? _cookbookId,
      );

  num? get cookbookId => _cookbookId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['cookbookId'] = _cookbookId;
    return map;
  }
}

/// error : null
/// general : ["Added successfully"]

class Message {
  Message({
    List<String>? error,
    List<String>? general,
  }) {
    _error = error;
    _general = general;
  }

  Message.fromJson(dynamic json) {
    _error = json['error'] != null ? List<String>.from(json['error']) : null;
    _general = json['general'] != null ? List<String>.from(json['general']) : null;
  }

  List<String>? _error;
  List<String>? _general;

  Message copyWith({
    List<String>? error, // Changed from dynamic to List<String>?
    List<String>? general,
  }) =>
      Message(
        error: error ?? _error,
        general: general ?? _general,
      );

  List<String>? get error => _error;

  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }
}