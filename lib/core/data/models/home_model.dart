class HomeModel {
  final bool success;
  final Message message;
  final Data data;
  final int status;

  HomeModel({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  /// Factory constructor to create an [ApiResponse] instance from a JSON map.
  factory HomeModel.fromJson(Map<String, dynamic> json) {
    return HomeModel(
      success: json['success'] as bool,
      message: Message.fromJson(json['message'] as Map<String, dynamic>),
      data: Data.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as int,
    );
  }

  /// Converts this [ApiResponse] instance to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message.toJson(),
      'data': data.toJson(),
      'status': status,
    };
  }
}

/// Represents the 'message' field in the API response.
class Message {
  final String? error;
  final List<String> general;

  Message({
    this.error,
    required this.general,
  });

  /// Factory constructor to create a [Message] instance from a JSON map.
  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: json['error'] as String?,
      // The 'general' field is an array, so we cast it to List<dynamic>
      // and then map each element to a String.
      general: (json['general'] as List<dynamic>)
          .map((e) => e.toString())
          .toList(),
    );
  }

  /// Converts this [Message] instance to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'error': error,
      'general': general,
    };
  }
}

/// Represents the 'data' field in the API response.
class Data {
  final List<String> banners;

  Data({
    required this.banners,
  });

  /// Factory constructor to create a [Data] instance from a JSON map.
  factory Data.fromJson(Map<String, dynamic> json) {
    return Data(
      // The 'banners' field is an array, so we cast it to List<dynamic>
      // and then map each element to a String.
      banners: (json['banners'] as List<dynamic>)
          .map((e) => e.toString())
          .toList(),
    );
  }

  /// Converts this [Data] instance to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'banners': banners,
    };
  }
}