class Category {
  final int id;
  final String name;
  final bool system;
  final String lastModified;

  Category({
    required this.id,
    required this.name,
    required this.system,
    required this.lastModified,
  });

  factory Category.fromJson(Map<String, dynamic> json) => Category(
    id: json['id'],
    name: json['name'],
    system: json['system'],
    lastModified: json['lastModified'],
  );
}