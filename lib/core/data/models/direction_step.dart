// Model
import 'dart:io';

class DirectionStep {
  final String title;
  final String description;
  final String imageUrl;
  final File? mediaFile;
  final String? mediaFileName;
  final int? mediaFileId;

  DirectionStep({
    required this.title,
    required this.description,
    this.imageUrl = '',
    this.mediaFile,
    this.mediaFileName,
    this.mediaFileId,
  });

  Map<String, dynamic> toJson() {
    // Skip object if all fields are empty or null
    if (title.isEmpty &&
        description.isEmpty &&
        imageUrl.isEmpty &&
        mediaFileName == null &&
        mediaFileId == null) {
      return {};
    }

    return {
      "Title": title,
      "Description": description,
      "MediaFileName": mediaFileName,
      "MediaFileId": mediaFileName != null && mediaFileName!.isNotEmpty
          ? null
          : mediaFileId, // Set MediaFileId to null if MediaFileName is not empty
    };
  }
}