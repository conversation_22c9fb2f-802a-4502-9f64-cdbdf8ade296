/// success : true
/// message : {"error":null,"general":["Added successfully"]}
/// data : {"shoppingListId":115}
/// status : 200

class CreateShoppingListResponse {
  CreateShoppingListResponse({
      bool? success, 
      Message? message, 
      Data? data, 
      int? status,}){
    _success = success;
    _message = message;
    _data = data;
    _status = status;
}

  CreateShoppingListResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
    _status = json['status'];
  }
  bool? _success;
  Message? _message;
  Data? _data;
  int? _status;
CreateShoppingListResponse copyWith({  bool? success,
  Message? message,
  Data? data,
  int? status,
}) => CreateShoppingListResponse(  success: success ?? _success,
  message: message ?? _message,
  data: data ?? _data,
  status: status ?? _status,
);
  bool? get success => _success;
  Message? get message => _message;
  Data? get data => _data;
  int? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }

}

/// shoppingListId : 115

class Data {
  Data({
      int? shoppingListId,}){
    _shoppingListId = shoppingListId;
}

  Data.fromJson(dynamic json) {
    _shoppingListId = json['shoppingListId'];
  }
  int? _shoppingListId;
Data copyWith({  int? shoppingListId,
}) => Data(  shoppingListId: shoppingListId ?? _shoppingListId,
);
  int? get shoppingListId => _shoppingListId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['shoppingListId'] = _shoppingListId;
    return map;
  }

}

/// error : null
/// general : ["Added successfully"]

class Message {
  Message({
      dynamic error, 
      List<String>? general,}){
    _error = error;
    _general = general;
}

  Message.fromJson(dynamic json) {
    _error = json['error'];
    _general = json['general'] != null ? json['general'].cast<String>() : [];
  }
  dynamic _error;
  List<String>? _general;
Message copyWith({  dynamic error,
  List<String>? general,
}) => Message(  error: error ?? _error,
  general: general ?? _general,
);
  dynamic get error => _error;
  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }

}