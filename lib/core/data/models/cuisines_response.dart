/// success : true
/// message : {"error":null,"general":["Fetched successfully"]}
/// data : {"cuisines":[{"id":1,"name":"African","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":2,"name":"American","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":3,"name":"Cajun","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":4,"name":"Chinese","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":5,"name":"French","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":6,"name":"German","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":7,"name":"Greek","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":8,"name":"Indian","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":9,"name":"Italian","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":10,"name":"Japanese","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":11,"name":"Korean","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":12,"name":"Lebanese","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":13,"name":"Mexican","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":14,"name":"Spanish","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":15,"name":"Thai1","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"}]}
/// status : 200

class CuisinesResponse {
  CuisinesResponse({
      bool? success, 
      Message? message, 
      Data? data, 
      int? status,}){
    _success = success;
    _message = message;
    _data = data;
    _status = status;
}

  CuisinesResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
    _status = json['status'];
  }
  bool? _success;
  Message? _message;
  Data? _data;
  int? _status;
CuisinesResponse copyWith({  bool? success,
  Message? message,
  Data? data,
  int? status,
}) => CuisinesResponse(  success: success ?? _success,
  message: message ?? _message,
  data: data ?? _data,
  status: status ?? _status,
);
  bool? get success => _success;
  Message? get message => _message;
  Data? get data => _data;
  int? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }

}

/// cuisines : [{"id":1,"name":"African","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":2,"name":"American","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":3,"name":"Cajun","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":4,"name":"Chinese","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":5,"name":"French","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":6,"name":"German","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":7,"name":"Greek","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":8,"name":"Indian","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":9,"name":"Italian","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":10,"name":"Japanese","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":11,"name":"Korean","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":12,"name":"Lebanese","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":13,"name":"Mexican","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":14,"name":"Spanish","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"},{"id":15,"name":"Thai1","user":null,"system":true,"recipes":null,"lastModified":"2025-05-30T04:37:08.8666667"}]

class Data {
  Data({
      List<Cuisines>? cuisines,}){
    _cuisines = cuisines;
}

  Data.fromJson(dynamic json) {
    if (json['cuisines'] != null) {
      _cuisines = [];
      json['cuisines'].forEach((v) {
        _cuisines?.add(Cuisines.fromJson(v));
      });
    }
  }
  List<Cuisines>? _cuisines;
Data copyWith({  List<Cuisines>? cuisines,
}) => Data(  cuisines: cuisines ?? _cuisines,
);
  List<Cuisines>? get cuisines => _cuisines;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_cuisines != null) {
      map['cuisines'] = _cuisines?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// id : 1
/// name : "African"
/// user : null
/// system : true
/// recipes : null
/// lastModified : "2025-05-30T04:37:08.8666667"

class Cuisines {
  Cuisines({
      int? id, 
      String? name, 
      dynamic user, 
      bool? system, 
      dynamic recipes, 
      String? lastModified,}){
    _id = id;
    _name = name;
    _user = user;
    _system = system;
    _recipes = recipes;
    _lastModified = lastModified;
}

  Cuisines.fromJson(dynamic json) {
    _id = json['id'];
    _name = json['name'];
    _user = json['user'];
    _system = json['system'];
    _recipes = json['recipes'];
    _lastModified = json['lastModified'];
  }
  int? _id;
  String? _name;
  dynamic _user;
  bool? _system;
  dynamic _recipes;
  String? _lastModified;
Cuisines copyWith({  int? id,
  String? name,
  dynamic user,
  bool? system,
  dynamic recipes,
  String? lastModified,
}) => Cuisines(  id: id ?? _id,
  name: name ?? _name,
  user: user ?? _user,
  system: system ?? _system,
  recipes: recipes ?? _recipes,
  lastModified: lastModified ?? _lastModified,
);
  int? get id => _id;
  String? get name => _name;
  dynamic get user => _user;
  bool? get system => _system;
  dynamic get recipes => _recipes;
  String? get lastModified => _lastModified;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['name'] = _name;
    map['user'] = _user;
    map['system'] = _system;
    map['recipes'] = _recipes;
    map['lastModified'] = _lastModified;
    return map;
  }

}

/// error : null
/// general : ["Fetched successfully"]

class Message {
  Message({
      dynamic error, 
      List<String>? general,}){
    _error = error;
    _general = general;
}

  Message.fromJson(dynamic json) {
    _error = json['error'];
    _general = json['general'] != null ? json['general'].cast<String>() : [];
  }
  dynamic _error;
  List<String>? _general;
Message copyWith({  dynamic error,
  List<String>? general,
}) => Message(  error: error ?? _error,
  general: general ?? _general,
);
  dynamic get error => _error;
  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }

}