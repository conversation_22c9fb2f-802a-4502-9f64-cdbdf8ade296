import 'dart:convert';

SearchItemsResponse searchItemsResponseFromJson(String str) => 
    SearchItemsResponse.fromJson(json.decode(str));

String searchItemsResponseToJson(SearchItemsResponse data) => 
    json.encode(data.toJson());

class SearchItemsResponse {
  final bool success;
  final Message message;
  final SearchItemsData? data;
  final int status;

  SearchItemsResponse({
    required this.success,
    required this.message,
    this.data,
    required this.status,
  });

  factory SearchItemsResponse.fromJson(Map<String, dynamic> json) => SearchItemsResponse(
    success: json["success"] ?? false,
    message: Message.fromJson(json["message"] ?? {}),
    data: json["data"] != null ? SearchItemsData.fromJson(json["data"]) : null,
    status: json["status"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "message": message.toJson(),
    "data": data?.toJson(),
    "status": status,
  };
}

class SearchItemsData {
  final List<String>? items;

  SearchItemsData({
    this.items,
  });

  factory SearchItemsData.fromJson(Map<String, dynamic> json) => SearchItemsData(
    items: json["items"] != null 
        ? List<String>.from(json["items"].map((x) => x))
        : null,
  );

  Map<String, dynamic> toJson() => {
    "items": items != null ? List<dynamic>.from(items!.map((x) => x)) : null,
  };
}

// Reusing the existing Message class from your recipe model
class Message {
  final dynamic error;
  final List<String> general;

  Message({
    this.error,
    required this.general,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
    error: json["error"],
    general: List<String>.from(json["general"] ?? []),
  );

  Map<String, dynamic> toJson() => {
    "error": error,
    "general": general,
  };
}