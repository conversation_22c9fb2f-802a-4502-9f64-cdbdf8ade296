/// success : true
/// message : {"error":null,"general":["Email has been sent to <b><EMAIL></b>."]}
/// data : null
/// status : 200

class ForgotPasswordResponse {
  ForgotPasswordResponse({
      bool? success, 
      Message? message, 
      dynamic data, 
      num? status,}){
    _success = success;
    _message = message;
    _data = data;
    _status = status;
}

  ForgotPasswordResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'];
    _status = json['status'];
  }
  bool? _success;
  Message? _message;
  dynamic _data;
  num? _status;
ForgotPasswordResponse copyWith({  bool? success,
  Message? message,
  dynamic data,
  num? status,
}) => ForgotPasswordResponse(  success: success ?? _success,
  message: message ?? _message,
  data: data ?? _data,
  status: status ?? _status,
);
  bool? get success => _success;
  Message? get message => _message;
  dynamic get data => _data;
  num? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    map['data'] = _data;
    map['status'] = _status;
    return map;
  }

}

/// error : null
/// general : ["Email has been sent to <b><EMAIL></b>."]

class Message {
  Message({
      dynamic error, 
      List<String>? general,}){
    _error = error;
    _general = general;
}

  Message.fromJson(dynamic json) {
    _error = json['error'];
    _general = json['general'] != null ? json['general'].cast<String>() : [];
  }
  dynamic _error;
  List<String>? _general;
Message copyWith({  dynamic error,
  List<String>? general,
}) => Message(  error: error ?? _error,
  general: general ?? _general,
);
  dynamic get error => _error;
  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }

}