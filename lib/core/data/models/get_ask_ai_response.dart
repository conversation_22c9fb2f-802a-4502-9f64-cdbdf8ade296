class GetAskAiResponse {
  final bool success;
  final Message message;
  final Data data;
  final int status;

  GetAskAiResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory GetAskAiResponse.fromJson(Map<String, dynamic> json) {
    return GetAskAiResponse(
      success: json['success'],
      message: Message.fromJson(json['message']),
      data: Data.fromJson(json['data']),
      status: json['status'],
    );
  }
}

class Message {
  final String? error;
  final List<String> general;

  Message({
    required this.error,
    required this.general,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: json['error'],
      general: List<String>.from(json['general']),
    );
  }
}

class Data {
  final List<Thread> threads;
  final int totalRecords;
  final int totalPageCount;

  Data({
    required this.threads,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory Data.fromJson(Map<String, dynamic> json) {
    return Data(
      threads: (json['threads'] as List)
          .map((e) => Thread.fromJson(e))
          .toList(),
      totalRecords: json['totalRecords'],
      totalPageCount: json['totalPageCount'],
    );
  }
}

class Thread {
  final int id;
  final String title;
  final String createdAtUtc;

  Thread({
    required this.id,
    required this.title,
    required this.createdAtUtc,
  });

  factory Thread.fromJson(Map<String, dynamic> json) {
    return Thread(
      id: json['id'],
      title: json['title'],
      createdAtUtc : json['createdAtUtc'],
    );
  }
}