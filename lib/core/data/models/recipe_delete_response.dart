class RecipeDeleteResponse {
  final bool success;
  final String? message;
  final int? status;

  RecipeDeleteResponse({
    required this.success,
    this.message,
    this.status,
  });

  factory RecipeDeleteResponse.fromJson(Map<String, dynamic> json) {
    return RecipeDeleteResponse(
      success: json['success'] ?? false,
      status: json['status'],
      message: json['message']?['general']?.isNotEmpty == true
          ? json['message']['general'][0]
          : null,
    );
  }
}
