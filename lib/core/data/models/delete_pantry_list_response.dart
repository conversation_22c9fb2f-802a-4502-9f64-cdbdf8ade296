import 'ask_ai_response.dart';

class DeletePantryListResponse {
  final bool success;
  final Message message;
  final int status;

  DeletePantryListResponse({
    required this.success,
    required this.message,
    required this.status,
  });

  factory DeletePantryListResponse.fromJson(Map<String, dynamic> json) {
    return DeletePantryListResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      status: json['status'] as int,
    );
  }
}