
class LoginModel {
  final bool success;
  final Message message;
  final LoginData? data;
  final int status;

  LoginModel({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory LoginModel.fromJson(Map<String, dynamic> json) {
    return LoginModel(
      success: json['success'] as bool,
      message: Message.fromJson(json['message'] ?? {}),
      data: json['data'] != null ? LoginData.fromJson(json['data']) : null,
      status: json['status'] as int,
    );
  }
}

class Message {
  final List<String>? error;
  final List<String>? general;

  Message({this.error, this.general});

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: (json['error'] as List?)?.map((e) => e.toString()).toList(),
      general: (json['general'] as List?)?.map((e) => e.toString()).toList(),
    );
  }
}

class LoginData {
  final String token;

  LoginData({required this.token});

  factory LoginData.fromJson(Map<String, dynamic> json) {
    return LoginData(
      token: json['token'] as String,
    );
  }
}

