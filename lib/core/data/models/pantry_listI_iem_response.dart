import 'package:mastercookai/core/data/models/pantry.dart';

class PantryListItemResponse{
  final bool success;
  final Message message;
  final PantryListItemData data;
  final int status;

  PantryListItemResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory PantryListItemResponse.fromJson(Map<String, dynamic> json) {
    return PantryListItemResponse(
      success: json['success'],
      message: Message.fromJson(json['message']),
      data: PantryListItemData.fromJson(json['data']),
      status: json['status'],
    );
  }
}

class PantryListItemData {
  final List<PantryItem> pantryItems;
  final int totalRecords;
  final int totalPageCount;

  PantryListItemData({
    required this.pantryItems,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory PantryListItemData.fromJson(Map<String, dynamic> json) {
    return PantryListItemData(
      pantryItems: (json['pantryItems'] as List)
          .map((e) => PantryItem.fromJson(e))
          .toList(),
      totalRecords: json['totalRecords'],
      totalPageCount: json['totalPageCount'],
    );
  }

  Map<String, dynamic> toJson() => {
    'pantryItems': pantryItems.map((e) => e.toJson()).toList(),
    'totalRecords': totalRecords,
    'totalPageCount': totalPageCount,
  };
}
