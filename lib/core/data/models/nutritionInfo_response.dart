class NutritionInfoResponse {
  NutritionInfoResponse({
      bool? success, 
      Message? message, 
      Data? data, 
      int? status,}){
    _success = success;
    _message = message;
    _data = data;
    _status = status;
}

  NutritionInfoResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
    _status = json['status'];
  }
  bool? _success;
  Message? _message;
  Data? _data;
  int? _status;
NutritionInfoResponse copyWith({  bool? success,
  Message? message,
  Data? data,
  int? status,
}) => NutritionInfoResponse(  success: success ?? _success,
  message: message ?? _message,
  data: data ?? _data,
  status: status ?? _status,
);
  bool? get success => _success;
  Message? get message => _message;
  Data? get data => _data;
  int? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }

}

class Data {
  Data({
    NutritionInfoData? nutritionInfo,}){
    _nutritionInfo = nutritionInfo;
}

  Data.fromJson(dynamic json) {
    _nutritionInfo = json['nutritionInfo'] != null ? NutritionInfoData.fromJson(json['nutritionInfo']) : null;
  }
  NutritionInfoData? _nutritionInfo;
Data copyWith({  NutritionInfoData? nutritionInfo,
}) => Data(  nutritionInfo: nutritionInfo ?? _nutritionInfo,
);
  NutritionInfoData? get nutritionInfo => _nutritionInfo;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_nutritionInfo != null) {
      map['nutritionInfo'] = _nutritionInfo?.toJson();
    }
    return map;
  }

}

class NutritionInfoData {
  NutritionInfoData({
      int? caloriesPerServing, 
      List<Nutrients>? nutrients,}){
    _caloriesPerServing = caloriesPerServing;
    _nutrients = nutrients;
}

  NutritionInfoData.fromJson(dynamic json) {
    _caloriesPerServing = json['caloriesPerServing'];
    if (json['nutrients'] != null) {
      _nutrients = [];
      json['nutrients'].forEach((v) {
        _nutrients?.add(Nutrients.fromJson(v));
      });
    }
  }
  int? _caloriesPerServing;
  List<Nutrients>? _nutrients;
  NutritionInfoData copyWith({  int? caloriesPerServing,
  List<Nutrients>? nutrients,
}) => NutritionInfoData(  caloriesPerServing: caloriesPerServing ?? _caloriesPerServing,
  nutrients: nutrients ?? _nutrients,
);
  int? get caloriesPerServing => _caloriesPerServing;
  List<Nutrients>? get nutrients => _nutrients;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['caloriesPerServing'] = _caloriesPerServing;
    if (_nutrients != null) {
      map['nutrients'] = _nutrients?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class Nutrients {
  Nutrients({
      String? name, 
      String? amount, 
      String? dailyValue, 
      List<SubNutrients>? subNutrients,}){
    _name = name;
    _amount = amount;
    _dailyValue = dailyValue;
    _subNutrients = subNutrients;
}

  Nutrients.fromJson(dynamic json) {
    _name = json['name'];
    _amount = json['amount'];
    _dailyValue = json['dailyValue'];
    if (json['subNutrients'] != null) {
      _subNutrients = [];
      json['subNutrients'].forEach((v) {
        _subNutrients?.add(SubNutrients.fromJson(v));
      });
    }
  }
  String? _name;
  String? _amount;
  String? _dailyValue;
  List<SubNutrients>? _subNutrients;
Nutrients copyWith({  String? name,
  String? amount,
  String? dailyValue,
  List<SubNutrients>? subNutrients,
}) => Nutrients(  name: name ?? _name,
  amount: amount ?? _amount,
  dailyValue: dailyValue ?? _dailyValue,
  subNutrients: subNutrients ?? _subNutrients,
);
  String? get name => _name;
  String? get amount => _amount;
  String? get dailyValue => _dailyValue;
  List<SubNutrients>? get subNutrients => _subNutrients;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['name'] = _name;
    map['amount'] = _amount;
    map['dailyValue'] = _dailyValue;
    if (_subNutrients != null) {
      map['subNutrients'] = _subNutrients?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class SubNutrients {
  SubNutrients({
      String? name, 
      String? amount, 
      String? dailyValue, 
      dynamic subNutrients,}){
    _name = name;
    _amount = amount;
    _dailyValue = dailyValue;
    _subNutrients = subNutrients;
}

  SubNutrients.fromJson(dynamic json) {
    _name = json['name'];
    _amount = json['amount'];
    _dailyValue = json['dailyValue'];
    _subNutrients = json['subNutrients'];
  }
  String? _name;
  String? _amount;
  String? _dailyValue;
  dynamic _subNutrients;
SubNutrients copyWith({  String? name,
  String? amount,
  String? dailyValue,
  dynamic subNutrients,
}) => SubNutrients(  name: name ?? _name,
  amount: amount ?? _amount,
  dailyValue: dailyValue ?? _dailyValue,
  subNutrients: subNutrients ?? _subNutrients,
);
  String? get name => _name;
  String? get amount => _amount;
  String? get dailyValue => _dailyValue;
  dynamic get subNutrients => _subNutrients;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['name'] = _name;
    map['amount'] = _amount;
    map['dailyValue'] = _dailyValue;
    map['subNutrients'] = _subNutrients;
    return map;
  }

}

class Message {
  Message({
      dynamic error, 
      List<String>? general,}){
    _error = error;
    _general = general;
}

  Message.fromJson(dynamic json) {
    _error = json['error'];
    _general = json['general'] != null ? json['general'].cast<String>() : [];
  }
  dynamic _error;
  List<String>? _general;
Message copyWith({  dynamic error,
  List<String>? general,
}) => Message(  error: error ?? _error,
  general: general ?? _general,
);
  dynamic get error => _error;
  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }

}