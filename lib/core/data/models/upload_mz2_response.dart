/// ImportId : 1

class UploadMz2Response {
  UploadMz2Response({
    num? importId,
  }) {
    _importId = importId;
  }

  UploadMz2Response.fromJson(dynamic json) {
    _importId = json['ImportId'];
  }

  num? _importId;

  UploadMz2Response copyWith({
    num? importId,
  }) =>
      UploadMz2Response(
        importId: importId ?? _importId,
      );

  num? get importId => _importId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['ImportId'] = _importId;
    return map;
  }
}
