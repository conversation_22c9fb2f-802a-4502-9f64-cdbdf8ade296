import 'dart:io';

class RecipeDetailResponse {
  RecipeDetailResponse({
    bool? success,
    UserMessage? message,
    Data? data,
    int? status,
  }) {
    _success = success;
    _message = message;
    _data = data;
    _status = status;
  }

  RecipeDetailResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message =
        json['message'] != null ? UserMessage.fromJson(json['message']) : null;
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
    _status = json['status'];
  }

  bool? _success;
  UserMessage? _message;
  Data? _data;
  int? _status;

  RecipeDetailResponse copyWith({
    bool? success,
    UserMessage? message,
    Data? data,
    int? status,
  }) =>
      RecipeDetailResponse(
        success: success ?? _success,
        message: message ?? _message,
        data: data ?? _data,
        status: status ?? _status,
      );

  bool? get success => _success;

  UserMessage? get message => _message;

  Data? get data => _data;

  int? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }
}

/// recipeDetails : {"id":77,"name":"dfsd","description":"sdfgsf","categoryId":2,"category":"Beverage","cuisineId":6,"cuisine":"German","yield":"4","yieldUnit":"cups","servings":435,"prepTime":"6h 0m","totalTime":"6:00","cookTime":"4h 0m","author":"rishi","authorMediaFileId":200,"authorMediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/62/recipes/77/author/images/7df3e826-3001-4976-a706-d51f71229785.jpeg","recipeThumbnailFileId":null,"recipeThumbnailFileUrl":null,"directionThumbnailFileId":null,"directionThumbnailFileUrl":null,"source":"test","copyright":"2025","notes":"dsfgjdjfshvjhx   ajsdjhfga","servingIdeas":"sdjfhsjdhgjdsjg sdahfjhasjdfjasd","reviewsCount":10,"rating":4.2,"wine":"asdfjhasdjfhjasdh jsdjfjahsdjfhajsd","nutritionFacts":[{"label":"Fiber, total dietary","value":"1.7 g"},{"label":"Energy","value":"345 kcal"},{"label":"Total lipid (fat)","value":"13.79 g"},{"label":"Carbohydrate, by difference","value":"50 g"},{"label":"Protein","value":"5.17 g"}],"directions":[{"id":258,"title":"","description":"sdfhsjdhgjbsd sdhfjhasdjhfgjhasdj sdhfjhasdjf","mediaFileId":201,"mediaType":"IMAGE","mediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/62/recipes/77/instruction/images/49f69e7d-d7ea-428b-bd97-3f11eebf468c.jpeg"},{"id":259,"title":"","description":"","mediaFileId":null,"mediaType":null,"mediaUrl":null},{"id":260,"title":"","description":"","mediaFileId":null,"mediaType":null,"mediaUrl":null}],"recipeMedia":[{"mediaFileId":197,"mediaType":"VIDEO","mediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/62/recipes/77/recipe/videos/1fe07abe-310c-4b3f-8839-0adac687e0e3.mov"},{"mediaFileId":198,"mediaType":"IMAGE","mediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/62/recipes/77/recipe/images/29e56b2f-b547-4815-a1bc-1d648fe449b4.jpeg"},{"mediaFileId":199,"mediaType":"IMAGE","mediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/62/recipes/77/recipe/images/615cac8b-d53b-489f-b67d-f5c2f39ab60e.jpeg"}],"ingredients":["dfgsdfg","dfgs","fgsfg"],"aiMessageId":null,"aiImageUrl":null}

class Data {
  Data({
    RecipeDetails? recipeDetails,
  }) {
    _recipeDetails = recipeDetails;
  }

  Data.fromJson(dynamic json) {
    _recipeDetails = json['recipeDetails'] != null
        ? RecipeDetails.fromJson(json['recipeDetails'])
        : null;
  }

  RecipeDetails? _recipeDetails;

  Data copyWith({
    RecipeDetails? recipeDetails,
  }) =>
      Data(
        recipeDetails: recipeDetails ?? _recipeDetails,
      );

  RecipeDetails? get recipeDetails => _recipeDetails;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_recipeDetails != null) {
      map['recipeDetails'] = _recipeDetails?.toJson();
    }
    return map;
  }
}

class RecipeDetails {
  RecipeDetails({
    int? id,
    int? coverMediaIndex,
    String? name,
    String? description,
    int? categoryId,
    String? category,
    int? cuisineId,
    String? cuisine,
    String? yield,
    String? yieldUnit,
    int? servings,
    String? prepTime,
    String? totalTime,
    String? cookTime,
    String? author,
    int? authorMediaFileId,
    String? authorMediaUrl,
    dynamic recipeThumbnailFileId,
    dynamic recipeThumbnailFileUrl,
    dynamic directionThumbnailFileId,
    dynamic directionThumbnailFileUrl,
    String? source,
    String? copyright,
    String? notes,
    String? servingIdeas,
    int? reviewsCount,
    dynamic rating,
    String? wine,
    NutritionInfo? nutritionInfo, // Changed from List<NutritionFacts>
    List<Directions>? directions,
    List<RecipeMedia>? recipeMedia,
    List<String>? ingredients,
    dynamic aiMessageId,
    dynamic aiImageUrl,
  }) {
    _id = id;
    _coverMediaIndex = coverMediaIndex;
    _name = name;
    _description = description;
    _categoryId = categoryId;
    _category = category;
    _cuisineId = cuisineId;
    _cuisine = cuisine;
    _yield = yield;
    _yieldUnit = yieldUnit;
    _servings = servings;
    _prepTime = prepTime;
    _totalTime = totalTime;
    _cookTime = cookTime;
    _author = author;
    _authorMediaFileId = authorMediaFileId;
    _authorMediaUrl = authorMediaUrl;
    _recipeThumbnailFileId = recipeThumbnailFileId;
    _recipeThumbnailFileUrl = recipeThumbnailFileUrl;
    _directionThumbnailFileId = directionThumbnailFileId;
    _directionThumbnailFileUrl = directionThumbnailFileUrl;
    _source = source;
    _copyright = copyright;
    _notes = notes;
    _servingIdeas = servingIdeas;
    _reviewsCount = reviewsCount;
    _rating = rating;
    _wine = wine;
    _nutritionInfo = nutritionInfo; // Changed from _nutritionFacts
    _directions = directions;
    _recipeMedia = recipeMedia;
    _ingredients = ingredients;
    _aiMessageId = aiMessageId;
    _aiImageUrl = aiImageUrl;
  }

  RecipeDetails.fromJson(dynamic json) {
    _id = json['id'];
    _coverMediaIndex = json['coverMediaIndex'];
    _name = json['name'];
    _description = json['description'];
    _categoryId = json['categoryId'];
    _category = json['category'];
    _cuisineId = json['cuisineId'];
    _cuisine = json['cuisine'];
    _yield = json['yield'];
    _yieldUnit = json['yieldUnit'];
    _servings = json['servings'];
    _prepTime = json['prepTime'];
    _totalTime = json['totalTime'];
    _cookTime = json['cookTime'];
    _author = json['author'];
    _authorMediaFileId = json['authorMediaFileId'];
    _authorMediaUrl = json['authorMediaUrl'];
    _recipeThumbnailFileId = json['recipeThumbnailFileId'];
    _recipeThumbnailFileUrl = json['recipeThumbnailFileUrl'];
    _directionThumbnailFileId = json['directionThumbnailFileId'];
    _directionThumbnailFileUrl = json['directionThumbnailFileUrl'];
    _source = json['source'];
    _copyright = json['copyright'];
    _notes = json['notes'];
    _servingIdeas = json['servingIdeas'];
    _reviewsCount = json['reviewsCount'];
    _rating = json['rating'];
    _wine = json['wine'];
    _nutritionInfo = json['nutritionInfo'] != null // Changed from nutritionFacts to nutritionInfo
        ? NutritionInfo.fromJson(json['nutritionInfo'])
        : null;
    if (json['directions'] != null) {
      _directions = [];
      json['directions'].forEach((v) {
        _directions?.add(Directions.fromJson(v));
      });
    }
    if (json['recipeMedia'] != null) {
      _recipeMedia = [];
      json['recipeMedia'].forEach((v) {
        _recipeMedia?.add(RecipeMedia.fromJson(v));
      });
    }
    _ingredients =
    json['ingredients'] != null ? json['ingredients'].cast<String>() : [];
    _aiMessageId = json['aiMessageId'];
    _aiImageUrl = json['aiImageUrl'];
  }

  int? _id;
  int? _coverMediaIndex;
  String? _name;
  String? _description;
  int? _categoryId;
  String? _category;
  int? _cuisineId;
  String? _cuisine;
  String? _yield;
  String? _yieldUnit;
  int? _servings;
  String? _prepTime;
  String? _totalTime;
  String? _cookTime;
  String? _author;
  int? _authorMediaFileId;
  String? _authorMediaUrl;
  dynamic _recipeThumbnailFileId;
  dynamic _recipeThumbnailFileUrl;
  dynamic _directionThumbnailFileId;
  dynamic _directionThumbnailFileUrl;
  String? _source;
  String? _copyright;
  String? _notes;
  String? _servingIdeas;
  int? _reviewsCount;
  dynamic _rating;
  String? _wine;
  NutritionInfo? _nutritionInfo; // Changed from List<NutritionFacts>
  List<Directions>? _directions;
  List<RecipeMedia>? _recipeMedia;
  List<String>? _ingredients;
  dynamic _aiMessageId;
  dynamic _aiImageUrl;

  RecipeDetails copyWith({
    int? id,
    int? coverMediaIndex,
    String? name,
    String? description,
    int? categoryId,
    String? category,
    int? cuisineId,
    String? cuisine,
    String? yield,
    String? yieldUnit,
    int? servings,
    String? prepTime,
    String? totalTime,
    String? cookTime,
    String? author,
    int? authorMediaFileId,
    String? authorMediaUrl,
    dynamic recipeThumbnailFileId,
    dynamic recipeThumbnailFileUrl,
    dynamic directionThumbnailFileId,
    dynamic directionThumbnailFileUrl,
    String? source,
    String? copyright,
    String? notes,
    String? servingIdeas,
    int? reviewsCount,
    dynamic rating,
    String? wine,
    NutritionInfo? nutritionInfo, // Changed from List<NutritionFacts>
    List<Directions>? directions,
    List<RecipeMedia>? recipeMedia,
    List<String>? ingredients,
    dynamic aiMessageId,
    dynamic aiImageUrl,
  }) =>
      RecipeDetails(
        id: id ?? _id,
        coverMediaIndex: coverMediaIndex ?? _coverMediaIndex,
        name: name ?? _name,
        description: description ?? _description,
        categoryId: categoryId ?? _categoryId,
        category: category ?? _category,
        cuisineId: cuisineId ?? _cuisineId,
        cuisine: cuisine ?? _cuisine,
        yield: yield ?? _yield,
        yieldUnit: yieldUnit ?? _yieldUnit,
        servings: servings ?? _servings,
        prepTime: prepTime ?? _prepTime,
        totalTime: totalTime ?? _totalTime,
        cookTime: cookTime ?? _cookTime,
        author: author ?? _author,
        authorMediaFileId: authorMediaFileId ?? _authorMediaFileId,
        authorMediaUrl: authorMediaUrl ?? _authorMediaUrl,
        recipeThumbnailFileId: recipeThumbnailFileId ?? _recipeThumbnailFileId,
        recipeThumbnailFileUrl:
        recipeThumbnailFileUrl ?? _recipeThumbnailFileUrl,
        directionThumbnailFileId:
        directionThumbnailFileId ?? _directionThumbnailFileId,
        directionThumbnailFileUrl:
        directionThumbnailFileUrl ?? _directionThumbnailFileUrl,
        source: source ?? _source,
        copyright: copyright ?? _copyright,
        notes: notes ?? _notes,
        servingIdeas: servingIdeas ?? _servingIdeas,
        reviewsCount: reviewsCount ?? _reviewsCount,
        rating: rating ?? _rating,
        wine: wine ?? _wine,
        nutritionInfo: nutritionInfo ?? _nutritionInfo, // Changed from nutritionFacts
        directions: directions ?? _directions,
        recipeMedia: recipeMedia ?? _recipeMedia,
        ingredients: ingredients ?? _ingredients,
        aiMessageId: aiMessageId ?? _aiMessageId,
        aiImageUrl: aiImageUrl ?? _aiImageUrl,
      );

  int? get id => _id;
  int? get coverMediaIndex => _coverMediaIndex;
  String? get name => _name;
  String? get description => _description;
  int? get categoryId => _categoryId;
  String? get category => _category;
  int? get cuisineId => _cuisineId;
  String? get cuisine => _cuisine;
  String? get yield => _yield;
  String? get yieldUnit => _yieldUnit;
  int? get servings => _servings;
  String? get prepTime => _prepTime;
  String? get totalTime => _totalTime;
  String? get cookTime => _cookTime;
  String? get author => _author;
  int? get authorMediaFileId => _authorMediaFileId;
  String? get authorMediaUrl => _authorMediaUrl;
  dynamic get recipeThumbnailFileId => _recipeThumbnailFileId;
  dynamic get recipeThumbnailFileUrl => _recipeThumbnailFileUrl;
  dynamic get directionThumbnailFileId => _directionThumbnailFileId;
  dynamic get directionThumbnailFileUrl => _directionThumbnailFileUrl;
  String? get source => _source;
  String? get copyright => _copyright;
  String? get notes => _notes;
  String? get servingIdeas => _servingIdeas;
  int? get reviewsCount => _reviewsCount;
  num? get rating => _rating;
  String? get wine => _wine;
  NutritionInfo? get nutritionInfo => _nutritionInfo; // Changed from nutritionFacts
  List<Directions>? get directions => _directions;
  List<RecipeMedia>? get recipeMedia => _recipeMedia;
  List<String>? get ingredients => _ingredients;
  dynamic get aiMessageId => _aiMessageId;
  dynamic get aiImageUrl => _aiImageUrl;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['coverMediaIndex'] = _coverMediaIndex;
    map['name'] = _name;
    map['description'] = _description;
    map['categoryId'] = _categoryId;
    map['category'] = _category;
    map['cuisineId'] = _cuisineId;
    map['cuisine'] = _cuisine;
    map['yield'] = _yield;
    map['yieldUnit'] = _yieldUnit;
    map['servings'] = _servings;
    map['prepTime'] = _prepTime;
    map['totalTime'] = _totalTime;
    map['cookTime'] = _cookTime;
    map['author'] = _author;
    map['authorMediaFileId'] = _authorMediaFileId;
    map['authorMediaUrl'] = _authorMediaUrl;
    map['recipeThumbnailFileId'] = _recipeThumbnailFileId;
    map['recipeThumbnailFileUrl'] = _recipeThumbnailFileUrl;
    map['directionThumbnailFileId'] = _directionThumbnailFileId;
    map['directionThumbnailFileUrl'] = _directionThumbnailFileUrl;
    map['source'] = _source;
    map['copyright'] = _copyright;
    map['notes'] = _notes;
    map['servingIdeas'] = _servingIdeas;
    map['reviewsCount'] = _reviewsCount;
    map['rating'] = _rating;
    map['wine'] = _wine;
    if (_nutritionInfo != null) {
      map['nutritionInfo'] = _nutritionInfo?.toJson(); // Changed from nutritionFacts
    }
    if (_directions != null) {
      map['directions'] = _directions?.map((v) => v.toJson()).toList();
    }
    if (_recipeMedia != null) {
      map['recipeMedia'] = _recipeMedia?.map((v) => v.toJson()).toList();
    }
    map['ingredients'] = _ingredients;
    map['aiMessageId'] = _aiMessageId;
    map['aiImageUrl'] = _aiImageUrl;
    return map;
  }
}

/// mediaFileId : 197
/// mediaType : "VIDEO"
/// mediaUrl : "https://api.mastercook.ai/uploads/users/3/cookbooks/62/recipes/77/recipe/videos/1fe07abe-310c-4b3f-8839-0adac687e0e3.mov"

class RecipeMedia {
  RecipeMedia({
    int? mediaFileId,
    String? mediaType,
    String? mediaUrl,
    File? mediaFile,
    File? thumbnailFile,
    File? videoFile,
  }) {
    _mediaFileId = mediaFileId;
    _mediaType = mediaType;
    _mediaUrl = mediaUrl;
    _mediaFile = mediaFile;
    _thumbnailFile = thumbnailFile;
    _videoFile = videoFile;
  }

  RecipeMedia.fromJson(dynamic json) {
    _mediaFileId = json['mediaFileId'];
    _mediaType = json['mediaType'];
    _mediaUrl = json['mediaUrl'];
  }

  int? _mediaFileId;
  String? _mediaType;
  String? _mediaUrl;
  File? _mediaFile;
  File? _thumbnailFile;
  File? _videoFile;

  RecipeMedia copyWith({
    int? mediaFileId,
    String? mediaType,
    String? mediaUrl,
  }) =>
      RecipeMedia(
        mediaFileId: mediaFileId ?? _mediaFileId,
        mediaType: mediaType ?? _mediaType,
        mediaUrl: mediaUrl ?? _mediaUrl,
        mediaFile: mediaFile ?? _mediaFile,
        videoFile: videoFile ?? _videoFile,
        thumbnailFile: thumbnailFile ?? _thumbnailFile,
      );

  int? get mediaFileId => _mediaFileId;

  String? get mediaType => _mediaType;

  File? get mediaFile => _mediaFile;

  String? get mediaUrl => _mediaUrl;

  File? get thumbnailFile => _thumbnailFile;

  File? get videoFile => _videoFile;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['mediaFileId'] = _mediaFileId;
    map['mediaType'] = _mediaType;
    map['mediaUrl'] = _mediaUrl;
    map['mediaFile'] = _mediaFile;

    return map;
  }
}

class Directions {
  Directions({
    int? id,
    String? title,
    String? description,
    int? mediaFileId,
    String? mediaType,
    String? mediaUrl,
  }) {
    _id = id;
    _title = title;
    _description = description;
    _mediaFileId = mediaFileId;
    _mediaType = mediaType;
    _mediaUrl = mediaUrl;
  }

  Directions.fromJson(dynamic json) {
    _id = json['id'];
    _title = json['title'];
    _description = json['description'];
    _mediaFileId = json['mediaFileId'];
    _mediaType = json['mediaType'];
    _mediaUrl = json['mediaUrl'];
  }

  int? _id;
  String? _title;
  String? _description;
  int? _mediaFileId;
  String? _mediaType;
  String? _mediaUrl;

  Directions copyWith({
    int? id,
    String? title,
    String? description,
    int? mediaFileId,
    String? mediaType,
    String? mediaUrl,
  }) =>
      Directions(
        id: id ?? _id,
        title: title ?? _title,
        description: description ?? _description,
        mediaFileId: mediaFileId ?? _mediaFileId,
        mediaType: mediaType ?? _mediaType,
        mediaUrl: mediaUrl ?? _mediaUrl,
      );

  int? get id => _id;

  String? get title => _title;

  String? get description => _description;

  int? get mediaFileId => _mediaFileId;

  String? get mediaType => _mediaType;

  String? get mediaUrl => _mediaUrl;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['title'] = _title;
    map['description'] = _description;
    map['mediaFileId'] = _mediaFileId;
    map['mediaType'] = _mediaType;
    map['mediaUrl'] = _mediaUrl;
    return map;
  }
}

/// label : "Fiber, total dietary"
/// value : "1.7 g"

/// Represents the top-level nutritionInfo object with caloriesPerServing and a list of nutrients.
class NutritionInfo {
  NutritionInfo({
    this.caloriesPerServing,
    this.nutrients,
  });

  NutritionInfo.fromJson(dynamic json) {
    caloriesPerServing = json['caloriesPerServing'];
    if (json['nutrients'] != null) {
      nutrients = <Nutrient>[];
      json['nutrients'].forEach((v) {
        nutrients!.add(Nutrient.fromJson(v));
      });
    }
  }

  int? caloriesPerServing;
  List<Nutrient>? nutrients;

  NutritionInfo copyWith({
    int? caloriesPerServing,
    List<Nutrient>? nutrients,
  }) =>
      NutritionInfo(
        caloriesPerServing: caloriesPerServing ?? this.caloriesPerServing,
        nutrients: nutrients ?? this.nutrients,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['caloriesPerServing'] = caloriesPerServing;
    if (nutrients != null) {
      map['nutrients'] = nutrients!.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// Represents a nutrient with name, amount, dailyValue, and optional subNutrients.
class Nutrient {
  Nutrient({
    this.name,
    this.amount,
    this.dailyValue,
    this.subNutrients,
  });

  Nutrient.fromJson(dynamic json) {
    name = json['name'];
    amount = json['amount'];
    dailyValue = json['dailyValue'];
    if (json['subNutrients'] != null) {
      subNutrients = <Nutrient>[];
      json['subNutrients'].forEach((v) {
        subNutrients!.add(Nutrient.fromJson(v));
      });
    }
  }

  String? name;
  String? amount;
  String? dailyValue;
  List<Nutrient>? subNutrients;

  Nutrient copyWith({
    String? name,
    String? amount,
    String? dailyValue,
    List<Nutrient>? subNutrients,
  }) =>
      Nutrient(
        name: name ?? this.name,
        amount: amount ?? this.amount,
        dailyValue: dailyValue ?? this.dailyValue,
        subNutrients: subNutrients ?? this.subNutrients,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['name'] = name;
    map['amount'] = amount;
    map['dailyValue'] = dailyValue;
    if (subNutrients != null) {
      map['subNutrients'] = subNutrients!.map((v) => v.toJson()).toList();
    } else {
      map['subNutrients'] = null;
    }
    return map;
  }
}

/// error : null
/// general : ["Fetched successfully"]

class UserMessage {
  UserMessage({
    dynamic error,
    List<String>? general,
  }) {
    _error = error;
    _general = general;
  }

  UserMessage.fromJson(dynamic json) {
    _error = json['error'];
    _general = json['general'] != null ? json['general'].cast<String>() : [];
  }

  dynamic _error;
  List<String>? _general;

  UserMessage copyWith({
    dynamic error,
    List<String>? general,
  }) =>
      UserMessage(
        error: error ?? _error,
        general: general ?? _general,
      );

  dynamic get error => _error;

  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }
}
