class CookbookModelData {
  final int id;
  final String name;
  final int recipeCount;
  final bool system;
  final DateTime dateAdded;
  final DateTime lastModified;
  final String type;
  final bool readOnly;
  final String permission;

  CookbookModelData({
    required this.id,
    required this.name,
    required this.recipeCount,
    required this.system,
    required this.dateAdded,
    required this.lastModified,
    required this.type,
    required this.readOnly,
    required this.permission,
  });

  factory CookbookModelData.fromJson(Map<String, dynamic> json) {
    return CookbookModelData(
      id: json['id'],
      name: json['name'],
      recipeCount: json['recipeCount'],
      system: json['system'],
      dateAdded: DateTime.parse(json['dateAdded']),
      lastModified: DateTime.parse(json['lastModified']),
      type: json['type'],
      readOnly: json['readOnly'],
      permission: json['permission'],
    );
  }
}



class CookbookModel {
  final bool success;
  final Message message;
  final CookbookData data;
  final int status;

  CookbookModel({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory CookbookModel.fromJson(Map<String, dynamic> json) {
    return CookbookModel(
      success: json['success'],
      message: Message.fromJson(json['message']),
      data: CookbookData.fromJson(json['data']),
      status: json['status'],
    );
  }
}

class Message {
  final String? error;
  final List<String> general;

  Message({
    this.error,
    required this.general,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: json['error'],
      general: List<String>.from(json['general']),
    );
  }
}

class CookbookData {
  final List<CookbookModelData> cookbooks;
  final int totalRecords;
  final int totalPageCount;

  CookbookData({
    required this.cookbooks,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory CookbookData.fromJson(Map<String, dynamic> json) {
    return CookbookData(
      cookbooks: (json['cookbooks'] as List)
          .map((item) => CookbookModelData.fromJson(item))
          .toList(),
      totalRecords: json['totalRecords'],
      totalPageCount: json['totalPageCount'],
    );
  }
}



