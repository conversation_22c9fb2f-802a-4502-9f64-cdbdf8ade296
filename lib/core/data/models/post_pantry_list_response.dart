class PostPantryListResponse {
  final bool success;
  final Message message;
  final PostPantryListResponseData data;
  final int status;

  PostPantryListResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory PostPantryListResponse.fromJson(Map<String, dynamic> json) {
    return PostPantryListResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      data: PostPantryListResponseData.fromJson(json['data']),
      status: json['status'] as int,
    );
  }
}

class Message {
  final List<String>? error;
  final List<String>? general;

  Message({this.error, this.general});

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: (json['error'] as List?)?.map((e) => e.toString()).toList(),
      general: (json['general'] as List?)?.map((e) => e.toString()).toList(),
    );
  }
}

class PostPantryListResponseData {
  final int pantryId;

  PostPantryListResponseData({
    required this.pantryId,
  });

  factory PostPantryListResponseData.fromJson(Map<String, dynamic> json) {
    return PostPantryListResponseData(
      pantryId: json['pantryId'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pantryId': pantryId,
    };
  }
}