class Shopping {
  final int id;
  final String title;
  final String imageUrl;
  final String recipeCount;
  final String createdDate;

  Shopping({
    required this.id,
    required this.title,
    required this.imageUrl,
    required this.recipeCount,
    required this.createdDate,
  });

  factory Shopping.fromJson(Map<String, dynamic> json) {
    return Shopping(
      id: json['id'],
      title: json['title'],
      imageUrl: json['imageUrl'],
      recipeCount: json['recipeCount'],
      createdDate: json['createdDate'],
    );
  }
}

class PostShoppingListResponse {
  final bool success;
  final Message message;
  final PostShoppingListResponseData data;
  final int status;

  PostShoppingListResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory PostShoppingListResponse.fromJson(Map<String, dynamic> json) {
    return PostShoppingListResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      data: PostShoppingListResponseData.fromJson(json['data']),
      status: json['status'] as int,
    );
  }
}

class Message {
  final List<String>? error;
  final List<String>? general;

  Message({this.error, this.general});

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: (json['error'] as List?)?.map((e) => e.toString()).toList(),
      general: (json['general'] as List?)?.map((e) => e.toString()).toList(),
    );
  }
}

class PostShoppingListResponseData {
  final int shoppingListId;

  PostShoppingListResponseData({
    required this.shoppingListId,
  });

  factory PostShoppingListResponseData.fromJson(Map<String, dynamic> json) {
    return PostShoppingListResponseData(
      shoppingListId: json['shoppingListId'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'shoppingListId': shoppingListId,
    };
  }
}

class GetShoppingListResponse {
  final bool success;
  final Message message;
  final GetShoppingListResponseData data;
  final int status;

  GetShoppingListResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory GetShoppingListResponse.fromJson(Map<String, dynamic> json) {
    return GetShoppingListResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      data: GetShoppingListResponseData.fromJson(json['data']),
      status: json['status'] as int,
    );
  }
}

class GetShoppingListResponseData {
  final List<ShoppingList> shoppingLists;
  final int totalRecords;
  final int totalPageCount;

  GetShoppingListResponseData({
    required this.shoppingLists,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory GetShoppingListResponseData.fromJson(Map<String, dynamic> json) {
    return GetShoppingListResponseData(
      shoppingLists: (json['shoppingLists'] as List)
          .map((e) => ShoppingList.fromJson(e))
          .toList(),
      totalRecords: json['totalRecords'] as int,
      totalPageCount: json['totalPageCount'] as int,
    );
  }
}

class ShoppingList {
  final int id;
  final String name;
  final dynamic user;
  final int shoppingItemsCount;
  final String? coverImageUrl;
  final List<Shopping> shoppingItems;
  final DateTime dateCreated;
  final DateTime lastUpdated;

  ShoppingList({
    required this.id,
    required this.name,
    this.user,
    required this.shoppingItemsCount,
    this.coverImageUrl,
    required this.shoppingItems,
    required this.dateCreated,
    required this.lastUpdated,
  });

  factory ShoppingList.fromJson(Map<String, dynamic> json) {
    return ShoppingList(
      id: json['id'] as int,
      name: json['name'] as String,
      user: json['user'],
      shoppingItemsCount: json['shoppingItemsCount'] as int,
      coverImageUrl: json['coverImageUrl'],
      shoppingItems: (json['shoppingItems'] as List).map((e) => Shopping.fromJson(e)).toList(),
      dateCreated: DateTime.parse(json['dateCreated'] as String),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

class ShoppingListItemResponse {
  final bool success;
  final Map<String, dynamic> message;
  final ShoppingListItemData data;
  final int status;

  ShoppingListItemResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory ShoppingListItemResponse.fromJson(Map<String, dynamic> json) {
    return ShoppingListItemResponse(
      success: json['success'],
      message: Map<String, dynamic>.from(json['message']),
      data: ShoppingListItemData.fromJson(json['data']),
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() => {
        'success': success,
        'message': message,
        'data': data.toJson(),
        'status': status,
      };
}

class ShoppingListItemData {
  final List<ShoppingItem> shoppingItems;
  final double totalCost;
  final int totalRecords;
  final int totalPageCount;

  ShoppingListItemData({
    required this.shoppingItems,
    required this.totalCost,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory ShoppingListItemData.fromJson(Map<String, dynamic> json) {
    return ShoppingListItemData(
      shoppingItems: (json['shoppingItems'] as List)
          .map((e) => ShoppingItem.fromJson(e))
          .toList(),
      totalCost: json['totalCost'].toDouble(),
      totalRecords: json['totalRecords'],
      totalPageCount: json['totalPageCount'],
    );
  }

  Map<String, dynamic> toJson() => {
        'shoppingItems': shoppingItems.map((e) => e.toJson()).toList(),
        'totalCost': totalCost,
        'totalRecords': totalRecords,
        'totalPageCount': totalPageCount,
      };
}

class ShoppingItem {
  final int id;
  final String item;
  final double amount;
  final String unit;
  final String storeLocation;
  final String recipe;
  final double cost;
  final bool purchased;

  ShoppingItem({
    required this.id,
    required this.item,
    required this.amount,
    required this.unit,
    required this.storeLocation,
    required this.recipe,
    required this.cost,
    required this.purchased,
  });

  factory ShoppingItem.fromJson(Map<String, dynamic> json) {
    return ShoppingItem(
      id: json['id'],
      item: json['item'],
      amount: (json['amount'] ?? 0.0).toDouble(),
      unit: json['unit'],
      storeLocation: json['storeLocation'],
      recipe: json['recipe'],
      cost: (json['cost'] ?? 0.0).toDouble(),
      purchased: json['purchased'],
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'item': item,
        'amount': amount,
        'unit': unit,
        'storeLocation': storeLocation,
        'recipe': recipe,
        'cost': cost,
        'purchased': purchased,
      };
}

class DeleteShoppingListResponse {
  final bool success;
  final Message message;
  final int status;

  DeleteShoppingListResponse({
    required this.success,
    required this.message,
    required this.status,
  });

  factory DeleteShoppingListResponse.fromJson(Map<String, dynamic> json) {
    return DeleteShoppingListResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      status: json['status'] as int,
    );
  }
}