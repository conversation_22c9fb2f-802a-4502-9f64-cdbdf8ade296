import 'dart:convert';

import 'package:mastercookai/core/data/models/recipe_detail_response.dart';

import 'ask_ai_response.dart';

// Top-level response model
class GetAskAiThreadMessagesResponse {
  final bool success;
  final MessageResponse message;
  final Data data;
  final int status;

  GetAskAiThreadMessagesResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory GetAskAiThreadMessagesResponse.fromJson(Map<String, dynamic> json) {
    return GetAskAiThreadMessagesResponse(
      success: json['success'] as bool,
      message: MessageResponse.fromJson(json['message'] as Map<String, dynamic>),
      data: Data.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message.toJson(),
      'data': data.toJson(),
      'status': status,
    };
  }
}

// Message response containing error and general messages
class MessageResponse {
  final String? error;
  final List<String> general;

  MessageResponse({
    this.error,
    required this.general,
  });

  factory MessageResponse.fromJson(Map<String, dynamic> json) {
    return MessageResponse(
      error: json['error'] as String?,
      general: List<String>.from(json['general'] as List),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'error': error,
      'general': general,
    };
  }
}

// Data containing messages and pagination info
class Data {
  final List<UserMessage> messages;
  final int totalRecords;
  final int totalPageCount;

  Data({
    required this.messages,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory Data.fromJson(Map<String, dynamic> json) {
    return Data(
      messages: (json['messages'] as List)
          .map((e) => UserMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalRecords: json['totalRecords'] as int,
      totalPageCount: json['totalPageCount'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'messages': messages.map((e) => e.toJson()).toList(),
      'totalRecords': totalRecords,
      'totalPageCount': totalPageCount,
    };
  }
}

// Message containing prompt, response, and recipe details
class UserMessage {
  final int id;
  final String prompt;
  final String response;
  final String? imageUrl;
  final String askedAt;
  final String? recipeDetailsJson;
  final RecipeDetails? recipeDetails;

  UserMessage({
    required this.id,
    required this.prompt,
    required this.response,
    this.imageUrl,
    required this.askedAt,
    this.recipeDetailsJson,
    this.recipeDetails,
  });

  factory UserMessage.fromJson(Map<String, dynamic> json) {
    return UserMessage(
      id: json['id'] as int,
      prompt: json['prompt'] as String,
      response: json['response'] as String,
      imageUrl: json['imageUrl'] as String?,
      askedAt: json['askedAt'] as String,
      recipeDetailsJson: json['recipeDetailsJson'] as String?,
      recipeDetails: json['recipeDetails'] != null
          ? RecipeDetails.fromJson(json['recipeDetails'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'prompt': prompt,
      'response': response,
      'imageUrl': imageUrl,
      'askedAt': askedAt,
      'recipeDetailsJson': recipeDetailsJson,
      'recipeDetails': recipeDetails?.toJson(),
    };
  }

  factory UserMessage.fromThreadData(ThreadData threadData, {String? image}) {
    return UserMessage(
      id: threadData.messageId,
      prompt: threadData.prompt,
      response: threadData.response,
      askedAt: threadData.askedAt.toString(),
      imageUrl: image,
      recipeDetails: threadData.recipeDetails,
    );
  }
}
