/// success : false
/// message : {"error":["Already exists."],"general":null}
/// data : null
/// status : 409

class ErrorResponse {
  ErrorResponse({
      bool? success, 
      Message? message, 
      dynamic data, 
      int? status,}){
    _success = success;
    _message = message;
    _data = data;
    _status = status;
}

  ErrorResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'];
    _status = json['status'];
  }
  bool? _success;
  Message? _message;
  dynamic _data;
  int? _status;
ErrorResponse copyWith({  bool? success,
  Message? message,
  dynamic data,
  int? status,
}) => ErrorResponse(  success: success ?? _success,
  message: message ?? _message,
  data: data ?? _data,
  status: status ?? _status,
);
  bool? get success => _success;
  Message? get message => _message;
  dynamic get data => _data;
  int? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    map['data'] = _data;
    map['status'] = _status;
    return map;
  }

}

/// error : ["Already exists."]
/// general : null

class Message {
  Message({
      List<String>? error, 
      dynamic general,}){
    _error = error;
    _general = general;
}

  Message.fromJson(dynamic json) {
    _error = json['error'] != null ? json['error'].cast<String>() : [];
    _general = json['general'];
  }
  List<String>? _error;
  dynamic _general;
Message copyWith({  List<String>? error,
  dynamic general,
}) => Message(  error: error ?? _error,
  general: general ?? _general,
);
  List<String>? get error => _error;
  dynamic get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }

}