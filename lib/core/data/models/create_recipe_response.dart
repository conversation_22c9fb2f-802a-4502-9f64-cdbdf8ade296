class CreateRecipeResponse {
  final bool success;
  final MessageResponse message;
  final dynamic data;
  final int status;

  CreateRecipeResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory CreateRecipeResponse.fromJson(Map<String, dynamic> json) {
    return CreateRecipeResponse(
      success: json['success'],
      message: MessageResponse.fromJson(json['message']),
      data: json['data'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message.toJson(),
      'data': data,
      'status': status,
    };
  }
}

class MessageResponse {
  final List<String>? error;
  final List<dynamic>? general;

  MessageResponse({
    this.error,
    this.general,
  });

  factory MessageResponse.fromJson(Map<String, dynamic> json) {
    return MessageResponse(
      error: json['error'] != null ? List<String>.from(json['error']) : null,
      general: json['general'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'error': error,
      'general': general,
    };
  }
}
