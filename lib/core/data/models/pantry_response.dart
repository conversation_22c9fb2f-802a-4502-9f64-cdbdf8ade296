/// success : true
/// message : {"error":null,"general":["Fetched successfully"]}
/// data : {"pantries":[{"id":66,"name":"adFASDFDS","coverImageUrl":null,"pantryItemCount":5,"dateCreated":"2025-07-04T13:00:46.100Z"},{"id":65,"name":"sdfasdf","coverImageUrl":null,"pantryItemCount":0,"dateCreated":"2025-07-04T12:59:45.860Z"},{"id":64,"name":"wew","coverImageUrl":null,"pantryItemCount":0,"dateCreated":"2025-07-04T12:32:50.276Z"},{"id":28,"name":"hgwdhh","coverImageUrl":"https://api.mastercook.ai/uploads/users/3/pantries/28/others/728f2374-b93a-4305-9320-85fe44f15cef.jpg","pantryItemCount":0,"dateCreated":"2025-07-02T08:46:50.320Z"},{"id":26,"name":"wiuehr","coverImageUrl":"https://api.mastercook.ai/uploads/users/3/pantries/26/others/538bb10a-7e2f-4ba8-9162-2145f4ea5e96.jpeg","pantryItemCount":0,"dateCreated":"2025-07-01T12:36:44.206Z"},{"id":25,"name":"test 32","coverImageUrl":"https://api.mastercook.ai/uploads/users/3/pantries/25/others/883e1dd9-34bf-4e96-a27c-73a7b296d3d0.jpeg","pantryItemCount":0,"dateCreated":"2025-07-01T12:22:29.760Z"},{"id":24,"name":"newww33","coverImageUrl":"https://api.mastercook.ai/uploads/users/3/pantries/24/others/e1feeeca-d990-4943-86cc-eae504346786.jpg","pantryItemCount":0,"dateCreated":"2025-06-30T10:04:35.733Z"},{"id":23,"name":"werrtyee","coverImageUrl":null,"pantryItemCount":0,"dateCreated":"2025-06-30T09:17:29.373Z"},{"id":22,"name":"pan","coverImageUrl":null,"pantryItemCount":0,"dateCreated":"2025-06-30T09:17:02.176Z"},{"id":21,"name":"new one","coverImageUrl":"https://api.mastercook.ai/uploads/users/3/pantries/21/others/82403c1d-e990-4dd4-a9c7-5c47846aac2f.png","pantryItemCount":0,"dateCreated":"2025-06-30T07:48:22.746Z"}],"totalRecords":15,"totalPageCount":2}
/// status : 200

class PantryResponse {
  PantryResponse({
      bool? success, 
      Message? message,
    PantryResponseData? data,
      int? status,}){
    _success = success;
    _message = message;
    _data = data;
    _status = status;
}

  PantryResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? PantryResponseData.fromJson(json['data']) : null;
    _status = json['status'];
  }
  bool? _success;
  Message? _message;
  PantryResponseData? _data;
  int? _status;
PantryResponse copyWith({  bool? success,
  Message? message,
  PantryResponseData? data,
  int? status,
}) => PantryResponse(  success: success ?? _success,
  message: message ?? _message,
  data: data ?? _data,
  status: status ?? _status,
);
  bool? get success => _success;
  Message? get message => _message;
  PantryResponseData? get data => _data;
  int? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }

}

/// pantries : [{"id":66,"name":"adFASDFDS","coverImageUrl":null,"pantryItemCount":5,"dateCreated":"2025-07-04T13:00:46.100Z"},{"id":65,"name":"sdfasdf","coverImageUrl":null,"pantryItemCount":0,"dateCreated":"2025-07-04T12:59:45.860Z"},{"id":64,"name":"wew","coverImageUrl":null,"pantryItemCount":0,"dateCreated":"2025-07-04T12:32:50.276Z"},{"id":28,"name":"hgwdhh","coverImageUrl":"https://api.mastercook.ai/uploads/users/3/pantries/28/others/728f2374-b93a-4305-9320-85fe44f15cef.jpg","pantryItemCount":0,"dateCreated":"2025-07-02T08:46:50.320Z"},{"id":26,"name":"wiuehr","coverImageUrl":"https://api.mastercook.ai/uploads/users/3/pantries/26/others/538bb10a-7e2f-4ba8-9162-2145f4ea5e96.jpeg","pantryItemCount":0,"dateCreated":"2025-07-01T12:36:44.206Z"},{"id":25,"name":"test 32","coverImageUrl":"https://api.mastercook.ai/uploads/users/3/pantries/25/others/883e1dd9-34bf-4e96-a27c-73a7b296d3d0.jpeg","pantryItemCount":0,"dateCreated":"2025-07-01T12:22:29.760Z"},{"id":24,"name":"newww33","coverImageUrl":"https://api.mastercook.ai/uploads/users/3/pantries/24/others/e1feeeca-d990-4943-86cc-eae504346786.jpg","pantryItemCount":0,"dateCreated":"2025-06-30T10:04:35.733Z"},{"id":23,"name":"werrtyee","coverImageUrl":null,"pantryItemCount":0,"dateCreated":"2025-06-30T09:17:29.373Z"},{"id":22,"name":"pan","coverImageUrl":null,"pantryItemCount":0,"dateCreated":"2025-06-30T09:17:02.176Z"},{"id":21,"name":"new one","coverImageUrl":"https://api.mastercook.ai/uploads/users/3/pantries/21/others/82403c1d-e990-4dd4-a9c7-5c47846aac2f.png","pantryItemCount":0,"dateCreated":"2025-06-30T07:48:22.746Z"}]
/// totalRecords : 15
/// totalPageCount : 2

class PantryResponseData {
  PantryResponseData({
      List<Pantries>? pantries, 
      int? totalRecords, 
      int? totalPageCount,}){
    _pantries = pantries;
    _totalRecords = totalRecords;
    _totalPageCount = totalPageCount;
}

  PantryResponseData.fromJson(dynamic json) {
    if (json['pantries'] != null) {
      _pantries = [];
      json['pantries'].forEach((v) {
        _pantries?.add(Pantries.fromJson(v));
      });
    }
    _totalRecords = json['totalRecords'];
    _totalPageCount = json['totalPageCount'];
  }
  List<Pantries>? _pantries;
  int? _totalRecords;
  int? _totalPageCount;
  PantryResponseData copyWith({  List<Pantries>? pantries,
  int? totalRecords,
  int? totalPageCount,
}) => PantryResponseData(  pantries: pantries ?? _pantries,
  totalRecords: totalRecords ?? _totalRecords,
  totalPageCount: totalPageCount ?? _totalPageCount,
);
  List<Pantries>? get pantries => _pantries;
  int? get totalRecords => _totalRecords;
  int? get totalPageCount => _totalPageCount;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_pantries != null) {
      map['pantries'] = _pantries?.map((v) => v.toJson()).toList();
    }
    map['totalRecords'] = _totalRecords;
    map['totalPageCount'] = _totalPageCount;
    return map;
  }

}

/// id : 66
/// name : "adFASDFDS"
/// coverImageUrl : null
/// pantryItemCount : 5
/// dateCreated : "2025-07-04T13:00:46.100Z"

class Pantries {
  Pantries({
      int? id, 
      String? name, 
      dynamic coverImageUrl, 
      int? pantryItemCount, 
      String? dateCreated,
  }){
    _id = id;
    _name = name;
    _coverImageUrl = coverImageUrl;
    _pantryItemCount = pantryItemCount;
    _dateCreated = dateCreated;
}

  Pantries.fromJson(dynamic json) {
    _id = json['id'];
    _name = json['name'];
    _coverImageUrl = json['coverImageUrl'];
    _pantryItemCount = json['pantryItemCount'];
    _dateCreated = json['dateCreated'];
  }
  int? _id;
  String? _name;
  dynamic _coverImageUrl;
  int? _pantryItemCount;
  String? _dateCreated;
Pantries copyWith({  int? id,
  String? name,
  dynamic coverImageUrl,
  int? pantryItemCount,
  String? dateCreated,
}) => Pantries(  id: id ?? _id,
  name: name ?? _name,
  coverImageUrl: coverImageUrl ?? _coverImageUrl,
  pantryItemCount: pantryItemCount ?? _pantryItemCount,
  dateCreated: dateCreated ?? _dateCreated,
);
  int? get id => _id;
  String? get name => _name;
  dynamic get coverImageUrl => _coverImageUrl;
  int? get pantryItemCount => _pantryItemCount;
  String? get dateCreated => _dateCreated;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['name'] = _name;
    map['coverImageUrl'] = _coverImageUrl;
    map['pantryItemCount'] = _pantryItemCount;
    map['dateCreated'] = _dateCreated;
    return map;
  }

}

/// error : null
/// general : ["Fetched successfully"]

class Message {
  Message({
      dynamic error, 
      List<String>? general,}){
    _error = error;
    _general = general;
}

  Message.fromJson(dynamic json) {
    _error = json['error'];
    _general = json['general'] != null ? json['general'].cast<String>() : [];
  }
  dynamic _error;
  List<String>? _general;
Message copyWith({  dynamic error,
  List<String>? general,
}) => Message(  error: error ?? _error,
  general: general ?? _general,
);
  dynamic get error => _error;
  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }

}