
class PartnerModel {
  final bool success;
  final PartnerData? data;

  PartnerModel({required this.success, this.data});

  factory PartnerModel.fromJson(Map<String, dynamic> json) {
    return PartnerModel(
      success: json['success'] ?? false,
      data: json['data'] != null ? PartnerData.fromJson(json['data']) : null,
    );
  }
}

class PartnerData {
  final String apiKey;

  PartnerData({required this.apiKey});

  factory PartnerData.fromJson(Map<String, dynamic> json) {
    return PartnerData(apiKey: json['apiKey'] ?? '');
  }
}
