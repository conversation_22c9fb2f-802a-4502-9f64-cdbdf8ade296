// recipe_model.dart

import 'package:mastercookai/core/data/models/recipes.dart';

class RecipeResponse {
  final List<Recipe> recipes;
  final int totalRecords;
  final int totalPageCount;

  RecipeResponse({
    required this.recipes,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory RecipeResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'] ?? {};
    final recipesJson = data['recipes'] as List<dynamic>? ?? [];

    return RecipeResponse(
      recipes: recipesJson.map((e) => Recipe.fromJson(e)).toList(),
      totalRecords: data['totalRecords'] ?? 0,
      totalPageCount: data['totalPageCount'] ?? 0,
    );
  }
}
class Recipe {
  final int id;
  final String name;
  final String? description;
  final int servings;
  final int coverMediaIndex;
  final String? prepTime;
  final String? cookTime;
  final String? totalTime;
  final int reviewsCount;
  final double rating;
  final String? mediaUrl;
  final DateTime? dateAdded;
  final String permission;
  final bool isPublic;
  final List<RecipeMedia>? recipeMedia;

  Recipe({
    required this.id,
    required this.name,
    this.description,
    required this.servings,
    this.prepTime,
    required this.coverMediaIndex,
    this.cookTime,
    this.totalTime,
    required this.reviewsCount,
    required this.rating,
    this.mediaUrl,
    this.dateAdded,
    required this.permission,
    required this.isPublic,
    this.recipeMedia,
  });

  factory Recipe.fromJson(Map<String, dynamic> json) {
    return Recipe(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      servings: json['servings'] ?? 0,
      prepTime: json['prepTime'],
      cookTime: json['cookTime'],
      totalTime: json['totalTime'],
      reviewsCount: json['reviewsCount'] ?? 0,
      coverMediaIndex: json['coverMediaIndex'] ?? 1,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      mediaUrl: json['mediaUrl'] ?? '',
      dateAdded: json['dateAdded'] != null
          ? DateTime.tryParse(json['dateAdded'])
          : null,
      permission: json['permission'] ?? '',
      isPublic: json['public'] ?? false,
      recipeMedia: json["recipeMedia"] != null
          ? List<RecipeMedia>.from(
          json["recipeMedia"].map((x) => RecipeMedia.fromJson(x)))
          : [],
    );
  }
}



