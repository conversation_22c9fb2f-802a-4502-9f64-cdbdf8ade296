import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '/core/helpers/local_storage_service.dart';
import '/core/network/dio_provider.dart';
import '/core/utils/HeaderBuilder.dart';
import 'models/pantry.dart';
import 'models/pantry_listI_iem_response.dart';

final PostPantryListApiProvider = Provider<PostPantryListApi>((ref) {
  final dio = ref.watch(dioProvider);
  final storage = LocalStorageService();
  return PostPantryListApi(dio, storage);
});

class PostPantryListApi {
  final Dio dio;
  final LocalStorageService localStorage;

  PostPantryListApi(this.dio, this.localStorage);

  Future<Response> PantryList(String name) async {
    final apiKey = await localStorage.getApiKey();
    final token = await localStorage.getToken();

    if (apiKey == null) {
      throw Exception("No access key found for current platform");
    }
    final encodedKey = base64Encode(utf8.encode(apiKey));

    final headerBuilder = await HeaderBuilder.initialize();
    final headers = headerBuilder.build(extra: {
      'api-key': encodedKey,
      'Authorization': 'Bearer $token',
    });

    return await dio.post('api/pantry',
        data: {
          'name': name,
        },
        options: Options(headers: headers));
  }
}

final GetPantryListApiProvider = Provider<GetPantryListApi>((ref) {
  final dio = ref.watch(dioProvider);
  final storage = LocalStorageService();
  return GetPantryListApi(dio, storage);
});

class GetPantryListApi {
  final Dio dio;
  final LocalStorageService localStorage;

  GetPantryListApi(this.dio, this.localStorage);

  Future<GetPantryListResponse> fetchPantryLists({
    required int pageNumber,
    required int pageSize,
    bool includePantryItems = true,
    String itemSort = 'Newest',
    String? search,
  }) async {
    final apiKey = await localStorage.getApiKey();
    final token = await localStorage.getToken();

    if (apiKey == null) {
      throw Exception("No access key found for current platform");
    }

    final encodedKey = base64Encode(utf8.encode(apiKey));

    final headerBuilder = await HeaderBuilder.initialize();
    final headers = headerBuilder.build(extra: {
      'api-key': encodedKey,
      'Authorization': 'Bearer $token',
    });

    // Build query parameters, only include search if it's not null or empty
    final queryParams = {
      'pageNumber': pageNumber,
      'pageSize': pageSize,
      'includePantryItems': includePantryItems,
      'itemSort': itemSort,
    };

    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }

    final response = await dio.get(
      'api/pantries',
      queryParameters: queryParams,
      options: Options(headers: headers),
    );

    if (response.statusCode == 200) {
      return GetPantryListResponse.fromJson(response.data);
    } else {
      throw Exception('Failed to load shopping lists: ${response.statusCode}');
    }
  }
}

//Pantry List Items API
final GetPantryListItemsApiProvider = Provider<GetPantryListItemsApi>((ref) {
  final dio = ref.watch(dioProvider);
  final storage = LocalStorageService();
  return GetPantryListItemsApi(dio, storage);
});

class GetPantryListItemsApi {
  final Dio dio;
  final LocalStorageService localStorage;

  GetPantryListItemsApi(this.dio, this.localStorage);

  Future<PantryListItemResponse> fetcPantryListsItems({
    required int id,
    required int pageNumber,
    required int pageSize,
    String? search,
  }) async {
    final apiKey = await localStorage.getApiKey();
    final token = await localStorage.getToken();

    if (apiKey == null) {
      throw Exception("No access key found for current platform");
    }

    final encodedKey = base64Encode(utf8.encode(apiKey));

    final headerBuilder = await HeaderBuilder.initialize();
    final headers = headerBuilder.build(extra: {
      'api-key': encodedKey,
      'Authorization': 'Bearer $token',
    });

    final queryParams = <String, dynamic>{
      'pageNumber': pageNumber,
      'pageSize': pageSize,
    };

    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }

    final response = await dio.get(
      'api/pantry/$id/items',
      queryParameters: queryParams,
      options: Options(headers: headers),
    );

    if (response.statusCode == 200) {
      return PantryListItemResponse.fromJson(response.data);
    } else {
      throw Exception('Failed to load shopping lists: ${response.statusCode}');
    }
  }
}

final DeletePantryListApiProvider = Provider<DeletePantryListApi>((ref) {
  final dio = ref.watch(dioProvider);
  final storage = LocalStorageService();
  return DeletePantryListApi(dio, storage);
});

class DeletePantryListApi {
  final Dio dio;
  final LocalStorageService localStorage;

  DeletePantryListApi(this.dio, this.localStorage);

  Future<Response> deletePantryList(String id) async {
    final apiKey = await localStorage.getApiKey();
    final token = await localStorage.getToken();

    if (apiKey == null) {
      throw Exception("No access key found for current platform");
    }

    final encodedKey = base64Encode(utf8.encode(apiKey));

    final headerBuilder = await HeaderBuilder.initialize();
    final headers = headerBuilder.build(extra: {
      'api-key': encodedKey,
      'Authorization': 'Bearer $token',
    });


    return await dio.delete('api/pantry/$id',
        options: Options(headers: headers));
  }
}
