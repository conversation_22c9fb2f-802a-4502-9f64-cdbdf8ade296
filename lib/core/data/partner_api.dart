import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../helpers/local_storage_service.dart';
import '../network/dio_provider.dart';
import '../../domain/repositories/repository_implementation.dart';

final partnerApiProvider = Provider<RepositoryImplementation>((ref) {
  final dio = ref.watch(dioProvider);
  final localStorage = ref.watch(localStorageProvider);
  return RepositoryImplementation(dio, localStorage, ref);
});
