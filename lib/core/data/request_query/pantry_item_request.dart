class PantryItemsRequest {
  List<PantryItem>? pantryItems;

  PantryItemsRequest({
    this.pantryItems,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (pantryItems != null) {
      data['pantryItems'] = pantryItems!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PantryItem {
  int? id;
  String? item;
  double? amount;
  String? unit;
  DateTime? purchasedDate;
  DateTime? useByDate;

  PantryItem({
    this.id,
    this.item,
    this.amount,
    this.unit,
    this.purchasedDate,
    this.useByDate,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    if (id != null) data['id'] = id;
    if (item != null) data['item'] = item;
    if (amount != null && amount != 0.0) data['amount'] = amount;
    if (unit != null) data['unit'] = unit;
    if (purchasedDate != null) {
      data['purchasedDate'] = purchasedDate!.toIso8601String().substring(0, 10);
    }
    if (useByDate != null) {
      data['useByDate'] = useByDate!.toIso8601String().substring(0, 10);
    }

    return data;
  }

}

class DeletePantryItemsRequest {
  String? type;
  List<int>? pantryItemIds;

  DeletePantryItemsRequest({
    this.type, 
    this.pantryItemIds,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (type != null) {
      data['type'] = type;
    }
    if (pantryItemIds != null) {
      data['pantryItemIds'] = pantryItemIds;
    }
    return data;
  }
}