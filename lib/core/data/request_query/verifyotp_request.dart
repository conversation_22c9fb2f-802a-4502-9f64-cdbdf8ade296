class VerifyotpRequest {
  String? useremail;
  String? otp;
  String? deviceToken;
  String? packageName;
  String? accessKey;


  VerifyotpRequest({
    this.useremail,
    this.otp,
    this.deviceToken,
    this.accessKey,
    this.packageName,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['email'] = useremail;
    data['otp'] = otp;
    return data;
  }
}