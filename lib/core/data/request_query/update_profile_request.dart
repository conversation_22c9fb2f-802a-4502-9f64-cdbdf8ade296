
import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';

class UpdateProfileRequest {
  final String firstName;       // required
  final String? lastName;
  final String gender;         // required
  final String dob;            // required (date)
  final String countryCode;    // required
  final String contact;        // required
  final String? companyName;
  final int userTypeId;        // required (integer)
  final File? profilePic;      // (binary)

  UpdateProfileRequest({
    required this.firstName,
    this.lastName,
    required this.gender,
    required this.dob,
    required this.countryCode,
    required this.contact,
    this.companyName,
    required this.userTypeId,
    this.profilePic,
  });

  // Convert to FormData for multipart request
  Future<FormData> toFormData() async {
    final map = <String, dynamic>{
      'FirstName': firstName,
      'Gender': gender,
      'Dob': dob,
      'CountryCode': countryCode,
      'Contact': contact,
      'UserTypeId': userTypeId.toString(),
    };

    // Add optional fields if they exist
    if (lastName != null) map['LastName'] = lastName!;
    if (companyName != null) map['CompanyName'] = companyName!;

    final formData = FormData.fromMap(map);

    // Add profile picture if exists
    if (profilePic != null) {
      formData.files.add(
        MapEntry(
          'ProfilePic',
          await MultipartFile.fromFile(
            profilePic!.path,
            filename: profilePic!.path.split('/').last,
            contentType: MediaType('image', 'jpeg'), // Generic image type
          ),
        ),
      );
    }

    return formData;
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'FirstName': firstName,
      'LastName': lastName,
      'Gender': gender,
      'Dob': dob,
      'CountryCode': countryCode,
      'Contact': contact,
      'CompanyName': companyName,
      'UserTypeId': userTypeId,
      'ProfilePic': profilePic?.path,
    };
  }

  // Pretty-print JSON
  String toJsonString() {
    const encoder = JsonEncoder.withIndent('  ');
    return encoder.convert(toJson());
  }
}