class PlanRequest {
  int? planId;
  String? platform;
  String? store;
  String? receiptDataJson;

  PlanRequest({
    this.planId,
    this.platform,
    this.store,
    this.receiptDataJson,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (planId != null) {
      data['planId'] = planId;
    }
    if (platform != null) {
      data['platform'] = platform;
    }
    if (store != null) {
      data['store'] = store;
    }
    if (receiptDataJson != null) {
      data['receiptDataJson'] = receiptDataJson;
    }

    return data;
  }
}
