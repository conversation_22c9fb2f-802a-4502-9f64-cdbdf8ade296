class PaginationQueryParam {
  int? pageNumber;
  int? pageSize;
  int? id;
  String? search;
  String? itemSort;
  String? includeShoppingItems;
  int? cuisineId;
  int? categoryId;

  PaginationQueryParam({
    this.pageNumber,
    this.pageSize,
    this.id,
    this.search,
    this.itemSort,
    this.includeShoppingItems,
    this.cuisineId,
    this.categoryId,

  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    // Always include pageNumber and pageSize if they are not null
    if (pageNumber != null) {
      data['pageNumber'] = pageNumber;
    }
    if (pageSize != null) {
      data['pageSize'] = pageSize;
    }
    if (search != null && search!.isNotEmpty) {
      data['search'] = search;
    }
    if (itemSort != null && itemSort!.isNotEmpty &&itemSort!="Sort By") {
      data['itemSort'] = itemSort;
    }
    if (includeShoppingItems != null && includeShoppingItems!.isNotEmpty) {
      data['includeShoppingItems'] = includeShoppingItems;
    }
    if (cuisineId != null && cuisineId! > 0) {
      data['cuisineId'] = cuisineId;
    }
    if (categoryId != null && categoryId! > 0) {
      data['categoryId'] = categoryId;
    }

    return data;
  }
}


