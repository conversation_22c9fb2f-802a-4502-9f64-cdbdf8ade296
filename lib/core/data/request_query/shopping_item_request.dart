class ShoppingItemRequest {
  List<ShoppingItem>? shoppingItems;

  ShoppingItemRequest({
    this.shoppingItems,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (shoppingItems != null) {
      data['shoppingItems'] = shoppingItems!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ShoppingItem {
  int? id;
  String? item;
  double? amount;
  String? unit;
  String? storeLocation;
  String? recipe;
  double? cost;

  ShoppingItem({
    this.id,
    this.item,
    this.amount,
    this.unit,
    this.storeLocation,
    this.recipe,
    this.cost,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    if (id != null) data['id'] = id;
    if (item != null) data['item'] = item;
    if (amount != null && amount != 0.0) data['amount'] = amount;
    if (unit != null) data['unit'] = unit;
    if (storeLocation != null) data['storeLocation'] = storeLocation;
    if (recipe != null) data['recipe'] = recipe;
    if (cost != null && cost != 0.0) data['cost'] = cost;

    return data;
  }

}

class DeleteShoppingItemsRequest {
  String? type;
  List<int>? shoppingItemIds;
  String? status;

  DeleteShoppingItemsRequest({
    this.type,
    this.shoppingItemIds,
    this.status,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (type != null) {
      data['type'] = type;
    }
    if (shoppingItemIds != null) {
      data['shoppingItemIds'] = shoppingItemIds;
    }
    if (status != null) {
      data['status'] = status;
    }
    return data;
  }
}