class AskAiQueryParam {
  int? threadId;
  String? prompt;


  AskAiQueryParam({
    this.threadId,
    this.prompt,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['threadId'] = threadId;
    data['prompt'] = prompt;

    // data['deviceType'] = GetPlatform.isIOS ? ios : android;
    //  data['deviceToken'] = deviceToken;
    return data;
  }
}
