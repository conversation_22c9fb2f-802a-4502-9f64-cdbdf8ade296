class RegisterRequest {
  String? username;
  String? useremail;
  String? password;
  String? userType;
  String? deviceToken;
  String? packageName;
  String? accessKey;


  RegisterRequest({
    this.username,
    this.useremail,
    this.password,
    this.userType,
    this.deviceToken,
    this.accessKey,
    this.packageName,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = username;
    data['email'] = useremail;
    data['password'] = password;
    data['userTypeId'] = userType;
    return data;
  }
}
