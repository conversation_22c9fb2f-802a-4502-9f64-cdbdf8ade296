class AuthQueryParam {
  String? username;
  String? password;
  String? deviceToken;
  String? packageName;
  String? accessKey;


  AuthQueryParam({
    this.username,
    this.password,
    this.deviceToken,
    this.accessKey,
    this.packageName,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['email'] = username;
    data['password'] = password;

    // data['deviceType'] = GetPlatform.isIOS ? ios : android;
    //  data['deviceToken'] = deviceToken;
    return data;
  }
}
