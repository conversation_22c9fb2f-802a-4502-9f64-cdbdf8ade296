import 'dart:convert';

class DeleteThreadRequest {
  final String type;          // required
  final List<int> threadIds;   // required

  DeleteThreadRequest({
    required this.type,
    required this.threadIds,
  });

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'threadIds': threadIds,
    };
  }

  // Pretty-print JSON
  String toJsonString() {
    const encoder = JsonEncoder.withIndent('  ');
    return encoder.convert(toJson());
  }
}