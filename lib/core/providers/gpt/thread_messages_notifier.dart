// Thread Messages Notifier - handles messages for a specific thread
import '../../../app/imports/packages_imports.dart';
import '../../data/models/get_ask_ai_thread_messages.dart';
import '../../data/request_query/paginantion_request.dart';
import '../../network/app_status.dart';
import '../../network/base_notifier.dart';
import '../../network/network_utils.dart';
import '../../utils/Utils.dart';

// Thread Messages Provider - separate from threads provider
final threadMessagesNotifierProvider =
StateNotifierProvider<ThreadMessagesNotifier, AppState<List<UserMessage>>>(
      (ref) => ThreadMessagesNotifier(ref),
);


class ThreadMessagesNotifier extends BaseNotifier<List<UserMessage>> {
  ThreadMessagesNotifier(Ref ref) : super(ref, const AppState<List<UserMessage>>());

  String? _currentSearchQuery;
  final int pageSize = 10;

  /// GET - Fetch Thread messages with pagination and search
  Future<void> fetchThreadsMessages({
    required BuildContext context,
    required int id,
    bool loadMore = false,
    String? search,
  }) async {
    // If this is a new search, reset pagination
    if (search != _currentSearchQuery) {
      _currentSearchQuery = search;
      loadMore = false;
    }

    if (loadMore && (!state.hasMore || state.status == AppStatus.loadingMore)) {
      return;
    }

    final currentPage = loadMore ? state.currentPage + 1 : 1;



    callDataService(
      ref: ref,
      repo.getAskAiMessages(
        id,
        PaginationQueryParam(
          pageNumber: currentPage,
          pageSize: pageSize,
          search: _currentSearchQuery,
        ),
      ),
      onStart: () => state = state.copyWith(
        status: loadMore ? AppStatus.loadingMore : AppStatus.loading,
        data: loadMore ? state.data : [],
        hasMore: loadMore ? state.hasMore : true,
        currentPage: currentPage,
      ),
      onSuccess: (GetAskAiThreadMessagesResponse response) {
        final newMessage = response.data.messages;
        final totalPages = response.data.totalPageCount;
        final hasMore = currentPage < totalPages;

        state = state.copyWith(
          status: newMessage.isEmpty && !loadMore ? AppStatus.empty : AppStatus.success,
          data: loadMore
              ? [...(state.data ?? []), ...newMessage]
              : newMessage,
          hasMore: hasMore,
          currentPage: currentPage,
          totalItems: response.data.totalRecords,
          errorMessage: null,
        );
        debugPrint(
            'Fetched ${newMessage.length} messages for thread $id, page: $currentPage/$totalPages, hasMore: $hasMore, search: $_currentSearchQuery');
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        debugPrint('Messages fetch error: $e');
      },
      onComplete: () => debugPrint('Messages fetch completed'),
    );
  }

  @override
  void reset() {
    state = const AppState<List<UserMessage>>();
  }

  void resetToIdle() {
    state = state.copyWith(status: AppStatus.idle, errorMessage: null);
  }

  void removeLastMessage() {
    final threads = state.data; // Adjust this if your field is named differently

    if (threads != null && threads.isNotEmpty) {
      state = state.copyWith(data: threads.sublist(0, threads.length - 1));
    } else {
      debugPrint('No messages to remove');
    }
  }

  // Add new message to the current thread messages list
  void addNewMessage(UserMessage newMessage) {
    final currentMessages = state.data ?? [];
    final updatedMessages = [...currentMessages, newMessage];

    state = state.copyWith(
      status: AppStatus.success,
      data: updatedMessages,
      totalItems: state.totalItems + 1,
    );
  }

  // Clear all messages for new chat
  void clearMessages() {
    state = const AppState<List<UserMessage>>();
  }

  /// Search messages by prompt
  Future<void> searchMessages({
    required BuildContext context,
    required int threadId,
    required String query,
  }) async {
    await fetchThreadsMessages(
      context: context,
      id: threadId,
      loadMore: false,
      search: query.isNotEmpty ? query : null,
    );
  }

  /// Clear search and reload messages
  Future<void> clearSearch({
    required BuildContext context,
    required int threadId,
  }) async {
    _currentSearchQuery = null;
    await fetchThreadsMessages(
      context: context,
      id: threadId,
      loadMore: false,
    );
  }
}