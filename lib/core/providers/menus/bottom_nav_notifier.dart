import '../../../app/imports/packages_imports.dart';

final bottomNavProvider =
    StateNotifierProvider<BottomNavNotifier, int>((ref) => BottomNavNotifier());

class BottomNavNotifier extends StateNotifier<int> {
  StatefulNavigationShell? _navigationShell;

  BottomNavNotifier() : super(0);

  void setNavigationShell(StatefulNavigationShell shell) {
    _navigationShell = shell;
    // Sync the state with the current navigation shell index after the build cycle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state != shell.currentIndex) {
        state = shell.currentIndex;
      }
    });
  }

  void onTabSelected(int index) {
      if (state == index) return;
    if (_navigationShell != null) {
      _navigationShell!.goBranch(index);
      state = index;
    }
  }
}
