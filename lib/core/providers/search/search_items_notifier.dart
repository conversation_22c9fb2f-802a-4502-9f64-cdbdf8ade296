import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/data/models/search_items_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/network/base_notifier.dart';
import 'package:mastercookai/core/network/network_utils.dart';

/// Provider for managing search items operations
final searchItemsNotifierProvider =
    StateNotifierProvider<SearchItemsNotifier, AppState<List<String>>>(
  (ref) => SearchItemsNotifier(ref),
);

class SearchItemsNotifier extends BaseNotifier<List<String>> {
  SearchItemsNotifier(Ref ref) : super(ref, const AppState<List<String>>());

  /// Search for items based on query
  Future<void> searchItems(String searchQuery) async {
    if (searchQuery.trim().isEmpty) {
      // Clear results if search query is empty
      state = state.copyWith(
        status: AppStatus.idle,
        data: [],
        errorMessage: null,
      );
      return;
    }

    callDataService(
      ref: ref,
      repo.searchItems(searchQuery.trim()),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (SearchItemsResponse response) {
        if (response.success && response.data?.items != null) {
          state = state.copyWith(
            status: AppStatus.success,
            data: response.data!.items!,
            errorMessage: null,
          );
        } else {
          // Handle case where API returns success but no items
          state = state.copyWith(
            status: AppStatus.empty,
            data: [],
            errorMessage: response.message.general.isNotEmpty
                ? response.message.general.first
                : 'No items found',
          );
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
          data: [],
        );
      },
      onComplete: () {}, // Optional completion callback
    );
  }

  /// Clear search results
  void clearSearch() {
    state = state.copyWith(
      status: AppStatus.idle,
      data: [],
      errorMessage: null,
    );
  }

  /// Reset the notifier state to initial state
  void reset() {
    state = const AppState<List<String>>();
  }

  /// Get current search results
  List<String> get searchResults => state.data ?? [];

  /// Check if currently searching
  bool get isSearching => state.status == AppStatus.loading;

  /// Check if search has results
  bool get hasResults => (state.data?.isNotEmpty ?? false) && state.status == AppStatus.success;

  /// Check if search returned empty results
  bool get isEmpty => state.status == AppStatus.empty || (state.data?.isEmpty ?? true);

  /// Check if there's an error
  bool get hasError => state.status == AppStatus.error;

  /// Get error message
  String? get errorMessage => state.errorMessage;
}
