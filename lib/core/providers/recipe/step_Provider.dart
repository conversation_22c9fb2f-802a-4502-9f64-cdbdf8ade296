// Define the StepState class
import '../../../app/imports/packages_imports.dart';

class StepState {
  final int current;

  StepState({required this.current});

  StepState copyWith({int? current}) {
    return StepState(current: current ?? this.current);
  }
}

// Define the StepNotifier
class StepNotifier extends StateNotifier<StepState> {
  StepNotifier() : super(StepState(current: 1));

  void nextStep() {
    state = state.copyWith(current: state.current + 1);
  }

  void prevStep() {
    if (state.current > 1) {
      state = state.copyWith(current: state.current - 1);
    }
  }

  void setStep(int step) {
    state = state.copyWith(current: step);
  }
}

// Define the provider
final stepProvider = StateNotifierProvider<StepNotifier, StepState>((ref) {
  return StepNotifier();
});