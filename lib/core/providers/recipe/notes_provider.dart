
import '../../../app/imports/packages_imports.dart';

final notesProvider = StateNotifierProvider<NotesNotifier, String?>((ref) {
  return NotesNotifier();
});

class NotesNotifier extends StateNotifier<String?> {
  NotesNotifier() : super(null);

  void setNote(String? note) {
    state = note;
  }

  /// Reset all author data to initial state
  void reset() {
    debugPrint("AuthorNotifier: Resetting to initial state");
    state = '';
  }
}