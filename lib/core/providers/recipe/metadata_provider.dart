import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/recipe_detail_response.dart';
import '../../data/models/recipe_response.dart' show Recipe;
import '../../data/request_query/recipe_meta_data.dart';

enum RecipeSection {
  BASIC,
  RECIPE_MEDIA,
  DIRECTION,
  INGREDIENT,
  AUTHOR,
  NOTES,
  OTHER,
}

final recipeMetadataProvider =
    StateNotifierProvider<RecipeMetadataNotifier, RecipeMetadata>((ref) {
  return RecipeMetadataNotifier();
});

class RecipeMetadataNotifier extends StateNotifier<RecipeMetadata> {
  RecipeMetadataNotifier() : super(RecipeMetadata());

  String? getWineDesc() => state.wineDesc;

  void updateRecipeId(int id) => state = state.copyWith(recipeId: id);

  void updateCookbookId(int id) => state = state.copyWith(cookbookId: id);

  void updateRecipesList(List<Recipe> recipes) =>
      state = state.copyWith(recipesList: recipes);

  void updateName(String? name) => state = state.copyWith(name: name);

  void updateDescription(String? description) =>
      state = state.copyWith(description: description);

  void updateIngredients(List<String>? ingredients) =>
      state = state.copyWith(ingredients: ingredients);

  void updateNutritionFacts(NutritionInfo? nutritionFacts) =>
      state = state.copyWith(nutritionFacts: nutritionFacts);

  void updateAuthor(String? author) => state = state.copyWith(author: author);

  void updateAuthorMediaUrl(String? url) =>
      state = state.copyWith(authorMediaUrl: url);

  void updateRecipeThumbnailFileUrl(String? url) =>
      state = state.copyWith(recipeThumbnailFileUrl: url);

  void updateCopyright(String? copyright) =>
      state = state.copyWith(copyright: copyright);

  void updateSource(String? source) => state = state.copyWith(source: source);

  void updateAuthorMedidaFile(File? authorMediaFile) =>
      state = state.copyWith(authorMediaFile: authorMediaFile);

  void updateDirections(List<Directions>? directions) => state = state.copyWith(directions: directions);

  void updateServingIdeas(String? servingIdeas) =>
      state = state.copyWith(servingIdeas: servingIdeas);

  void updateWine(String? wine) => state = state.copyWith(wine: wine);

  void updateRecipeMedia(List<RecipeMedia>? recipeMedia) =>
      state = state.copyWith(recipeMedia: recipeMedia);

  void updateCategoryId(int? id) => state = state.copyWith(categoryId: id);

  void updateCuisineId(int? id) => state = state.copyWith(cuisineId: id);

  void updateYield(String? value) => state = state.copyWith(yieldValue: value);

  void updateServings(int? value) => state = state.copyWith(servings: value);

  void updatePrepTime(String? value) => state = state.copyWith(prepTime: value);

  void updateCookTime(String? value) => state = state.copyWith(cookTime: value);

  void updateTotalTime(String? value) =>
      state = state.copyWith(totalTime: value);

  void updateWineDesc(String? value) => state = state.copyWith(wineDesc: value);

  // Method to update all data at once
  void updateAll({
    required int recipeId,
    required int cookbookId,
    required List<Recipe> recipesList,
    required RecipeDetails recipeDetails,
    required int? categoryId,
    required int? cuisineId,
  }) {
    state = RecipeMetadata(
      recipeId: recipeId,
      cookbookId: cookbookId,
      recipesList: recipesList,
      name: recipeDetails.name,
      description: recipeDetails.description,
      ingredients: recipeDetails.ingredients,
      nutritionFacts: recipeDetails.nutritionInfo,
      author: recipeDetails.author,
      authorMediaUrl: recipeDetails.authorMediaUrl,
      copyright: recipeDetails.copyright,
      source: recipeDetails.source,
      directions: recipeDetails.directions,
      servingIdeas: recipeDetails.servingIdeas,
      wine: recipeDetails.wine,
      recipeMedia: recipeDetails.recipeMedia,
      recipeThumbnailFileUrl: recipeDetails.recipeThumbnailFileUrl,
      categoryId: categoryId,
      cuisineId: cuisineId,
      yieldValue: state.yieldValue,
      servings: state.servings,
      prepTime: state.prepTime,
      cookTime: state.cookTime,
      totalTime: state.totalTime,
      wineDesc: state.wineDesc,
      AiImageUrl: state.AiImageUrl,
      AiMessageId: state.AiMessageId,
    );
  }

  // Method to set state with default values
  void set(
      {int? recipeId,
      int? cookbookId,
      List<Recipe>? recipesList,
      String? name,
      String? description,
      List<String>? ingredients,
      NutritionInfo? nutritionFacts,
      String? author,
      String? authorMediaUrl,
      String? recipeThumbnailFileUrl,
      File? authorMediaFile,
      String? copyright,
      String? source,
      List<Directions>? directions,
      String? servingIdeas,
      String? wine,
      List<RecipeMedia>? recipeMedia,
      int? categoryId,
      int? cuisineId,
      String? yieldValue,
      int? servings,
      String? prepTime,
      String? cookTime,
      String? totalTime,
      String? wineDesc,
      String? AiImageUrl,
      String? AiMessageId}) {
    state = RecipeMetadata(
        recipeId: recipeId ?? 0,
        cookbookId: cookbookId ?? 0,
        recipesList: recipesList ?? [],
        name: name,
        description: description,
        ingredients: ingredients,
        nutritionFacts: nutritionFacts,
        author: author,
        authorMediaUrl: authorMediaUrl,
        authorMediaFile: authorMediaFile,
        recipeThumbnailFileUrl: recipeThumbnailFileUrl,
        copyright: copyright,
        source: source,
        directions: directions,
        servingIdeas: servingIdeas,
        wine: wine,
        recipeMedia: recipeMedia,
        categoryId: categoryId,
        cuisineId: cuisineId,
        yieldValue: yieldValue,
        servings: servings,
        prepTime: prepTime,
        cookTime: cookTime,
        totalTime: totalTime,
        wineDesc: wineDesc,
        AiImageUrl: AiImageUrl,
        AiMessageId: AiMessageId);
  }

  // Method to reset state for a new ad
  void reset() {
    state = RecipeMetadata(
      recipeId: 0,
      cookbookId: 0,
      recipesList: [],
      name: null,
      description: null,
      ingredients: null,
      nutritionFacts: null,
      author: null,
      authorMediaUrl: null,
      authorMediaFile: null,
      recipeThumbnailFileUrl: null,
      copyright: null,
      source: null,
      directions: null,
      servingIdeas: null,
      wine: null,
      recipeMedia: null,
      categoryId: null,
      cuisineId: null,
      yieldValue: null,
      servings: null,
      prepTime: null,
      cookTime: null,
      totalTime: null,
      wineDesc: null,
      AiImageUrl: null,
      AiMessageId: null,
    );
  }

  // In your recipe metadata provider
  void resetFilters() {
    state = state.copyWith(
      cuisineId: 0,
      categoryId: 0,
    );
  }
}
