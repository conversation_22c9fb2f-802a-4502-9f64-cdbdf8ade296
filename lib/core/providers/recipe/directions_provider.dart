import 'dart:convert';
import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../app/imports/core_imports.dart';
import '../../data/models/direction_step.dart';
import '../../data/models/recipe_detail_response.dart';


// Provider
final directionStepsProvider =
StateNotifierProvider<DirectionStepsNotifier, List<DirectionStep>>((ref) {
  return DirectionStepsNotifier();
});

class DirectionStepsNotifier extends StateNotifier<List<DirectionStep>> {
  DirectionStepsNotifier() : super([]);

  List<File?> _mediaFiles = []; // Track media files, allowing null entries

  // Input state for the top input section
  String _currentInputDescription = '';
  File? _currentInputImage;
  int? _currentEditingIndex;

  List<File>? get mediaFiles => _mediaFiles.whereType<File>().toList();

  // Getters for input state
  String get currentInputDescription => _currentInputDescription;
  File? get currentInputImage => _currentInputImage;
  int? get currentEditingIndex => _currentEditingIndex;

  void addStep() {
    state = [
      ...state,
      DirectionStep(
        title: "",
        description: "",
        mediaFile: null,
        mediaFileName: null,
        mediaFileId: null,
      ),
    ];
    _mediaFiles = [..._mediaFiles, null];
    debugPrint("Added step, new state: ${state.map((e) => e.toJson()).toList()}");
  }

  void updateStep(
      int index, {
        String? title,
        String? description,
        File? mediaFile,
        bool? removeMediaFile,
        String? mediaFileName,
        bool? removeMediaFileName,
        int? mediaFileId,
        String? imageUrl,
      }) {
    if (index < 0 || index >= state.length) {
      debugPrint("Invalid index $index for updateStep");
      return;
    }

    final current = state[index];
    bool hasChanges = false;

    final updatedFields = <String, dynamic>{};

    if (title != null) {
      updatedFields['title'] = title;
      hasChanges = true;
    }
    if (description != null) {
      updatedFields['description'] = description;
      hasChanges = true;
    }
    if (mediaFile != null) {
      updatedFields['mediaFile'] = mediaFile;
      updatedFields['mediaFileName'] = mediaFileName;
      updatedFields['mediaFileId'] = null; // Clear MediaFileId for new media
      hasChanges = true;
    }
    if (removeMediaFile == true) {
      updatedFields['mediaFile'] = null;
      updatedFields['mediaFileName'] = null;
      updatedFields['mediaFileId'] = null; // Clear MediaFileId on removal
      hasChanges = true;
    }
    if (mediaFileName != null) {
      updatedFields['mediaFileName'] = mediaFileName;
      updatedFields['mediaFileId'] = null; // Clear MediaFileId if MediaFileName is set
      hasChanges = true;
    }
    if (removeMediaFileName == true) {
      updatedFields['mediaFileName'] = null;
      updatedFields['mediaFileId'] = null; // Clear MediaFileId on removal
      hasChanges = true;
    }
    if (mediaFileId != null) {
      updatedFields['mediaFileId'] = mediaFileName != null && mediaFileName!.isNotEmpty
          ? null
          : mediaFileId; // Respect MediaFileName rule
      hasChanges = true;
    }
    if (imageUrl != null) {
      updatedFields['imageUrl'] = imageUrl;
      hasChanges = true;
    }

    if (hasChanges) {
      final updatedStep = DirectionStep(
        title: updatedFields['title'] ?? current.title,
        description: updatedFields['description'] ?? current.description,
        mediaFile: updatedFields['mediaFile'] ?? current.mediaFile,
        mediaFileName: updatedFields['mediaFileName'] ?? current.mediaFileName,
        mediaFileId: updatedFields['mediaFileId'] ?? current.mediaFileId,
        imageUrl: updatedFields['imageUrl'] ?? current.imageUrl,
      );

      state[index] = updatedStep;
      _mediaFiles[index] = updatedFields['mediaFile'] ?? current.mediaFile;
      state = [...state]; // Trigger rebuild
      debugPrint("Updated step at index $index: ${updatedStep.toJson()}");
    } else {
      debugPrint("No changes to update for step at index $index");
    }
  }

  void setDirections(List<Directions> backendDirections) {
    state = backendDirections.map((direction) {
      var id=direction.mediaFileId;

      return DirectionStep(
         title: direction.title ?? "",
          description: direction.description ?? "",
          imageUrl: direction.mediaUrl ?? '',
          mediaFileId: id,
      );
    }).toList();
   _mediaFiles = List.filled(state.length, null);
    // Don't automatically create empty steps - let the UI handle empty states
    debugPrint("Set directions: ${state.map((e) => e.toJson()).toList()}");
  }

  void removeStep(int index) {
  //  if (state.length <= 1) {
  //     debugPrint("Cannot remove step at index $index: at least one step required");
  //     return;
  //  }
    final list = [...state]..removeAt(index);
    _mediaFiles = [..._mediaFiles]..removeAt(index);
    state = list;
    debugPrint(
        "Removed step at index $index, new state: ${state.map((e) => e.toJson()).toList()}");
  }

  void removeMediaFile(int index) {
    if (index < 0 || index >= state.length) {
      debugPrint("Invalid index $index for removeMediaFile");
      return;
    }
    final current = state[index];
    final updatedStep = DirectionStep(
      title: current.title,
      description: current.description,
      imageUrl: '',
      mediaFile: null,
      mediaFileName: null,
      mediaFileId: null, // Explicitly set mediaFileId to null
    );
    state[index] = updatedStep;
    _mediaFiles[index] = null;
    state = [...state]; // Trigger rebuild
    debugPrint("Removed media for step at index $index: ${updatedStep.toJson()}");
  }

  void resetWithDefaults({int defaultCount = 3}) {
    clearDirections();
    for (int i = 0; i < defaultCount; i++) {
      state = [...state, DirectionStep(title: "", description: "")];
      _mediaFiles = [..._mediaFiles, null];
    }
    debugPrint(
        "Reset with $defaultCount empty steps. New state: ${state.map((e) => e.toJson()).toList()}");
  }

  void clearDirections() {
    state = [];
    _mediaFiles = [];
    debugPrint("Cleared directions. New state: $state");
  }

  String get directionsJson {
    // Filter out empty objects and encode the remaining ones
    final validSteps = state
        .map((e) => e.toJson())
        .where((json) => json.isNotEmpty)
        .toList();
    return jsonEncode(validSteps);
  }

  // Methods to manage input state
  void updateInputDescription(String description) {
    _currentInputDescription = description;
  }

  void updateInputImage(File? image) {
    _currentInputImage = image;
  }

  void setEditingIndex(int? index) {
    _currentEditingIndex = index;
  }

  void clearInputState() {
    _currentInputDescription = '';
    _currentInputImage = null;
    _currentEditingIndex = null;
  }
}