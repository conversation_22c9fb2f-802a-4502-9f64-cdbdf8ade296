import 'package:mastercookai/core/network/app_status.dart';
import '../../../app/imports/packages_imports.dart';
import '../data/models/nutritionInfo_response.dart';
import '../network/base_notifier.dart';
import '../network/network_utils.dart';
import '../utils/Utils.dart';

// Provider for SingleRecipeNotifier
final nutritionInfoNotifierProvider =
    StateNotifierProvider<NutrionsRecipeNotifier, AppState<NutritionInfoData>>(
  (ref) => NutrionsRecipeNotifier(ref),
);

class NutrionsRecipeNotifier extends BaseNotifier<NutritionInfoData> {
  NutrionsRecipeNotifier(Ref ref)
      : super(ref, const AppState<NutritionInfoData>());

  Future<void> getNutritions({
    required BuildContext context,
    required int recipeId,
  }) async {
    callDataService(
      ref: ref,
      repo.getNutritions(recipeId: recipeId),
      onStart: () => state = state.copyWith(
        status: AppStatus.loading,
        data: null, // Clear previous nutrition data immediately
        errorMessage: null, // Clear previous error messages
      ),
      onSuccess: (NutritionInfoResponse response) {
        if (response.status == 200) {
          if (response.data?.nutritionInfo != null) {
             state = state.copyWith(status: AppStatus.success, data: response.data?.nutritionInfo);
          } else {
             // When API returns success but no nutrition data, treat as success with empty data
             // Extract the message from the API response and clean HTML
             String message = 'No Nutritions Available';
             if (response.message?.general != null && response.message!.general!.isNotEmpty) {
               message = Utils().cleanHtmlText(response.message!.general!.first);
             }
             state = state.copyWith(
               status: AppStatus.success,
               data: null,
               errorMessage: message
             );
          }
        } else {
          state = state.copyWith(
            status: AppStatus.error,
            data: null,
            errorMessage: 'Failed to load nutrition data'
          );
        }
      },
      onError: (e) {
        // Extract error message from API response and clean HTML
        String errorMessage = 'Failed to load nutrition data';

        // Safely extract the error message, handling different exception types
        try {
          String rawMessage = e.message;
          // Clean HTML tags and entities from error message
          errorMessage = Utils().cleanHtmlText(rawMessage);
        } catch (ex) {
          // If there's an issue accessing the message, use default message
          debugPrint('Error accessing exception message: $ex');
        }

        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: errorMessage
        );

        debugPrint('Nutrition fetch error: $e');
        debugPrint('Error message: $errorMessage');
      },
      onComplete: () => debugPrint('Recipe fetch completed'),
    );
  }

  /// Reset nutrition state - useful when switching recipes
  void resetNutritionState() {
    state = const AppState<NutritionInfoData>();
  }
}
