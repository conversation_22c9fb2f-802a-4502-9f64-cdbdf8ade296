import 'package:flutter/material.dart';
import 'package:mastercookai/core/data/models/cuisines_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../../app/imports/packages_imports.dart';
import '../network/base_notifier.dart';
import '../network/network_utils.dart';


// Provider for CuisinesNotifier
final cuisinesNotifierProvider = StateNotifierProvider<CuisinesNotifier, AppState<List<Cuisines>>>(
      (ref) => CuisinesNotifier(ref),
);

class CuisinesNotifier extends BaseNotifier<List<Cuisines>> {
  CuisinesNotifier(Ref ref) : super(ref, const AppState<List<Cuisines>>());

  Future<void> fetchCuisines({
    required BuildContext context,
    bool reset = false,
    int pageSize = 20,
  }) async {
    if (!reset && (state.hasMore == false || state.status == AppStatus.loadingMore)) {
      return;
    }

    final currentPage = reset ? 1 : state.currentPage + 1;

    callDataService(
      ref: ref,
      repo.fetchCuisines(),
      onStart: () => state = state.copyWith(
        status: reset ? AppStatus.loading : AppStatus.loadingMore,
        data: reset ? null : state.data,
        hasMore: reset ? true : state.hasMore,
        currentPage: currentPage,
      ),
      onSuccess: (CuisinesResponse response) {
        state = state.copyWith(status: AppStatus.success,data: response.data!.cuisines);
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to fetch cuisines',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        debugPrint('Cuisines fetch error: $e');
      },
      onComplete: () => debugPrint('Cuisines fetch completed'),
    );
  }
}