import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/home_model.dart';

import '../../data/request_query/home_request.dart';
import '../../network/app_status.dart';
import '../../network/base_notifier.dart';
import '../../network/network_utils.dart';
import '../../utils/Utils.dart';



final homeNotifierProvider =
StateNotifierProvider<HomeNotifier, AppState<HomeModel>>(
      (ref) => HomeNotifier(ref),
);





class HomeNotifier extends BaseNotifier<HomeModel> {
  HomeNotifier(Ref ref) : super(ref,const AppState<HomeModel>()) {
    _initialize();
  }

  Future<void> _initialize() async {
  }

  void setLoggedIn(bool isLoggedIn) {
    state = state.copyWith(
      status: isLoggedIn ? AppStatus.success : AppStatus.idle,
    );
  }

  Future<void> banners(
      BuildContext context, String type) async {
    callDataService(
      ref: ref,
      repo.banner(HomeQueryParam(
        type: type,
      )),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) {
        state = state.copyWith(status: AppStatus.success,data: response);

      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Login failed',
        );
        Utils().showFlushbar(context, message: state.errorMessage!,isError: true);      },
    );
  }



  Future<void> _handleSuccessResponse(BuildContext context, dynamic response, String type) async {

    state = state.copyWith(status: AppStatus.success);
    if (context.mounted) {
      // await localStorage.saveLoginData(
      //   loginModel: response,
      //   type: type,
      // );
      // context.go('/home');
    }
  }


  void reset() {
    state = const AppState<HomeModel>();
  }
}
