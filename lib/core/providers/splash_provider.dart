import 'package:flutter_riverpod/flutter_riverpod.dart';


final splashControllerProvider = StateNotifierProvider<SplashController, bool>(
      (ref) => SplashController(),
);

class SplashController extends StateNotifier<bool> {
  SplashController() : super(false) {
    _startTimer();
  }

  void _startTimer() async {
    await Future.delayed(const Duration(seconds: 2));
    state = true; // Ready to navigate
  }
}
