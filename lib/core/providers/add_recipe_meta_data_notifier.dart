import 'package:flutter_riverpod/flutter_riverpod.dart';

class AddRecipeMetadataState {
  final String? selectedCategory;
  final String? selectedCuisine;

  AddRecipeMetadataState({this.selectedCategory, this.selectedCuisine});

  AddRecipeMetadataState copyWith({
    String? selectedCategory,
    String? selectedCuisine,
  }) {
    return AddRecipeMetadataState(
      selectedCategory: selectedCategory ?? this.selectedCategory,
      selectedCuisine: selectedCuisine ?? this.selectedCuisine,
    );
  }
}

final addRecipeMetadataProvider =
    StateNotifierProvider<AddRecipeMetadataNotifier, AddRecipeMetadataState>(
  (ref) => AddRecipeMetadataNotifier(),
);

class AddRecipeMetadataNotifier extends StateNotifier<AddRecipeMetadataState> {
  AddRecipeMetadataNotifier() : super(AddRecipeMetadataState());

  void setCategory(String category) {
    state = state.copyWith(selectedCategory: category);
  }

  void setCuisine(String cuisine) {
    state = state.copyWith(selectedCuisine: cuisine);
  }

  void reset() {
    state = AddRecipeMetadataState();
  }
}
