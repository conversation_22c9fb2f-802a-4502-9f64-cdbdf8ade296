import 'package:flutter_riverpod/flutter_riverpod.dart';

final selectedMealPlanProvider = StateProvider<int>((ref) => 0);

final mealPlansProvider = StateProvider<List<Map<String, dynamic>>>((ref) => [
  {
    "title": "Dinner Party",
    "note": "Avoid Sugar",
    "items": ["Spanish omelette", "Tater tots", "Sourdough bread", "Milk", "Pot roast" ],
    "servings": 5,
  },
  {
    "title": "Dinner Party",
    "note": "Avoid Sugar",
    "items": ["Spanish omelette", "Tater tots", "Sourdough bread", "Milk", "Pot roast"],
    "servings": 5,
  },
  {
    "title": "Dinner Party",
    "note": "Avoid Sugar",
    "items": ["Spanish omelette", "Tater tots", "Sourdough bread", "Milk", "Pot roast"],
    "servings": 5,
  },
  {
    "title": "Dinner Party",
    "note": "Avoid Sugar",
    "items": ["Spanish omelette", "Tater tots", "Sourdough bread", "Milk", "Pot roast" ],
    "servings": 5,
  },
  // Add more mock data...
]);
