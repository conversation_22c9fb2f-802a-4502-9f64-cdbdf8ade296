import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/shopping_response.dart';
import 'package:mastercookai/core/data/request_query/paginantion_request.dart';
import 'package:mastercookai/core/data/request_query/shopping_item_request.dart' as shopping_request;
import 'package:mastercookai/core/helpers/app_constant.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/network/base_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';

import '../../../app/imports/packages_imports.dart';
import '../../data/models/shopping.dart';
import '../../data/partner_api.dart';
import '../../network/network_utils.dart';
import 'shopping_notifier.dart';

final shoppingItemNotifierProvider =
StateNotifierProvider<ShoppingItemNotifier, AppState<ShoppingListItemData>>(
      (ref) => ShoppingItemNotifier(ref),
);

class ShoppingItemNotifier extends BaseNotifier<ShoppingListItemData> {
  ShoppingItemNotifier(Ref ref) : super(ref, const AppState<ShoppingListItemData>());

  bool get canFetchMore => state.hasMore && state.status != AppStatus.loadingMore;

  /// Add shopping items to a shopping list
  Future<(bool, String?)> addShoppingItems({
    required int shoppingListId,
    required shopping_request.ShoppingItemRequest request,
  }) async {
    if (request.shoppingItems?.isEmpty ?? true) {
      const errorMessage = 'No shopping items provided';
      return (false, errorMessage);
    }

    state = state.copyWith(status: AppStatus.loading);

    try {
      final repositoryImpl = ref.read(partnerApiProvider);
      final success = await repositoryImpl.addShoppingItems(shoppingListId, request);

      if (success) {
        state = state.copyWith(status: AppStatus.success, errorMessage: null);
        await fetchShoppingListsItems(id: shoppingListId); // Refresh items
        // Refresh main shopping lists to update product count
        ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists();
        return (true, null);
      } else {
        const errorMessage = 'Failed to add shopping items';
        state = state.copyWith(status: AppStatus.error, errorMessage: errorMessage);
        return (false, errorMessage);
      }
    } catch (e) {
      final errorMessage = _handleError(e);
      state = state.copyWith(status: AppStatus.error, errorMessage: errorMessage);
      return (false, errorMessage);
    }
  }

  /// Delete shopping items from a shopping list
  Future<(bool, String?)> deleteShoppingItems({
    required int shoppingListId,
    required shopping_request.DeleteShoppingItemsRequest request,
  }) async {
    if (request.type == null) {
      const errorMessage = 'Delete type is required';
      return (false, errorMessage);
    }

    state = state.copyWith(status: AppStatus.loading);

    try {
      final repositoryImpl = ref.read(partnerApiProvider);
      final response = await repositoryImpl.deleteShoppingItems(shoppingListId, request);

      if (response.success == true) {
        // Reset state to ensure clean refresh after delete
        state = state.copyWith(
          status: AppStatus.success,
          errorMessage: null,
          hasMore: true,
          currentPage: 1,
        );
        await fetchShoppingListsItems(id: shoppingListId); // Refresh items
        // Refresh main shopping lists to update product count
        ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists();
        return (true, null);
      } else {
        const errorMessage = 'Failed to delete shopping items';
        state = state.copyWith(status: AppStatus.error, errorMessage: errorMessage);
        return (false, errorMessage);
      }
    } catch (e) {
      final errorMessage = _handleError(e);
      state = state.copyWith(status: AppStatus.error, errorMessage: errorMessage);
      return (false, errorMessage);
    }
  }

  /// Fetch shopping items for a specific shopping list
  Future<bool> fetchShoppingListsItems({
    BuildContext? context,
    required int id,
    String? search,
    bool loadMore = false,
  }) async {
   // final loadMore = state.data != null && state.data!.shoppingItems.isNotEmpty;
    if (loadMore && (!state.hasMore || state.status == AppStatus.loadingMore)) {
      return false;
    }

    // If not loading more, reset any previous loadingMore state
    if (!loadMore && state.status == AppStatus.loadingMore) {
      state = state.copyWith(status: AppStatus.idle);
    }

    final currentPage = loadMore ? state.currentPage + 1 : 1;
    bool isSuccess = false;

    final paginationParam = PaginationQueryParam(
      pageNumber: currentPage,
      pageSize: 20,
    );

    if (search != null && search.isNotEmpty) {
      paginationParam.search = search;
    }
    // Only show global loader for initial load, not for load more
    if (!loadMore) {
      showLoader();
    }

    await callDataService(
      ref: ref,
      ref.read(partnerApiProvider).fetchShoppingListsItems(id, paginationParam),
      onStart: () => state = state.copyWith(
        status: loadMore ? AppStatus.loadingMore : AppStatus.loading,
        data: loadMore ? state.data : null,
        hasMore: loadMore ? state.hasMore : true,
        currentPage: currentPage,
      ),
      onSuccess: (response) {
        _handleShoppingItemSuccessResponse(response, loadMore);
        isSuccess = true;
        if (!loadMore) {
          hideLoader();
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.toString(),
        );
        if (context?.mounted == true) {
          Utils().showFlushbar(context!, message: e.toString(), isError: true);
        }
        isSuccess = false;
        if (!loadMore) {
          hideLoader();
        }
      },
      onComplete: () => {},
    );

    return isSuccess;
  }

  void _handleShoppingItemSuccessResponse(ShoppingListItemResponse response, bool loadMore) {
    final result = response.data;
    state = state.copyWith(
      status: result.shoppingItems.isEmpty && !loadMore
          ? AppStatus.empty
          : AppStatus.success,
      data: loadMore
          ? ShoppingListItemData(
        shoppingItems: [...state.data?.shoppingItems ?? [], ...result.shoppingItems],
        totalCost: result.totalCost,
        totalRecords: result.totalRecords,
        totalPageCount: result.totalPageCount,
      )
          : result,
      hasMore: result.shoppingItems.length == 20,
      currentPage: state.currentPage,
      totalItems: result.totalPageCount.toInt(),
      errorMessage: null,
    );
  }

  String _handleError(dynamic error) {
    if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    } else {
      return 'An unexpected error occurred: ${error.toString()}';
    }
  }

  @override
  void reset() {
    state = const AppState<ShoppingListItemData>();
  }

  void resetToIdle() {
    state = state.copyWith(status: AppStatus.idle, errorMessage: null);
  }

  /// Clear shopping items data - useful when switching between shopping lists
  void clearData() {
    state = state.copyWith(
      data: null,
      status: AppStatus.idle,
      errorMessage: null,
      hasMore: true,
      currentPage: 1,
    );
  }
}
