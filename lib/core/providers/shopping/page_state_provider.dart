import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../app/imports/core_imports.dart';
import '../../../presentation/shopping/sub_view/shopping_dialog.dart';

/// Provider to store the current page state for shopping and pantry views
/// This ensures page state is preserved when navigating back from detail screens
class PageState {
  final int shoppingCurrentPage;
  final int pantryCurrentPage;

  const PageState({
    this.shoppingCurrentPage = 0,
    this.pantryCurrentPage = 0,
  });

  PageState copyWith({
    int? shoppingCurrentPage,
    int? pantryCurrentPage,
  }) {
    return PageState(
      shoppingCurrentPage: shoppingCurrentPage ?? this.shoppingCurrentPage,
      pantryCurrentPage: pantryCurrentPage ?? this.pantryCurrentPage,
    );
  }
}

class PageStateNotifier extends StateNotifier<PageState> {
  PageStateNotifier() : super(const PageState());

  void updateShoppingPage(int page) {
    state = state.copyWith(shoppingCurrentPage: page);
  }

  void updatePantryPage(int page) {
    state = state.copyWith(pantryCurrentPage: page);
  }

  void resetShoppingPage() {
    state = state.copyWith(shoppingCurrentPage: 0);
  }

  void resetPantryPage() {
    state = state.copyWith(pantryCurrentPage: 0);
  }

  void showShoppinDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => ShoppingDialog(
        dialogType: DialogType.shopping,
        title: 'Create Shopping List',
        hintText: 'Enter shopping item name',
        successText: 'Shopping list created\nsuccessfully!',
        buttonText: 'Create now',
        successButtonText: 'Go to Shopping list',
      ),
    );
  }

  void showPantryDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => ShoppingDialog(
        dialogType: DialogType.pantry,
        title: 'Create Pantry List',
        hintText: 'Enter Pantry name',
        successText: 'Pantry list created\nsuccessfully!',
        buttonText: 'Create now',
        successButtonText: 'Go to Pantry list',
      ),
    );
  }
}

final pageStateProvider = StateNotifierProvider<PageStateNotifier, PageState>((ref) {
  return PageStateNotifier();
});
