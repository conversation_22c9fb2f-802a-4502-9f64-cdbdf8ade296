import 'dart:io';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/data/models/shopping_response.dart';
import 'package:mastercookai/core/data/request_query/paginantion_request.dart';
import 'package:mastercookai/core/data/request_query/shopping_item_request.dart'
    as shopping_request;
import 'package:mastercookai/core/data/partner_api.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';
import 'package:mastercookai/core/network/network_utils.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/image_cropper/image_cropper.dart';
import 'package:mastercookai/core/helpers/app_constant.dart' as app_constant;

import '../../../presentation/shopping/mobile/sub_view/shopping_pantry_bottom_sheet_content.dart';
import '../../helpers/app_constant.dart';
import '../../network/app_status.dart';
import '../../network/base_notifier.dart';

final shoppingNotifierProvider =
    StateNotifierProvider<ShoppingNotifier, AppState<ShoppingResponseData>>(
  (ref) => ShoppingNotifier(ref),
);

class ShoppingNotifier extends BaseNotifier<ShoppingResponseData> {
  ShoppingNotifier(Ref ref)
      : super(ref, const AppState<ShoppingResponseData>()) {
    _initialize();
  }

  void _initialize() {
    fetchShoppingLists();
  }

  /// [shoppingListId] - The ID of the shopping list to add items to
  /// [request] - The shopping items request containing the items to add
  Future<(bool, String?)> addShoppingItems({
    required int shoppingListId,
    required shopping_request.ShoppingItemRequest request,
  }) async {
    if (request.shoppingItems?.isEmpty ?? true) {
      const errorMessage = 'No shopping items provided';
      return (false, errorMessage);
    }

    state = state.copyWith(status: AppStatus.loading);

    try {
      final repositoryImpl = ref.read(partnerApiProvider);
      final success =
          await repositoryImpl.addShoppingItems(shoppingListId, request);

      if (success) {
        state = state.copyWith(status: AppStatus.success, errorMessage: null);
        return (true, null);
      } else {
        const errorMessage = 'Failed to add shopping items';
        state =
            state.copyWith(status: AppStatus.error, errorMessage: errorMessage);
        return (false, errorMessage);
      }
    } catch (e) {
      final errorMessage = _handleError(e);
      state =
          state.copyWith(status: AppStatus.error, errorMessage: errorMessage);
      return (false, errorMessage);
    }
  }

  /// Update Shopping
  Future<void> updateShopping(BuildContext context,
      {required String id,
      required String name,
      required File filePath,
      required bool callFromUpdate}) async {
    await callDataService(
      ref: ref,
      ref.read(partnerApiProvider).updateShopping(id, name, filePath),
      onStart: () {
        app_constant.showLoader();
        state = state.copyWith(status: AppStatus.updating);
      },
      onSuccess: (response) async {
        app_constant.hideLoader();
        state = state.copyWith(status: AppStatus.updateSuccess);
        if (callFromUpdate && context.mounted) {
          await fetchShoppingLists(context: context);
        }
        if (callFromUpdate && context.mounted && Navigator.canPop(context)) {
          Navigator.pop(context);
        }
      },
      onError: (e) {
        app_constant.hideLoader();
        final errorMessage = _handleError(e);
        state = state.copyWith(
          status: AppStatus.updateError,
          errorMessage: errorMessage,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: errorMessage, isError: true);
        }
      },
      onComplete: () => {},
    );
  }

  /// GET - Fetch shopping lists with pagination
  Future<bool> fetchShoppingLists({
    BuildContext? context,
    bool loadMore = false,
    String? search,
  }) async {
    if (loadMore && (!state.hasMore || state.status == AppStatus.loadingMore)) {
      return false;
    }

    final currentPage = loadMore ? state.currentPage + 1 : 1;
    bool isSuccess = false;

    final paginationParam = PaginationQueryParam(
      pageNumber: currentPage,
      pageSize: 10,
    );

    if (search != null && search.isNotEmpty) {
      paginationParam.search = search;
    }

    await callDataService(
      ref: ref,
      ref.read(partnerApiProvider).getShoppingList(paginationParam),
      onStart: () => state = state.copyWith(
        status: loadMore ? AppStatus.loadingMore : AppStatus.loading,
        data: loadMore ? state.data : null,
        hasMore: loadMore ? state.hasMore : true,
        currentPage: currentPage,
      ),
      onSuccess: (response) {
        _handleSuccessResponse(response, loadMore);
        isSuccess = true;
      },
      onError: (e) {
        final errorMessage = _handleError(e);
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: errorMessage,
        );
        if (context?.mounted == true) {
          Utils().showFlushbar(context!, message: errorMessage, isError: true);
        }
        isSuccess = false;
      },
      onComplete: () => {},
    );

    return isSuccess;
  }

  /// POST - Create new shopping list
  Future<void> createShoppingList(BuildContext context, String name) async {
    await callDataService(
      ref: ref,
      ref.read(partnerApiProvider).createShoppingList(name),
      onStart: () => state = state.copyWith(status: AppStatus.creating),
      onSuccess: (response) async {
        if (response.success == false) {
          // Handle error response from API
          String errorMessage = 'Failed to create shopping list';

          if (response.message?.error != null) {
            // Handle error field - it can be List<String> or dynamic
            if (response.message!.error is List) {
              final errorList = response.message!.error as List;
              if (errorList.isNotEmpty) {
                errorMessage = errorList.first.toString();
              }
            } else if (response.message!.error is String) {
              errorMessage = response.message!.error.toString();
            }
          } else if (response.message?.general?.isNotEmpty == true) {
            errorMessage = response.message!.general!.first;
          }

          state = state.copyWith(
            status: AppStatus.createError,
            errorMessage: errorMessage,
          );
          if (context.mounted) {
            Utils().showFlushbar(context, message: errorMessage, isError: true);
          }
          return;
        }
        state = state.copyWith(status: AppStatus.createSuccess);
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.createError,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: e.message, isError: true);
        }
      },
      onComplete: () => {},
    );
  }

  /// DELETE
  Future<(AppStatus, String?)> deleteShoppingList(
      BuildContext context, String id) async {
    state = state.copyWith(status: AppStatus.loading);

    try {
      showLoader();
      final repositoryImpl = ref.read(partnerApiProvider);
      final response = await repositoryImpl.deleteShoppingList(id);

      if (response.success == true) {
        if (context.mounted) {
          Utils().showFlushbar(context,
              message:
                  response.message?.general?.first ?? 'Deleted successfully',
              isError: false);
          // Refresh the shopping lists and ensure proper state management
          await fetchShoppingLists(context: context);
        }
        return (AppStatus.success, null);
      } else {
        final errorMessage = response.message?.general?.isNotEmpty == true
            ? response.message!.general!.first
            : 'Failed to delete shopping list';
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: errorMessage,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: errorMessage, isError: true);
        }
        return (AppStatus.error, errorMessage);
      }
    } catch (e) {
      final errorMessage = _handleError(e);
      state = state.copyWith(
        status: AppStatus.error,
        errorMessage: errorMessage,
      );
      if (context.mounted) {
        Utils().showFlushbar(context, message: errorMessage, isError: true);
      }
      return (AppStatus.error, errorMessage);
    } finally {
      hideLoader();
    }
  }

  /// Pick image for shopping list cover
  Future<void> pickImage(
      BuildContext context, WidgetRef ref, String id, String name) async {
    final file = await MediaPickerService.pickSingleImage();
    if (file != null && context.mounted) {
      final result = await Navigator.of(context).push<File?>(
        MaterialPageRoute(
          builder: (context) => ImageCropper(
            pickedImage: file,
            showCropPresets: true,
            showGridLines: false,
            useDelegate: false,
            // Changed to false to get the file back
            enableFreeformCrop: true,
          ),
        ),
      );

      // Handle the cropped image result
      if (result != null && context.mounted) {
        await updateShopping(
          context,
          id: id,
          name: name,
          filePath: result,
          callFromUpdate: true,
        );
      }
    }
  }

  /// Handle errors and return user-friendly error messages
  String _handleError(dynamic error) {
    if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    } else {
      return 'An unexpected error occurred: ${error.toString()}';
    }
  }

  void _handleSuccessResponse(ShoppingResponse response, bool loadMore) {
    final result = response.data;
    state = state.copyWith(
      status: result!.shoppingLists!.isEmpty && !loadMore
          ? AppStatus.empty
          : AppStatus.success,
      data: loadMore
          ? ShoppingResponseData(
              shoppingLists: [
                ...state.data?.shoppingLists ?? [],
                ...result.shoppingLists!
              ],
              totalRecords: result.totalRecords,
              totalPageCount: result.totalPageCount,
            )
          : result,
      hasMore: result.shoppingLists!.length == 10,
      currentPage: state.currentPage,
      totalItems: result.totalPageCount!.toInt(),
      errorMessage: null,
    );
  }

  Future<void> showShoppingBottomSheet({
    required BuildContext context,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
        final screenHeight = MediaQuery.of(context).size.height;

        // More precise height calculation for compact content
        final contentHeight = 280.0; // Estimated content height
        final baseHeight = contentHeight + 40; // Content + padding
        final minHeight = screenHeight * 0.25; // 25% minimum
        final maxHeight = screenHeight * 0.7; // 70% maximum

        final adjustedHeight = keyboardHeight > 0
            ? (baseHeight + keyboardHeight * 0.8).clamp(minHeight, maxHeight)
            : baseHeight.clamp(minHeight, maxHeight);

        return SizedBox(
          height: adjustedHeight,
          child: ShoppingPantryBottomSheetContent(
            title: 'Create Shopping List',
            hintText: 'Enter shopping list name',
            successText: 'Shopping list created successfully',
            buttonText: 'Create',
            successButtonText: 'Done',
            scrollController: ScrollController(),
            isShoppingList: true,
            onCreate: (name, context, ref) {
              // Handle creation logic here
            },
          ),
        );
      },
    );
  }

  void reset() {
    state = const AppState<ShoppingResponseData>();
  }

  void resetToIdle() {
    state = state.copyWith(status: AppStatus.idle, errorMessage: null);
  }
}
