import 'package:mastercookai/core/helpers/app_constant.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';

import '../../../app/imports/packages_imports.dart';
import '../../data/models/plans_response.dart';
import '../../data/request_query/plan_request.dart';
import '../../network/app_status.dart';
import '../../network/base_notifier.dart';
import '../../network/network_utils.dart';

/// Provider for managing plans operations
final plansNotifierProvider =
    StateNotifierProvider<PlansNotifier, AppState<PlansData?>>(
  (ref) => PlansNotifier(ref),
);

class PlansNotifier extends BaseNotifier<PlansData?> {
  PlansNotifier(Ref ref) : super(ref, const AppState<PlansData?>());

  /// Fetch plans data
  Future<void> fetchPlans() async {
    callDataService(
      ref: ref,
      repo.plans(),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (PlansResponse response) {
        if (response.success == true && response.data != null) {
          state = state.copyWith(
            status: AppStatus.success,
            data: response.data!,
            errorMessage: null,
          );
        } else {
          // Handle case where API returns success but no plans data
          state = state.copyWith(
            status: AppStatus.empty,
            data: null,
            errorMessage: response.message?.general?.isNotEmpty == true
                ? response.message!.general!.first
                : 'No plans data found',
          );
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
          data: null,
        );
      },
      onComplete: () {}, // Optional completion callback
    );
  }

  var isPlanPurchased = true;

  Future<void> purchasePlan(PlanRequest queryParam, BuildContext context) async {
    callDataService(
      ref: ref,
      repo.purchasePlan(queryParam),
      onStart: () {
        state = state.copyWith(status: AppStatus.creating);
        isPlanPurchased = true; // reset at start
      },
      onSuccess: (res) {
        if (res.status == 200 &&
            state.status != AppStatus.createSuccess &&
            isPlanPurchased) {
          if (mounted) {
            isPlanPurchased = false; // block duplicate within this call
            Utils().showFlushbar(
              context,
              message: res.message?.general?.first ??
                  'Plan purchased successfully',
              isError: false,
            );

            ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
          }
          state = state.copyWith(status: AppStatus.createSuccess);
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.createError,
          errorMessage: e.message,
          data: null,
        );
      },
      onComplete: () {
        hideLoader();
      },
    );
  }


  /// Get loading status
  bool get isLoading => state.status == AppStatus.loading;

  /// Get success status
  bool get isSuccess => state.status == AppStatus.success;

  /// Get error status
  bool get hasError => state.status == AppStatus.error;

  /// Get empty status
  bool get isEmpty => state.status == AppStatus.empty;

  /// Get error message
  String? get errorMessage => state.errorMessage;

  /// Get plans list
  List<Plan> get plans => state.data?.plans ?? [];

  /// Check if plans are available
  bool get hasPlans => plans.isNotEmpty;
  bool isFieldsPopulated = false;
}
