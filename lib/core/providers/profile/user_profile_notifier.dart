import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/core/data/models/user_profile_response.dart';
import 'package:mastercookai/core/data/models/base_response.dart';
import 'package:mastercookai/core/data/models/plans_response.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/data/request_query/update_profile_request.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/network/base_notifier.dart';
import 'package:mastercookai/core/providers/profile/user_types_notifier.dart';
import 'package:mastercookai/core/services/file_picker_service.dart';
import 'package:mastercookai/core/utils/Utils.dart';

import '../../helpers/app_constant.dart';
import '../../helpers/local_storage_service.dart';
import '../../network/network_utils.dart';

/// Provider for managing user profile operations
final userProfileNotifierProvider =
    StateNotifierProvider<UserProfileNotifier, AppState<UserProfileData?>>(
  (ref) => UserProfileNotifier(ref),
);

class UserProfileNotifier extends BaseNotifier<UserProfileData?> {
  UserProfileNotifier(Ref ref) : super(ref, const AppState<UserProfileData?>());

  /// Fetch user profile data
  Future<void> fetchUserProfile() async {
    callDataService(
      ref: ref,
      repo.userProfile(),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (UserProfileResponse response) {
        if (response.success && response.data != null) {
          state = state.copyWith(
            status: AppStatus.success,
            data: response.data!,
            errorMessage: null,
          );
        } else {
          state = state.copyWith(
            status: AppStatus.empty,
            data: null,
            errorMessage: response.message.general.isNotEmpty
                ? response.message.general.first
                : 'No user profile data found',
          );
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
          data: null,
        );
      },
      onComplete: () {},
    );
  }

  /// Reset Password
  Future<void> resetPassword(
    BuildContext context,
    String currentPassword,
    String newPassword,
    String newConfirmPassword, {
    VoidCallback? onSuccess,
  }) async {
    callDataService(
      ref: ref,
      repo.resetPassword(currentPassword, newPassword, newConfirmPassword),
      onStart: () {
        state = state.copyWith(status: AppStatus.updating);
      },
      onSuccess: (BaseResponse response) async {
        if (response.success == true) {
          state = state.copyWith(status: AppStatus.success);
          if (context.mounted) {
            String successMessage = 'Password reset successfully';
            if (response.message?.general != null &&
                response.message!.general!.isNotEmpty) {
              successMessage = response.message!.general!.first;
            }
            Utils()
                .showFlushbar(context, message: successMessage, isError: false);
            if (onSuccess != null) {
              onSuccess();
            }
          }
        } else {
          String errorMessage = 'Failed to reset password';
          if (response.message?.error != null &&
              response.message!.error!.isNotEmpty) {
            errorMessage = response.message!.error!.first;
          }
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage: errorMessage,
          );
          if (context.mounted) {
            Utils().showFlushbar(context, message: errorMessage, isError: true);
          }
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showFlushbar(context,
              message: state.errorMessage!, isError: true);
        }
      },
      onComplete: () => debugPrint('Password reset completed'),
    );
  }

  /// Delete Account
  Future<void> deleteAccount(BuildContext context,
      {VoidCallback? onSuccess}) async {
    callDataService(
      ref: ref,
      repo.deleteAccount(),
      onStart: () {
        state = state.copyWith(status: AppStatus.deleting);
      },
      onSuccess: (BaseResponse response) async {
        if (response.success == true) {
          state = state.copyWith(status: AppStatus.success);
          if (context.mounted) {
            String successMessage = 'Account deleted successfully';
            if (response.message?.general != null &&
                response.message!.general!.isNotEmpty) {
              successMessage = response.message!.general!.first;
            }
            Utils()
                .showFlushbar(context, message: successMessage, isError: false);
            // Clear session data
            final localStorage = ref.read(localStorageProvider);
            await localStorage.clearLoginData();
            // Navigate to login view
            if (context.mounted) {
              context.go('/splash');
            }
            if (onSuccess != null) {
              onSuccess();
            }
          }
        } else {
          String errorMessage = 'Failed to delete account';
          if (response.message?.error != null &&
              response.message!.error!.isNotEmpty) {
            errorMessage = response.message!.error!.first;
          }
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage: errorMessage,
          );
          if (context.mounted) {
            Utils().showFlushbar(context, message: errorMessage, isError: true);
          }
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showFlushbar(context,
              message: state.errorMessage!, isError: true);
        }
      },
      onComplete: () => debugPrint('Account deletion completed'),
    );
  }

  /// Update profile
  Future<bool> updateProfile({
    required BuildContext context,
    required UpdateProfileRequest request,
  }) async {
    showLoader();
    final completer = Completer<bool>();
    callDataService(
      ref: ref,
      repo.updateUserProfile(request),
      onStart: () => state = state.copyWith(
        status: AppStatus.loading,
      ),
      onSuccess: (BaseResponse response) {
        hideLoader();
        if (response.success == true) {
          state = state.copyWith(
            status: AppStatus.success,
            errorMessage: null,
          );
          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: response.message!.general!.first,
              isError: false,
              onDismissed: () {},
            );
          }
          completer.complete(true);
        } else {
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage: response.message!.error?.join(', ') ??
                'Failed to update profile',
          );
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: response.message!.error!.first, isError: true);
          }
          debugPrint('profile update failed: ${response.message!.error}');
          completer.complete(false);
        }
      },
      onError: (e) {
        hideLoader();
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: e.message, isError: true);
        }
        debugPrint('profile updation error: $e');
        completer.complete(false);
      },
      onComplete: () => debugPrint('profile updation completed'),
    );
    return completer.future;
  }

  /// Pick image for Pantry list cover
  Future<void> pickImage(
      BuildContext context, WidgetRef ref, String id, String name) async {
    final pickFile = ref.read(filePickerServiceProvider.notifier);
    final file = await pickFile.pickFile(
      allowedExtensions: ['jpg', 'jpeg', 'png'],
    );
    // Note: pickImage seems incomplete; consider integrating with updateProfile
  }

  Future<void> pickProfileImage(BuildContext context) async {
    final filePicker = ref.read(filePickerServiceProvider.notifier);
    final file = await filePicker.pickFile(
      allowedExtensions: ['jpg', 'jpeg', 'png'],
    );

    if (file != null) {
      // Get the current user profile
      final currentUserProfile = state.data?.userProfile;

      if (currentUserProfile != null) {
        final request = UpdateProfileRequest(
          firstName: currentUserProfile.userName,
          gender: currentUserProfile.gender,
          dob: currentUserProfile.dob,
          countryCode: currentUserProfile.countryCode,
          contact: currentUserProfile.phone,
          userTypeId: currentUserProfile.userTypeId,
          profilePic: file,
        );
        await updateProfile(context: context, request: request);
        await fetchUserProfile();
      } else {
        if (context.mounted) {
          Utils().showFlushbar(context,
              message: 'User profile not loaded.', isError: true);
        }
      }
    }
  }

  /// Reset the notifier state to initial state
  void reset() {
    state = const AppState<UserProfileData?>();
  }

  /// Get current user profile data
  UserProfileData? get userProfileData => state.data;

  /// Get current user profile
  UserProfile? get userProfile => state.data?.userProfile;

  /// Get instruction words for units
  List<String> get instructionWords => state.data?.instructionWords ?? [];

  /// Check if currently loading
  bool get isLoading => state.status == AppStatus.loading;

  /// Check if user profile is loaded
  bool get hasProfile =>
      state.data?.userProfile != null && state.status == AppStatus.success;

  /// Check if there's an error
  bool get hasError => state.status == AppStatus.error;

  /// Check if profile is empty
  bool get isEmpty => state.status == AppStatus.empty || state.data == null;

  /// Get error message
  String? get errorMessage => state.errorMessage;

  /// Get user name
  String get userName => state.data?.userProfile?.userName ?? '';

  /// Get user email
  String get userEmail => state.data?.userProfile?.email ?? '';

  /// Get user role
  String get userRole => state.data?.userProfile?.userType ?? '';

  /// Get storage quota in MB
  double get storageQuotaInMB =>
      state.data?.userProfile?.storageQuotaInMB ?? 0.0;

  /// Get storage used in MB
  double get storageUsedInMB => state.data?.userProfile?.storageUsedInMB ?? 0.0;

  /// Get storage usage percentage
  double get storageUsagePercentage {
    if (storageQuotaInMB <= 0) return 0.0;
    return (storageUsedInMB / storageQuotaInMB) * 100;
  }

  /// Get profile picture URL
  String? get profilePicUrl => state.data?.userProfile?.profilePic;

  /// Check if user has profile picture
  bool get hasProfilePic =>
      state.data?.userProfile?.profilePic?.isNotEmpty ?? false;

  /// Get remaining recipe creation GPT quota
  int get recipeCreateGPTQuota =>
      state.data?.userProfile?.remainingQuota?.recipeCreateGPT ?? 0;

  /// Check if user has recipe creation GPT quota available
  bool get hasRecipeCreateGPTQuota => recipeCreateGPTQuota > 0;

  /// Get remaining recipe image GPT quota
  int get recipeImageGPTQuota =>
      state.data?.userProfile?.remainingQuota?.recipeImageGPT ?? 0;

  /// Get remaining recipe quota
  int get recipeQuota => state.data?.userProfile?.remainingQuota?.recipe ?? 0;

  /// Get remaining recipe storage quota
  int get recipeStorageInMB =>
      state.data?.userProfile?.remainingQuota?.recipeStorageInMB ?? 0;
}
