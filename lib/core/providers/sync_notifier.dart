import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/helpers/app_constant.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/data/request_query/fetch_cookbook_request.dart';
import 'package:mastercookai/core/network/network_utils.dart';
import 'package:mastercookai/core/widgets/image_cropper/image_cropper.dart';
import '../../app/imports/packages_imports.dart';
import '../data/models/recipe_detail_response.dart';
import '../data/models/sync_list_response.dart';
import '../network/app_status.dart';
import '../network/base_notifier.dart';
import '../services/file_picker_service.dart' show filePickerServiceProvider;
import 'package:path/path.dart' as path;

final syncNotifierProvider =
    StateNotifierProvider<SyncNotifier, AppState<SyncListResponseData>>(
  (ref) => SyncNotifier(ref),
);

class SyncNotifier extends BaseNotifier<SyncListResponseData> {
  SyncNotifier(Ref ref) : super(ref, const AppState<SyncListResponseData>()) {
    initialize();
  }

  // Internal state for categories, progress, and items
  Map<String, bool> _categories = {
    'Cookbooks': true,
    'Shopping': true,
  };
  double _progress = 0.0;
  List<Map<String, dynamic>> _items = [];

  // Getters for external access
  Map<String, bool> get categories => _categories;
  double get progress => _progress;
  List<Map<String, dynamic>> get items => _items;

  // Get selected item IDs for API, separated by category
  Map<String, List<int>> getSelectedItems() {
    return {
      'cookbookIds': _items
          .where((item) =>
              item['isSelected'] == true && item['category'] == 'Cookbooks')
          .map((item) => item['id'] as int)
          .toList(),
      'shoppingListIds': _items
          .where((item) =>
              item['isSelected'] == true && item['category'] == 'Shopping')
          .map((item) => item['id'] as int)
          .toList(),
    };
  }

  // Method to sync selected item IDs
  Future<void> syncSelectedItems(
      BuildContext context, Map<String, List<int>> selectedItems) async {
    try {
      showLoader();
      await callDataService(
        ref: ref,
        repo.syncData(selectedItems),
        onStart: () {
          state = state.copyWith(status: AppStatus.creating);
          _progress = 0.0; // Reset progress
        },
        onSuccess: (response) {
          state = state.copyWith(status: AppStatus.createSuccess);
          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: response.message?.general?.first ??
                  'Synchronization completed successfully',
              isError: false,
            );
            Future.delayed(Duration(seconds: 3), () {
              Navigator.pop(context);
            });
          }
        },
        onError: (e) {
          state = state.copyWith(
            status: AppStatus.createError,
            errorMessage: e.message ?? 'Failed to synchronize items',
          );
          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: state.errorMessage!,
              isError: true,
            );
          }
        },
        onComplete: () {
          debugPrint('Synchronization complete');
        },
      );
    } catch (e) {
      state = state.copyWith(
        status: AppStatus.createError,
        errorMessage: 'Failed to sync items: $e',
      );
      if (context.mounted) {
        Utils().showFlushbar(
          context,
          message: state.errorMessage!,
          isError: true,
        );
      }
    } finally {
      hideLoader();
    }
  }

  void initialize() {
    // Initial fetch of sync list
    callDataService(

      ref: ref,
      repo.fetchSyncList(),
      onStart: () {
        state = state.copyWith(status: AppStatus.loading);
        _progress = 0.0; // Reset progress
        _items = []; // Clear items only on initial fetch
      },
      onSuccess: (response) => _handleSuccessResponse(response, false),
      onError: (e) => state = state.copyWith(
        status: AppStatus.error,
        errorMessage: e.message ?? 'Failed to fetch sync list',
      ),
      onComplete: () => debugPrint('Sync list fetch complete'),
    );
  }

  void _handleSuccessResponse(SyncListResponse response, bool loadMore) {
    final result = response.data;

    // Combine cookbooks and shopping lists into _items
    _items = [
      ...(result?.cookbooks?.map((cookbook) => {
                'id': cookbook.id,
                'name': cookbook.name ?? 'Untitled Cookbook',
                'count': cookbook.recipeCount ?? 0,
                'category': 'Cookbooks',
                'isSelected': _categories['Cookbooks'] ?? true,
              }) ??
          []),
      ...(result?.shoppingLists?.map((shoppingList) => {
                'id': shoppingList.id,
                'name': shoppingList.name ?? 'Untitled Shopping List',
                'count': shoppingList.shoppingItemCount ?? 0,
                'category': 'Shopping',
                'isSelected': _categories['Shopping'] ?? true,
              }) ??
          []),
    ];

    // Update sync progress (average progress of selected items)
    final selectedItems =
        _items.where((item) => item['isSelected'] == true).toList();
    _progress = selectedItems.isNotEmpty
        ? selectedItems
                .map((item) => item['progress'] as double? ?? 0.0)
                .reduce((a, b) => a + b) /
            selectedItems.length
        : 0.0;

    // Update categories based on response data
    _categories = {
      'Cookbooks': result?.cookbooks?.isNotEmpty ?? _categories['Cookbooks']!,
      'Shopping': result?.shoppingLists?.isNotEmpty ?? _categories['Shopping']!,
    };

    state = state.copyWith(
      status: AppStatus.success,
      data: result?.copyWith(),
      errorMessage: null,
    );
  }

  Future<void> importCookBook(
      BuildContext context, File file, int cookBookId) async {
    try {
      showLoader();
      final fileName = path.basenameWithoutExtension(file.path);

      await callDataService(
        ref: ref,
        repo.importCookBook(file, cookBookId, fileName),
        onStart: () {
          state = state.copyWith(status: AppStatus.creating);
          _progress = 0.0; // Reset progress
          // Add a temporary sync item
          final newItem = {
            'id': cookBookId,
            'name': 'Importing Cookbook',
            'count': 0,
            'status': 'Syncing',
            'progress': 0.0,
            'category': 'Cookbooks',
            'isSelected': true,
          };
          _items = [..._items, newItem];
          state = state.copyWith(
            data: state.data?.copyWith(),
          );
        },
        onSuccess: (response) async {
          if (response.success == true) {
            state = state.copyWith(status: AppStatus.createSuccess);
            // Update sync item to "Done"
            _items = _items.map((item) {
              if (item['name'] == 'Importing Cookbook') {
                return {
                  ...item,
                  'status': 'Done',
                  'progress': 1.0,
                };
              }
              return item;
            }).toList();
            // Update progress
            final selectedItems =
                _items.where((item) => item['isSelected'] == true).toList();
            _progress = selectedItems.isNotEmpty
                ? selectedItems
                        .map((item) => item['progress'] as double? ?? 0.0)
                        .reduce((a, b) => a + b) /
                    selectedItems.length
                : 0.0;
            state = state.copyWith(
              data: state.data?.copyWith(),
            );

            if (context.mounted) {
              Utils().showFlushbar(
                context,
                message:
                    response.message?.general?.first ?? 'Imported successfully',
                isError: false,
              );
            }
          } else {
            debugPrint('API returned success: false');
            state = state.copyWith(
              status: AppStatus.createError,
              errorMessage: 'Failed to import cookbook: Invalid response',
            );
            if (context.mounted) {
              Utils().showFlushbar(
                context,
                message: state.errorMessage!,
                isError: true,
              );
            }
          }
        },
        onError: (e) {
          debugPrint('Error during import: ${e.message}');
          state = state.copyWith(
            status: AppStatus.createError,
            errorMessage: e.message ?? 'Failed to import cookbook',
          );
          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: state.errorMessage!,
              isError: true,
            );
          }
        },
        onComplete: () {
          hideLoader();
        },
      );
    } catch (e) {
      state = state.copyWith(
        status: AppStatus.createError,
        errorMessage: 'Unexpected error: $e',
      );
      if (context.mounted) {
        Utils().showFlushbar(
          context,
          message: state.errorMessage!,
          isError: true,
        );
      }
    } finally {
      hideLoader();
    }
  }

  // Method to toggle category sync state
  void toggleCategory(String category, bool value) {
    _categories = {..._categories, category: value};
    _items = _items.map((item) {
      if (item['category'] == category) {
        return {...item, 'isSelected': value};
      }
      return item;
    }).toList();
    // Update progress
    final selectedItems =
        _items.where((item) => item['isSelected'] == true).toList();
    _progress = selectedItems.isNotEmpty
        ? selectedItems
                .map((item) => item['progress'] as double? ?? 0.0)
                .reduce((a, b) => a + b) /
            selectedItems.length
        : 0.0;
    state = state.copyWith(data: state.data?.copyWith());
  }

  // Method to toggle individual item sync state
  void toggleItem(String name, bool value) {
    _items = _items.map((item) {
      if (item['name'] == name) {
        return {...item, 'isSelected': value};
      }
      return item;
    }).toList();
    // Update category state based on items
    _categories = {
      'Cookbooks': _items.any((item) =>
          item['category'] == 'Cookbooks' && item['isSelected'] == true),
      'Shopping': _items.any((item) =>
          item['category'] == 'Shopping' && item['isSelected'] == true),
    };
    // Update progress
    final selectedItems =
        _items.where((item) => item['isSelected'] == true).toList();
    _progress = selectedItems.isNotEmpty
        ? selectedItems
                .map((item) => item['progress'] as double? ?? 0.0)
                .reduce((a, b) => a + b) /
            selectedItems.length
        : 0.0;
    state = state.copyWith(data: state.data?.copyWith());
  }

  @override
  void reset() {
    state = const AppState<SyncListResponseData>();
    _categories = {
      'Cookbooks': true,
      'Shopping': true,
    };
    _progress = 0.0;
    _items = [];
  }

  void resetToIdle() {
    state = state.copyWith(status: AppStatus.idle, errorMessage: null);
    _progress = 0.0;
  }
}
