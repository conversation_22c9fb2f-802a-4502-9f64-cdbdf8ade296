import 'dart:convert';
import '../helpers/access_key_helper.dart';
import '../helpers/device_info.dart';


class HeaderBuilder {
  static Future<HeaderBuilder> initialize() async {
    final deviceInfo = await DeviceInfoService.getDeviceInfo();
    final ip = await DeviceInfoService.getIpAddress();

    return HeaderBuilder._(

      deviceInfo: deviceInfo,
      ip: ip,
    );
  }


  final Map<String, String> deviceInfo;
  final String ip;

  HeaderBuilder._({
    required this.deviceInfo,
    required this.ip,
  });

  Map<String, dynamic> build({Map<String, dynamic>? extra}) {
    final deviceName = base64Encode(utf8.encode(deviceInfo['device_name']!));
    final deviceId = base64Encode(utf8.encode(deviceInfo['device_id']!));
    final ipAddress = base64Encode(utf8.encode(ip));
    final latitude = base64Encode(utf8.encode("0"));
    final longitude = base64Encode(utf8.encode("0"));

    return {
      'device-name': deviceName,
      'device-id': deviceId,
      'latitude': latitude,
      'longitude': longitude,
      'ip': ipAddress,
      'primary-id' : "",
      'Content-Type': 'application/json',
      ...?extra
    };
  }
}


