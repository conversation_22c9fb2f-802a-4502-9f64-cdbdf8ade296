import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

class ImageCompressionUtils {
  /// Compresses and crops an image to the target file size in KB (≤ 250KB)
  /// Returns a File with the compressed and cropped image
  static Future<File> compressImageToSize(
      ui.Image image, {
        required int maxSizeKB, // Must be 250 for this use case
        String? customFileName,
        Rect? cropRect, // Optional crop region
        Function(String)? onProgress,
      }) async {
    assert(maxSizeKB == 250, 'This implementation is tailored for 250KB target size');
    final tempDir = await getTemporaryDirectory();
    final fileName = customFileName ??
        'compressed_image_${DateTime.now().millisecondsSinceEpoch}.png';
    final tempFile = File('${tempDir.path}/$fileName');

    onProgress?.call('Validating crop region...');

    // Validate cropRect
    Rect validCropRect = cropRect ?? Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    validCropRect = Rect.fromLTWH(
      validCropRect.left.clamp(0, image.width.toDouble()),
      validCropRect.top.clamp(0, image.height.toDouble()),
      validCropRect.width.clamp(0, image.width.toDouble() - validCropRect.left),
      validCropRect.height.clamp(0, image.height.toDouble() - validCropRect.top),
    );

    // Crop image if cropRect is provided and valid
    ui.Image croppedImage = image;
    if (cropRect != null && (validCropRect.width < image.width || validCropRect.height < image.height)) {
      onProgress?.call('Cropping image...');
      croppedImage = await _cropImage(image, validCropRect);
    }

    // Initial target dimensions for 250KB
    int targetWidth = croppedImage.width;
    int targetHeight = croppedImage.height;
    double scaleFactor = 1.0;
    const maxDimension = 500; // Base dimension for 250KB PNG

    onProgress?.call('Calculating optimal size...');

    // Scale down if dimensions are too large
    if (croppedImage.width > maxDimension || croppedImage.height > maxDimension) {
      scaleFactor = maxDimension / (croppedImage.width > croppedImage.height ? croppedImage.width : croppedImage.height);
      targetWidth = (croppedImage.width * scaleFactor).round();
      targetHeight = (croppedImage.height * scaleFactor).round();
    }

    // Ensure minimum size
    targetWidth = targetWidth.clamp(200, croppedImage.width);
    targetHeight = targetHeight.clamp(200, croppedImage.height);

    debugPrint('Processing from ${croppedImage.width}x${croppedImage.height} to ${targetWidth}x$targetHeight');
    onProgress?.call('Resizing image...');

    // Resize image
    ui.Image resizedImage = await _resizeImage(croppedImage, targetWidth, targetHeight);

    // Convert to bytes and check size
    onProgress?.call('Compressing image...');
    Uint8List compressedBytes = await _imageToBytes(resizedImage);
    double finalSizeKB = compressedBytes.length / 1024;

    // Iteratively reduce dimensions if size exceeds 250KB
    int iteration = 0;
    const maxIterations = 3;
    while (finalSizeKB > maxSizeKB && iteration < maxIterations) {
      onProgress?.call('Size $finalSizeKB KB exceeds 250KB, reducing dimensions...');
      scaleFactor *= 0.8; // Reduce by 20% each iteration
      targetWidth = (croppedImage.width * scaleFactor).round().clamp(100, croppedImage.width);
      targetHeight = (croppedImage.height * scaleFactor).round().clamp(100, croppedImage.height);

      debugPrint('Retry $iteration: Resizing to ${targetWidth}x$targetHeight');
      resizedImage = await _resizeImage(croppedImage, targetWidth, targetHeight);
      compressedBytes = await _imageToBytes(resizedImage);
      finalSizeKB = compressedBytes.length / 1024;
      iteration++;
    }

    debugPrint('Final compressed size: ${finalSizeKB.toStringAsFixed(1)} KB');

    // Save to file
    onProgress?.call('Saving image...');
    await tempFile.writeAsBytes(compressedBytes);

    // Verify final size
    if (finalSizeKB > maxSizeKB) {
      debugPrint('Warning: Final size ${finalSizeKB.toStringAsFixed(1)} KB exceeds target $maxSizeKB KB');
    }

    return tempFile;
  }

  /// Crops an image to the specified Rect
  static Future<ui.Image> _cropImage(ui.Image image, Rect cropRect) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint()..filterQuality = FilterQuality.medium;

    canvas.drawImageRect(
      image,
      cropRect,
      Rect.fromLTWH(0, 0, cropRect.width, cropRect.height),
      paint,
    );

    final picture = recorder.endRecording();
    return await picture.toImage(cropRect.width.round(), cropRect.height.round());
  }

  /// Converts ui.Image to bytes in PNG format
  static Future<Uint8List> _imageToBytes(ui.Image image) async {
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    if (byteData == null) {
      throw Exception('Failed to convert image to bytes');
    }
    return byteData.buffer.asUint8List();
  }

  /// Resizes an image to the specified dimensions
  static Future<ui.Image> _resizeImage(ui.Image originalImage, int width, int height) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint()..filterQuality = FilterQuality.medium;

    canvas.drawImageRect(
      originalImage,
      Rect.fromLTWH(0, 0, originalImage.width.toDouble(), originalImage.height.toDouble()),
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      paint,
    );

    final picture = recorder.endRecording();
    return await picture.toImage(width, height);
  }

  /// Validates if a file size is within the acceptable range
  static Future<bool> isFileSizeAcceptable(File file, int maxSizeKB) async {
    final sizeBytes = await file.length();
    final sizeKB = sizeBytes / 1024;
    return sizeKB <= maxSizeKB;
  }

  /// Gets the file size in KB
  static Future<double> getFileSizeKB(File file) async {
    final sizeBytes = await file.length();
    return sizeBytes / 1024;
  }

  /// Checks if an image file is too large for processing
  static Future<bool> isImageTooLargeForProcessing(File file, {int maxSizeMB = 10}) async {
    final sizeBytes = await file.length();
    final sizeMB = sizeBytes / (1024 * 1024);
    return sizeMB > maxSizeMB;
  }
}