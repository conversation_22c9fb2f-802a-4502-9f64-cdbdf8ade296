// ... (your existing imports and class definition)

// Add the import for permission_handler
import 'dart:io';

import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../app/imports/packages_imports.dart';
import '../../app/theme/colors.dart';
import '../../presentation/profile/mobileui/edit_profile_mobile.dart';
import '../widgets/custom_text.dart';
import 'Utils.dart';
import 'device_utils.dart';

class _MyAccountMobileSettingsState extends ConsumerState<EditProfileMobile> {
  // ... (your existing code)

  // This is the new function that will handle permission logic
  Future<File?> _handleImageSource(ImageSource source) async {
    // Determine which permission to request based on the source
    PermissionStatus status;
    if (source == ImageSource.camera) {
      status = await Permission.camera.request();
    } else {
      // For gallery, check for both new and old storage permissions
      status = await Permission.photos.request();
      if (status.isDenied) {
        status = await Permission.storage.request();
      }
    }

    if (status.isGranted) {
      // Permission granted, now pick the image
      return await _pickImageFromSource(source);
    } else if (status.isPermanentlyDenied) {
      // User has permanently denied permission, show a dialog to guide them to settings
      if (mounted) {
        Utils().showFlushbar(
          context,
          message:
          'Permission was permanently denied. Please enable it from app settings.',
          isError: true,
        );
        // You can also show a more formal dialog to guide the user
        await openAppSettings();
      }
      return null;
    } else {
      // Permission was denied, show a message and do not proceed
      if (mounted) {
        Utils().showFlushbar(
          context,
          message: 'Permission denied. Cannot select image.',
          isError: true,
        );
      }
      return null;
    }
  }

  // Your original _pickProfileImage function, now updated to use the new handler
  void _pickProfileImage() async {
    final source = await _showImageSourceDialog();
    if (source == null) return;

    // Use the new handler function
    final file = await _handleImageSource(source);
    if (file != null && mounted) {
      // ... (your existing code for ImageCropper)
    }
  }
  Future<ImageSource?> _showImageSourceDialog() async {
    return showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title:
        CustomText(
          text:  'Select Image From',
          color: AppColors.blackTextColor,
          size: DeviceUtils().isTabletOrIpad(context) ? 24 : 24,
          weight: FontWeight.w700,
          align:TextAlign.center ,
        ),

        actions: [
          Column(
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context, ImageSource.camera),
                child: Row(
                  children: [
                    const Icon(Icons.camera_alt,color: Colors.red,size: 24,),
                    const SizedBox(width: 8),
                    CustomText(
                      text:  'Camera',
                      color: AppColors.blackTextColor,
                      size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
                      weight: FontWeight.w500,
                      align:TextAlign.center ,
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, ImageSource.gallery),
                child: Row(
                  children: [
                    const Icon(Icons.photo_library,color: Colors.red,size: 24),
                    const SizedBox(width: 8),
                    CustomText(
                      text:  'Gallery',
                      color: AppColors.blackTextColor,
                      size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
                      weight: FontWeight.w500,
                      align:TextAlign.center ,
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, null),
                child:
                CustomText(
                  text:  'Cancel',
                  color: AppColors.primaryColor,
                  size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
                  weight: FontWeight.w600,
                  align:TextAlign.end ,
                ),
              ),
            ],


          ),

        ],
      ),
    );
  }

  Future<File?> _pickImageFromSource(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final XFile? xfile = await picker.pickImage(
        source: source,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (xfile == null) return null;
      return File(xfile.path);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    throw UnimplementedError();
  }

// ... (your other existing functions like _showImageSourceDialog and _pickImageFromSource)
}