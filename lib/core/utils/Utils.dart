import 'dart:io';

import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/screen_sizer.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import '../../app/imports/packages_imports.dart';
import '../widgets/common_confirm_dialog.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:device_info_plus/device_info_plus.dart';

import '../widgets/uploading_dialog.dart';
import 'device_utils.dart';

class Utils {
  Future<void> launchURL({required String url}) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  void showToast({
    required String message,
    Toast toastLength = Toast.LENGTH_SHORT,
    ToastGravity gravity = ToastGravity.CENTER,
    int timeInSecForIosWeb = 1,
    Color backgroundColor = Colors.red,
    Color textColor = Colors.white,
    double fontSize = 16.0,
  }) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: toastLength,
      gravity: gravity,
      timeInSecForIosWeb: timeInSecForIosWeb,
      backgroundColor: backgroundColor,
      textColor: textColor,
      fontSize: fontSize,
    );
  }

  showSnackBar(BuildContext context, String? message) {
    // Try to use the context first
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message ?? 'Unknown error',
          style: context.theme.textTheme.labelMedium!.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w400,
          ),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        behavior: SnackBarBehavior.fixed, // Changed from floating to fixed
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void showFlushbar(
    BuildContext context, {
    required String message,
    required bool isError,
    VoidCallback? onDismissed,
  }) {
    (Platform.isAndroid || Platform.isIOS)
        ? Fluttertoast.showToast(
            msg: message,
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            timeInSecForIosWeb: 1,
            backgroundColor: isError ? Colors.red : Colors.green,
            textColor: Colors.white,
            fontSize: 14.0)
        : Flushbar(
            messageText: Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            margin: EdgeInsets.only(
              top: 40.h,
              right: 8.w,
              left: MediaQuery.of(context).size.width - 300 - 8,
            ),
            borderRadius: BorderRadius.circular(8),
            duration: const Duration(seconds: 3),
            backgroundColor: isError ? Colors.red : Colors.green,
            flushbarPosition: FlushbarPosition.TOP,
            flushbarStyle: FlushbarStyle.FLOATING,
            forwardAnimationCurve: Curves.easeOut,
            boxShadows: const [
              BoxShadow(
                color: Colors.black26,
                offset: Offset(0, 2),
                blurRadius: 6,
              ),
            ],
            // maxWidth: 500.w,
            padding: EdgeInsets.all(16.w),
          ).show(context).then((_) {
            if (onDismissed != null) onDismissed();
          });
  }

  Future<bool?> showCommonConfirmDialog(
      {required BuildContext context,
      required String title,
      required String subtitle,
      required String confirmText,
      required String cancelText}) {
    return showDialog<bool>(
      context: context,
      builder: (context) {
        return CommonConfirmDialog(
          title: title,
          subtitle: subtitle,
          confirmText: confirmText,
          cancelText: cancelText,
        );
      },
    );
  }

  String capitalizeFirstLetter(String input) {
    if (input.isEmpty) return input;
    return input[0].toUpperCase() + input.substring(1);
  }

  void hideKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  Future<String?> getVideoThumbnailUrl(String videoUrl) async {
    try {
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: videoUrl,
        // Supports both local file paths and network URLs
        thumbnailPath: (await getTemporaryDirectory()).path,
        imageFormat: ImageFormat.JPEG,
        // Use JPEG for compatibility
        maxHeight: 120,
        // Match the height of your CommonImage widget
        quality: 75, // Adjust quality (0-100) for performance vs. clarity
      );
      return thumbnailPath;
    } catch (e) {
      debugPrint("Error generating thumbnail for $videoUrl: $e");
      return null;
    }
  }

  bool isVideo(String? url) {
    if (url == null) return false;
    final videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm'];
    return videoExtensions.any((ext) => url.toLowerCase().endsWith(ext));
  }

  // Helper method to format date from API response
  String formatDate(String dateString) {
    try {
      final DateTime date = DateTime.parse(dateString);
      return DateFormat('MM/dd/yyyy').format(date);
    } catch (e) {
      return dateString; // Return original string if parsing fails
    }
  }

  // Helper method to format date for display in form
  String formatDateForDisplay(String dateString) {
    try {
      final DateTime date = DateTime.parse(dateString);
      return DateFormat('dd MMM yyyy').format(date);
    } catch (e) {
      return dateString; // Return original string if parsing fails
    }
  }

  // void showTopRightDialog(BuildContext context) {
  //   final overlay = Overlay.of(context);
  //   OverlayEntry? overlayEntry;
  //
  //   overlayEntry = OverlayEntry(
  //     builder: (_) => Positioned(
  //       top: 40,
  //       right: 20,
  //       child: Material(
  //         color: Colors.transparent,
  //         child: UploadingDialog(
  //           title: "Cookbook list is uploading...",
  //           subtitle: "Fruits CookBook",
  //           progress: 0.54,
  //         ),
  //       ),
  //     ),
  //   );
  //
  //   overlay.insert(overlayEntry);
  // }

  // Method to calculate the height of a grid item
  double calculateGridItemHeight(
      BoxConstraints constraints, BuildContext context, int crossAxisCount) {
    // Get the available width from constraints
    final availableWidth = constraints.maxWidth;

    // Calculate padding and spacing
    final horizontalPadding = DeviceUtils().isTabletOrIpad(context) ? 8 : 16;
    final crossAxisSpacing = DeviceUtils().isTabletOrIpad(context) ? 8 : 12;

    // Calculate effective width for grid items
    final totalSpacing = crossAxisSpacing * (crossAxisCount - 1);
    final totalPadding = horizontalPadding * 2;
    final itemWidth =
        (availableWidth - totalSpacing - totalPadding) / crossAxisCount;

    // Get the childAspectRatio
    final childAspectRatio = DeviceUtils().isTabletOrIpad(context)
        ? 0.9
        : ScreenSizer().calculateChildAspectRatio(
            MediaQuery.of(context).size.width,
            MediaQuery.of(context).size.height,
          );

    // Calculate item height using the aspect ratio (width / height)
    final itemHeight = itemWidth / childAspectRatio;

    return itemHeight;
  }

  // Map to convert month numbers to short month names
  static const Map<int, String> _monthNames = {
    1: 'Jan',
    2: 'Feb',
    3: 'Mar',
    4: 'Apr',
    5: 'May',
    6: 'Jun',
    7: 'Jul',
    8: 'Aug',
    9: 'Sep',
    10: 'Oct',
    11: 'Nov',
    12: 'Dec',
  };

  // Method to extract year, month (as short string), and day from a DOB string
  Map<String, dynamic>? extractDobComponents(String dob) {
    try {
      // Validate the DOB format (YYYY-MM-DD)
      final regex = RegExp(r'^\d{4}-\d{2}-\d{2}$');
      if (!regex.hasMatch(dob)) {
        debugPrint('Invalid DOB format: $dob');
        return null;
      }

      // Parse the date string
      final dateTime = DateTime.parse(dob);

      // Extract year, month, and day
      return {
        'year': dateTime.year,
        'month': _monthNames[dateTime.month] ?? 'Unknown',
        'day': dateTime.day,
      };
    } catch (e) {
      debugPrint('Error parsing DOB: $e');
      return null;
    }
  }

  // Convert user-friendly sort option to API-compatible sort value
  String convertSortToApi(String sortOption) {
    switch (sortOption) {
      case 'Name (A-Z)':
        return 'Name';
      case 'Name (Z-A)':
        return 'NameDescending';
      case 'Most Recent':
        return 'Newest';
      case 'Oldest First':
        return 'Oldest';
      default:
        return '';
    }
  }

  bool isValidAmount(double amount) {
    return amount >= 0.01 && amount <= 1000000;
  }

  String cleanHtmlText(String text) {
    if (text.isEmpty) return text;

    // Convert HTML to properly formatted plain text
    String cleaned = text
        .replaceAll(RegExp(r'<div[^>]*>'), '') // Remove opening div tags
        .replaceAll('</div>', '') // Remove closing div tags
        .replaceAll(RegExp(r'<strong[^>]*>'), '') // Remove opening strong tags
        .replaceAll('</strong>', '') // Remove closing strong tags
        .replaceAll(RegExp(r'<em[^>]*>'), '') // Remove opening em tags
        .replaceAll('</em>', '') // Remove closing em tags
        .replaceAll('<br>', '\n') // Convert br tags to newlines
        .replaceAll('<br/>', '\n') // Convert self-closing br tags to newlines
        .replaceAll('<br />', '\n') // Convert self-closing br tags with space to newlines
        .replaceAll(RegExp(r'<ul[^>]*>'), '\n') // Convert ul opening to newline
        .replaceAll('</ul>', '') // Remove ul closing
        .replaceAll(RegExp(r'<li[^>]*>'), '- ') // Convert li opening to bullet point
        .replaceAll('</li>', '\n') // Convert li closing to newline
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove any remaining HTML tags
        .trim(); // Remove leading/trailing whitespace

    // Clean up multiple consecutive newlines
    cleaned = cleaned.replaceAll(RegExp(r'\n\s*\n\s*\n+'), '\n\n');

    // Clean up spaces around newlines
    cleaned = cleaned.replaceAll(RegExp(r'\n\s+'), '\n');
    cleaned = cleaned.replaceAll(RegExp(r'\s+\n'), '\n');

    // Clean up multiple spaces
    cleaned = cleaned.replaceAll(RegExp(r' +'), ' ');

    return decodeHtmlEntities(cleaned);
  }

  String decodeHtmlEntities(String text) {
    return text
        .replaceAll('&', '&')
        .replaceAll('<', '<')
        .replaceAll('>', '>')
        .replaceAll('"', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&hellip;', '...')
        .replaceAll('&mdash;', '—')
        .replaceAll('&ndash;', '–')
        .replaceAll('&copy;', '©')
        .replaceAll('&reg;', '®')
        .replaceAll('&trade;', '™');
  }

  bool isNumeric(String value) {
    return num.tryParse(value.trim()) != null;
  }

  void hideKeyboardOnTap() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  /// Returns a greeting message based on the current time of day.
  String getGreetingMessage() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning!';
    } else if (hour < 17) {
      // Before 5 PM
      return 'Good Afternoon!';
    } else if (hour < 21) {
      // Before 9 PM
      return 'Good Evening!';
    } else {
      return 'Good Night!';
    }
  }



  String getPlatformName() {
    if (kIsWeb) {
      return 'Web';
    } else if (Platform.isAndroid) {
      return 'Android';
    } else if (Platform.isIOS) {
      return 'iOS';
    } else if (Platform.isMacOS) {
      return 'macOS';
    } else if (Platform.isWindows) {
      return 'Windows';
    } else if (Platform.isLinux) {
      return 'Linux';
    } else {
      return 'Unknown';
    }
  }

  bool isKeyboardOpen(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom > 0;
  }


  // Show confirmation dialog for Home tab
  Future<bool> showExitConfirmationDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: const CustomText(text: 'Confirm Exit',color: AppColors.primaryLightTextColor,weight: FontWeight.w500,),
        content: const CustomText(text:'Are you sure you want to exit?',color: AppColors.primaryLightTextColor),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(), // Cancel
            child: const CustomText(text:'No',color: AppColors.primaryLightTextColor,weight: FontWeight.w500,),
          ),
          TextButton(
            onPressed: () => SystemNavigator.pop(), // Confirm
            child: const CustomText(text:'Yes',color: AppColors.primaryLightTextColor,weight: FontWeight.w500,),
          ),
        ],
      ),
    ) ??
        false; // Return false if dialog is dismissed
  }

  static Future<File?> pickAndCropImage(BuildContext context) async {
    final pickedFile = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: pickedFile.path,
        // aspectRatioPresets: [
        //   CropAspectRatioPreset.square,
        //   CropAspectRatioPreset.ratio3x2,
        //   CropAspectRatioPreset.original,
        //   CropAspectRatioPreset.ratio4x3,
        //   CropAspectRatioPreset.ratio16x9
        // ],
        uiSettings: [
          AndroidUiSettings(
              toolbarTitle: 'Cropper',
              toolbarColor: Colors.deepOrange,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: CropAspectRatioPreset.original,
              lockAspectRatio: false),
          IOSUiSettings(
            title: 'Cropper',
          ),
          WebUiSettings(
            context: context,
          ),
        ],
      );
      if (croppedFile != null) {
        return File(croppedFile.path);
      }
    }
    return null;
  }
}
