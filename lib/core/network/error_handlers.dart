import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'exceptions/api_exception.dart';
import 'exceptions/app_exception.dart';
import 'exceptions/network_exception.dart';
import 'exceptions/not_found_exception.dart';
import 'exceptions/service_unavailable_exception.dart';
import '../utils/Utils.dart';

Exception handleError(String error) {
  // final logger = BuildConfig.instance.config.logger;
  debugPrint("LOG >> Generic exception: $error");

  return AppException(message: error);
}

Exception handleDioError(DioException dioError) {
  switch (dioError.type) {
    case DioExceptionType.cancel:
      return AppException(message: "Request to API server was cancelled");
    case DioExceptionType.connectionTimeout:
      return AppException(message: "Connection timeout with API server");
    case DioExceptionType.connectionError:
      return NetworkException("Please check Internet Connection.");
    case DioExceptionType.receiveTimeout:
      return TimeoutException("Receive timeout in connection with API server");
    case DioExceptionType.sendTimeout:
      return TimeoutException("Send timeout in connection with API server");
    case DioExceptionType.badResponse:
      return _parseDioErrorResponse(dioError);
    case DioExceptionType.badCertificate:
     return AppException(message: "Bad Certificate");
    case DioExceptionType.unknown:
      return AppException(message: "Your session has been expire please login again");
  }
}


Exception _parseDioErrorResponse(DioException dioError) {
  // final logger = BuildConfig.instance.config.logger;

  int statusCode = dioError.response?.statusCode ?? -1;
  dynamic status;
  String? serverMessage;

  try {
    if (statusCode == -1 || statusCode == HttpStatus.ok) {
      statusCode = dioError.response?.data["statusCode"];
    }
    status = dioError.response?.data["status"];

    // Handle message field which can be either a String or a Map
    final messageData = dioError.response?.data["message"];
    if (messageData is String) {
      serverMessage = Utils().cleanHtmlText(messageData);
    } else if (messageData is Map<String, dynamic>) {
      // Extract error message from the Map structure
      final errorList = messageData['error'] as List?;
      final generalList = messageData['general'] as List?;

      if (errorList != null && errorList.isNotEmpty) {
        serverMessage = Utils().cleanHtmlText(errorList.first.toString());
      } else if (generalList != null && generalList.isNotEmpty) {
        serverMessage = Utils().cleanHtmlText(generalList.first.toString());
      } else {
        serverMessage = 'Unknown error occurred';
      }
    } else {
      serverMessage = messageData != null ? Utils().cleanHtmlText(messageData.toString()) : null;
    }

  } catch (e, s) {
    debugPrint("LOG >>$e");
    debugPrint("LOG >>$s");
    serverMessage = serverMessage ?? 'Error parsing response';
  }

  switch (statusCode) {
    case HttpStatus.serviceUnavailable:
      return ServiceUnavailableException("Service Temporarily Unavailable");
    case HttpStatus.notFound:
      return NotFoundException(serverMessage ?? "", status ?? "");
    default:
      return ApiException(
          httpCode: statusCode,
          status: status??0,
          message: serverMessage ?? "");
  }
}
