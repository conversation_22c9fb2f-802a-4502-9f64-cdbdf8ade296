import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import 'pretty_dio_logger.dart';

class DioProvider {
  static Dio? _instance;

  static const int _maxLineWidth = 90;
  static final _prettyDioLogger = PrettyDioLogger(
    requestHeader: true,
    requestBody: true,
    responseBody: true,
    responseHeader: false,
    error: true,
    compact: true,
    maxWidth: _maxLineWidth,
  );

  static final BaseOptions _options = BaseOptions(
    baseUrl: 'https://api.mastercook.ai/',
    connectTimeout: const Duration(milliseconds: 60 * 1000),
    receiveTimeout: const Duration(milliseconds: 60 * 1000),
  );

  static Dio get httpDio {
    if (_instance == null) {
      _instance = Dio(_options);
      _instance!.interceptors.add(_prettyDioLogger);
    }
    return _instance!;
  }

  /// Returns a Dio client with custom headers
  static Dio get tokenClient {
    _addInterceptors();
    return _instance!;
  }

  /// Adds a token refresh interceptor which retries the request when it's unauthorized
  static Dio get dioWithHeaderToken {
    _addInterceptors();
    return _instance!;
  }

  /// Adds interceptors including custom headers
  static void _addInterceptors() {
    _instance ??= httpDio;
    _instance!.interceptors.clear();
    // _instance!.interceptors.add(RequestHeaderInterceptor());
    _instance!.interceptors
        .add(_HeaderInterceptor()); // Add custom header interceptor
    _instance!.interceptors.add(_prettyDioLogger);
  }

  static String _buildContentType(String version) {
    return "user_defined_content_type+$version";
  }

  DioProvider.setContentType(String version) {
    _instance?.options.contentType = _buildContentType(version);
  }

  DioProvider.setContentTypeApplicationJson() {
    _instance?.options.contentType = "application/json";
  }
}

/// Custom Header Interceptor
class _HeaderInterceptor extends Interceptor {
  @override
  Future<void> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // Add dynamic headers
    options.headers.addAll({
      "Content-Type": "application/json",
      "Accept": "application/json",
    });
    // if(authToken.isEmpty){
    //   authToken=  await preferenceManager.getString(keyToken);
    // }
    //
    // debugPrint("auth token: >>>>>>> $authToken");
    // // Conditionally add Authorization header if authToken is not empty
    // if (authToken.isNotEmpty) {
    //   options.headers["Authorization"] = "Bearer $authToken";
    // }
    super.onRequest(options, handler);
  }
}
