import 'package:mastercookai/domain/repositories/repository_implementation.dart';
import '../../app/imports/packages_imports.dart';
import '../data/partner_api.dart';
import '../helpers/local_storage_service.dart';
import 'app_status.dart';


abstract class BaseNotifier<T> extends StateNotifier<AppState<T>> {
  final Ref ref;
  late final RepositoryImplementation repo;
  late final LocalStorageService localStorage;

  BaseNotifier(this.ref, AppState<T> initialState) : super(initialState) {
    repo = ref.read(partnerApiProvider);
    localStorage = ref.read(localStorageProvider);
  }
}