// Generic AppStatus enum for global use
// Full CRUD + granular loading AppStatus enum
enum AppStatus {
  // General states
  idle,            // Initial/default state
  loading,         // Generic loading
  loadingMore,     // Pagination loading
  success,         // Generic success
  otpVerificationSuccess, // OTP verification success
  error,           // Generic error
  empty,           // No data available

  // Create operation
  creating,        // When creating a new item
  createSuccess,   // When creation is successful
  createError,     // When creation fails

  // Update operation
  updating,        // When updating an item
  updateSuccess,   // When update is successful
  updateError,     // When update fails

  // Delete operation
  deleting,        // When deleting an item
  deleteSuccess,   // When deletion is successful
  deleteError,     // When deletion fails
}


// Generic AppState class to handle state dynamically
class AppState<T> {
  final AppStatus status;
  final T? data;
  final String? errorMessage;
  final bool hasMore;
  final int currentPage;
  final int totalItems;
  final int? totalPageCount;


  const AppState({
    this.status = AppStatus.idle,
    this.data,
    this.errorMessage,
    this.hasMore = true,
    this.currentPage = 1,
    this.totalItems = 0,
    this.totalPageCount,
   });

  AppState<T> copyWith({
    AppStatus? status,
    T? data,
    String? errorMessage,
    bool? hasMore,
    int? currentPage,
    int? totalItems,
    int? totalPageCount,

  }) {
    return AppState<T>(
      status: status ?? this.status,
      data: data ?? this.data,
      errorMessage: errorMessage ?? this.errorMessage,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      totalItems: totalItems ?? this.totalItems,
      totalPageCount: totalPageCount ?? this.totalPageCount,

    );
  }
}
