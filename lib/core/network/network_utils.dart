// lib/core/utils/network_utils.dart
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod/src/framework.dart';

import '../../main.dart';
import '../helpers/local_storage_service.dart';
import 'exceptions/base_exception.dart';

Future<T?> callDataService<T>(
    Future<T> future, {
      Function(BaseException exception)? onError,
      Function(T response)? onSuccess,
      Function? onStart,
      Function? onComplete,
      bool hideKeyboard = true,
      required Ref<Object?> ref,
    }) async {
  Exception? excp;
  if (onStart != null) onStart();
  try {
    final T response = await future;
    debugPrint('callDataService - Success response received: $response');
    if (onSuccess != null) {
      debugPrint('callDataService - Calling onSuccess callback');
      onSuccess(response);
    }
    if (onComplete != null) onComplete();
    return response;
  } catch (error) {
    excp = Exception("$error");
    debugPrint("LOG >>Controller>>>>>> $excp");
    debugPrint("LOG >> Controller >>>>>> $error");

    final baseException = error is BaseException
        ? error
        : AppException(message: error.toString()); // Use a concrete class here

    if (onError != null) {
      if(baseException.message=='Invalid token.'){
        debugPrint('callDataService - Invalid token detected, clearing login data');
        final localStorage = ref.read(localStorageProvider);
        await localStorage.clearLoginData();
        navigatorKey.currentContext!.go('/splash');
      }
      debugPrint('callDataService - Calling onError callback with: ${baseException.message}');
      onError(baseException);
    }
  }
  if (onError != null && excp is BaseException) {
    debugPrint('callDataService - Calling onError callback with: ${(excp as BaseException).message}');
    onError(excp);
  }
  if (onComplete != null) onComplete();
  return null;
}
