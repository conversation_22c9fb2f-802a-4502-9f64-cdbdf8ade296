import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'file_picker_service.g.dart';

@riverpod
class FilePickerService extends _$FilePickerService {
  @override
  void build() {
    // No state to initialize
  }

  Future<File?> pickFile({List<String>? allowedExtensions}) async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        return await _pickFileMobile();
      } else {
        return await _pickFileDesktop(allowedExtensions: allowedExtensions);
      }
    } catch (e, stackTrace) {
      debugPrint('File picking error: $e\n$stackTrace');
      return null;
    }
  }

  Future<File?> pickMz2File() async {
    try {
      // if (Platform.isAndroid || Platform.isIOS) {
      //   return await _pickMz2FileMobile();
      // } else {
      //   return await _pickFileDesktop(allowedExtensions: ['mz2']);
      // }
      if (Platform.isAndroid ) {
        return await _pickMz2FileMobile();
      }else  if (Platform.isIOS) {
        return await _pickMz2FileMobileIOS();
      }  else {
        return await _pickFileDesktop(allowedExtensions: ['mz2']);
      }
    } catch (e, stackTrace) {
      debugPrint('MZ2 file picking error: $e\n$stackTrace');
      return null;
    }
  }

  Future<File?> _pickFileMobile() async {
    try {
      final source = await _showMobileSourceDialog();
      if (source == null) return null;

      final picker = ImagePicker();
      final XFile? xfile = await picker.pickImage(
        source: source,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (xfile == null) return null;

      return File(xfile.path);
    } catch (e, stackTrace) {
      debugPrint('Mobile file picking error: $e\n$stackTrace');
      return null;
    }
  }

  // Future<File?> _pickMz2FileMobile() async {
  //   try {
  //     final result = await FilePicker.platform.pickFiles(
  //       type: FileType.custom,
  //       allowedExtensions: ['mz2'],
  //       allowMultiple: false,
  //     );

  //     if (result == null || result.files.isEmpty) return null;

  //     final filePath = result.files.single.path;
  //     if (filePath == null) return null;

  //     return File(filePath);
  //   } catch (e, stackTrace) {
  //     debugPrint('Mobile MZ2 file picking error: $e\n$stackTrace');
  //     return null;
  //   }
  // }

  Future<File?> _pickMz2FileMobile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,//allowedExtensions: ['mz2']
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) return null;

      final filePath = result.files.single.path;
      if (filePath == null) return null;

      final file = result.files.single;

      if (file.extension?.toLowerCase() == 'mz2') {
        debugPrint("Picked valid MZ2 file: ${file.path}");
      } else {
        debugPrint("Invalid file type: ${file.name}");
        return null;
      }
      return File(filePath);
    } catch (e, stackTrace) {
      debugPrint('Mobile MZ2 file picking error: $e\n$stackTrace');
      return null;
    }
  }

  Future<File?> _pickMz2FileMobileIOS() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['mz2'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) return null;

      final filePath = result.files.single.path;
      if (filePath == null) return null;

      return File(filePath);
    } catch (e, stackTrace) {
      debugPrint('Mobile MZ2 file picking error: $e\n$stackTrace');
      return null;
    }
  }

  Future<File?> _pickFileDesktop({List<String>? allowedExtensions}) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: allowedExtensions != null ? FileType.custom : FileType.any,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) return null;

      final filePath = result.files.single.path;
      if (filePath == null) return null;

      return File(filePath);
    } catch (e, stackTrace) {
      debugPrint('Desktop file picking error: $e\n$stackTrace');
      return null;
    }
  }

  Future<ImageSource?> _showMobileSourceDialog() async {
    final context = ref.read(navigatorKeyProvider).currentContext;
    if (context == null) return null;

    return showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Source'),
        content: const Text('Choose where to pick the file from:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, ImageSource.camera),
            child: const Text('Camera'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, ImageSource.gallery),
            child: const Text('Gallery'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, null),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

// Global navigator key provider for showing dialogs
final navigatorKeyProvider = Provider<GlobalKey<NavigatorState>>((ref) {
  return GlobalKey<NavigatorState>();
});