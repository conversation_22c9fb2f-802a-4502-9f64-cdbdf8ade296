import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mastercookai/core/helpers/Mz2Parser.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/widgets/quota_exhausted_dialog.dart';
import 'package:mastercookai/core/widgets/recipe_quota_exhausted_dialog.dart';
import 'package:mastercookai/core/widgets/uploading_dialog.dart';

/// Data class for upload progress information
class UploadProgressData {
  final String fileName;
  final String cookBookName;
  final int totalRecipes;
  final int uploadedRecipes;

  const UploadProgressData({
    required this.fileName,
    required this.cookBookName,
    required this.totalRecipes,
    required this.uploadedRecipes,
  });
}

/// Helper class for validating mz2 files before upload
class Mz2ValidationHelper {
  static ValueNotifier<UploadProgressData>? _progressNotifier;

  /// Shows upload progress dialog (only once)
  static void showUploadProgress({
    required String fileName,
    required String cookBookName,
    required int totalRecipes,
    required int uploadedRecipes,
  }) {
    // If dialog is already showing, just update the progress
    if (_progressNotifier != null) {
      updateUploadProgress(
        fileName: fileName,
        cookBookName: cookBookName,
        totalRecipes: totalRecipes,
        uploadedRecipes: uploadedRecipes,
      );
      return;
    }

    // Create progress notifier
    _progressNotifier = ValueNotifier(UploadProgressData(
      fileName: fileName,
      cookBookName: cookBookName,
      totalRecipes: totalRecipes,
      uploadedRecipes: uploadedRecipes,
    ));

    SmartDialog.show(
      clickMaskDismiss: false, // Prevent dismissal by clicking mask
      builder: (_) {
        return Align(
          alignment: Alignment.topRight,
          child: Material(
            color: Colors.transparent,
            child: ValueListenableBuilder<UploadProgressData>(
              valueListenable: _progressNotifier!,
              builder: (context, data, child) {
                final progress = data.totalRecipes > 0
                    ? (data.uploadedRecipes / data.totalRecipes).toDouble()
                    : 0.0;

                return UploadingDialog(
                  title: data.cookBookName.isEmpty
                      ? "Importing your Cookbook..."
                      : "Importing your recipes...",
                  subtitle: data.cookBookName.isEmpty
                      ? data.fileName
                      : '${data.uploadedRecipes} of ${data.totalRecipes} recipes uploaded. Sit back while we prepare your cookbook!',
                  progress: progress,
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// Updates upload progress without recreating the dialog
  static void updateUploadProgress({
    required String fileName,
    required String cookBookName,
    required int totalRecipes,
    required int uploadedRecipes,
  }) {
    if (_progressNotifier != null) {
      _progressNotifier!.value = UploadProgressData(
        fileName: fileName,
        cookBookName: cookBookName,
        totalRecipes: totalRecipes,
        uploadedRecipes: uploadedRecipes,
      );
    }
  }

  /// Hides upload progress dialog
  static void hideUploadProgress() {
    SmartDialog.dismiss();
    _progressNotifier?.dispose();
    _progressNotifier = null;
  }

  /// Validates file size and recipe count for mz2 files
  /// Returns a validation result indicating whether to proceed, skip, or stop
  static Future<Mz2ValidationResult> validateMz2File({
    required File file,
    required BuildContext context,
    required WidgetRef ref,
  }) async {
    try {
      // Get user profile for validations
      final userProfileNotifier =
          ref.read(userProfileNotifierProvider.notifier);
      // 1. Check file size validation
      final fileSizeInBytes = await file.length();
      final fileSizeInMB = fileSizeInBytes / (1024 * 1024);
      final recipeStorageInMB = userProfileNotifier.recipeStorageInMB;

      if (fileSizeInMB > recipeStorageInMB) {
        if (context.mounted) {
          showQuotaExhaustedDialog(
            context,
            recipeStorageInMB: '$recipeStorageInMB MB',
            fileSizeInMB: fileSizeInMB.toStringAsFixed(1),
          );
        }
        return Mz2ValidationResult.fileSizeExceeded();
      }

      // 2. Read bytes and parse mz2 archive to check recipe count
      final bytes = await file.readAsBytes();
      final result = await parseMz2Bytes(bytes);
      final totalRecipesInFile = result.recipes.length;
      final recipeQuota = userProfileNotifier.recipeQuota;

      // 3. Check recipe quota validation
      if (totalRecipesInFile > recipeQuota) {
        if (context.mounted) {
          final userChoice = await showRecipeQuotaExhaustedDialog(
            context,
            totalRecipes: totalRecipesInFile,
            remainingRecipes: recipeQuota,
          );

          if (userChoice == "proceed") {
            // User choose to proceed with limited recipes
            return Mz2ValidationResult.proceedWithLimit(
              parseResult: result,
              totalRecipes: totalRecipesInFile,
              limitedRecipes: recipeQuota,
            );
          } else {
            // User choose to skip/buy plan or dismissed dialog
            return Mz2ValidationResult.userCancelled();
          }
        } else {
          return Mz2ValidationResult.userCancelled();
        }
      }

      // 4. All validations passed
      return Mz2ValidationResult.success(
        parseResult: result,
        totalRecipes: totalRecipesInFile,
      );
    } catch (e) {
      debugPrint('Mz2 validation error: $e');
      return Mz2ValidationResult.error(e.toString());
    }
  }
}

/// Result of mz2 file validation
class Mz2ValidationResult {
  final Mz2ValidationStatus status;
  final Mz2ParseResult? parseResult;
  final int? totalRecipes;
  final int? limitedRecipes;
  final String? errorMessage;

  const Mz2ValidationResult._({
    required this.status,
    this.parseResult,
    this.totalRecipes,
    this.limitedRecipes,
    this.errorMessage,
  });

  /// File size exceeded storage quota
  factory Mz2ValidationResult.fileSizeExceeded() {
    return const Mz2ValidationResult._(
        status: Mz2ValidationStatus.fileSizeExceeded);
  }

  /// User cancelled or choose to skip
  factory Mz2ValidationResult.userCancelled() {
    return const Mz2ValidationResult._(
        status: Mz2ValidationStatus.userCancelled);
  }

  /// Proceed with limited recipes due to quota
  factory Mz2ValidationResult.proceedWithLimit({
    required Mz2ParseResult parseResult,
    required int totalRecipes,
    required int limitedRecipes,
  }) {
    return Mz2ValidationResult._(
      status: Mz2ValidationStatus.proceedWithLimit,
      parseResult: parseResult,
      totalRecipes: totalRecipes,
      limitedRecipes: limitedRecipes,
    );
  }

  /// All validations passed, proceed with all recipes
  factory Mz2ValidationResult.success({
    required Mz2ParseResult parseResult,
    required int totalRecipes,
  }) {
    return Mz2ValidationResult._(
      status: Mz2ValidationStatus.success,
      parseResult: parseResult,
      totalRecipes: totalRecipes,
    );
  }

  /// Validation error occurred
  factory Mz2ValidationResult.error(String message) {
    return Mz2ValidationResult._(
      status: Mz2ValidationStatus.error,
      errorMessage: message,
    );
  }

  /// Whether the upload should proceed
  bool get shouldProceed =>
      status == Mz2ValidationStatus.success ||
      status == Mz2ValidationStatus.proceedWithLimit;

  /// Whether recipes should be limited
  bool get shouldLimitRecipes => status == Mz2ValidationStatus.proceedWithLimit;

  /// Get recipes to upload (limited if necessary)
  List<RecipeZ> getRecipesToUpload() {
    if (parseResult == null) return [];

    if (shouldLimitRecipes && limitedRecipes != null) {
      return parseResult!.recipes.take(limitedRecipes!).toList();
    }

    return parseResult!.recipes;
  }

  /// Get success message for upload completion
  String getSuccessMessage() {
    final uploadedCount = getRecipesToUpload().length;

    if (shouldLimitRecipes && totalRecipes != null) {
      return 'Successfully uploaded $uploadedCount out of $totalRecipes recipes (limited by your quota)';
    }

    return 'Successfully uploaded all $uploadedCount recipes';
  }
}

/// Status of mz2 validation
enum Mz2ValidationStatus {
  success,
  proceedWithLimit,
  fileSizeExceeded,
  userCancelled,
  error,
}
