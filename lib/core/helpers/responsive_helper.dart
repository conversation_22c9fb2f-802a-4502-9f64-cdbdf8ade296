import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../app/imports/core_imports.dart';

class ResponsiveHelper {
  static bool isExtraLargeScreen(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1920;
  }

  static bool isLargeScreen(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1440;
  }

  static bool isMediumScreen(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1024;
  }

  static double getResponsiveFontSize(BuildContext context) {
    if (isExtraLargeScreen(context)) return 18.sp;
    if (isLargeScreen(context)) return 16.sp;
    return 14.sp;
  }
}