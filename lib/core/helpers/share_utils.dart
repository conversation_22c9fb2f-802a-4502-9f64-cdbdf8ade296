import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cross_file/cross_file.dart';

class ShareUtils {
  /// Share plain text, optionally with a subject and title
  static Future<void> shareText({
    required BuildContext context,
    required String text,
    String? subject,
    String? title,
  }) async {
    final box = context.findRenderObject() as RenderBox?;
    final result = await SharePlus.instance.share(
      ShareParams(
        text: text,
        subject: subject,
        title: title,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
      ),
    );
    _showResult(context, result);
  }

  /// Share a URI (like a link)
  static Future<void> shareUri({
    required BuildContext context,
    required String uri,
    String? subject,
    String? title,
  }) async {
    final box = context.findRenderObject() as RenderBox?;
    final result = await SharePlus.instance.share(
      ShareParams(
        uri: Uri.parse(uri),
        subject: subject,
        title: title,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
      ),
    );
    _showResult(context, result);
  }

  /// Share files by path
  static Future<void> shareFiles({
    required BuildContext context,
    required List<String> filePaths,
    List<String>? fileNames,
    String? text,
    String? subject,
    String? title,
  }) async {
    final box = context.findRenderObject() as RenderBox?;
    final files = List.generate(filePaths.length, (i) {
      return XFile(filePaths[i], name: fileNames?[i]);
    });

    final result = await SharePlus.instance.share(
      ShareParams(
        text: text,
        subject: subject,
        title: title,
        files: files,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
      ),
    );
    _showResult(context, result);
  }

  /// Share a string as a .txt file
  static Future<void> shareTextAsFile({
    required BuildContext context,
    required String content,
    required String fileName,
  }) async {
    final box = context.findRenderObject() as RenderBox?;
    final result = await SharePlus.instance.share(
      ShareParams(
        files: [
          XFile.fromData(
            utf8.encode(content),
            mimeType: 'text/plain',
          )
        ],
        fileNameOverrides: [fileName],
        downloadFallbackEnabled: true,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
      ),
    );
    _showResult(context, result);
  }
static Future<void> shareAll({
  required BuildContext context,
  required String text,
  String? subject,
  String? title,
  String? filePath,
}) async {
  final box = context.findRenderObject() as RenderBox?;

  final List<XFile> files = [];
  if (filePath != null && filePath.isNotEmpty) {
    files.add(XFile(filePath));
  }

  final result = await SharePlus.instance.share(
    ShareParams(
      text: text,
      subject: subject,
      title: title,
      files: files.isNotEmpty ? files : null,
      sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
    ),
  );

  _showResult(context, result);
}
  /// Share an asset file like image from assets/
  static Future<void> shareAssetImage({
    required BuildContext context,
    required String assetPath,
    required String fileName,
    String mimeType = 'image/png',
  }) async {
    final box = context.findRenderObject() as RenderBox?;
    final data = await rootBundle.load(assetPath);
    final buffer = data.buffer;

    final result = await SharePlus.instance.share(
      ShareParams(
        files: [
          XFile.fromData(
            buffer.asUint8List(data.offsetInBytes, data.lengthInBytes),
            name: fileName,
            mimeType: mimeType,
          ),
        ],
        downloadFallbackEnabled: true,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
      ),
    );
    _showResult(context, result);
  }

  static void _showResult(BuildContext context, ShareResult result) {
    final message = result.status == ShareResultStatus.success
        ? 'Shared to: ${result.raw}'
        : 'Share result: ${result.status}';
    // ScaffoldMessenger.of(context).showSnackBar(
    //   SnackBar(content: Text(message)),
    // );
  }
}
