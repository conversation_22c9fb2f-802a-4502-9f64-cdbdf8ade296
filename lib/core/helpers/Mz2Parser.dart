import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:archive/archive.dart';
import 'package:mastercookai/core/data/request_query/create_recipe_request.dart';
import 'package:path_provider/path_provider.dart';
import 'package:xml/xml.dart' as xml;
import 'package:image/image.dart' as img;

class Ingredient {
  final String? qty, unit, name;
  Ingredient({this.qty, this.unit, this.name});
}

class Mz2ParseResult {
  final List<RecipeZ> recipes;
  final Map<String, Uint8List> imagesByName;
  Mz2ParseResult(this.recipes, this.imagesByName);
}

Future<Mz2ParseResult> parseMz2Bytes(Uint8List mz2Bytes) async {
  final archive = ZipDecoder().decodeBytes(mz2Bytes);

  // 1) find .mx2 entry
  final mx2Entry = archive.files.firstWhere(
    (f) => f.name.toLowerCase().endsWith('.mx2'),
    orElse: () => throw Exception('No .mx2 file found in archive'),
  );
  final mx2Bytes = mx2Entry.content as List<int>;
  final doc = xml.XmlDocument.parse(String.fromCharCodes(mx2Bytes));

  // 2) extract images
  final images = <String, Uint8List>{};
  final imageFilesList = <String>[];
  final originalToCorrectedNames = <String, String>{};

  for (final f in archive.files) {
    final n = f.name.toLowerCase();
    String fileName = f.name;
    // Check if file has proper image extension
    bool hasImageExtension = n.endsWith('.jpg') ||
        n.endsWith('.jpeg') ||
        n.endsWith('.png') ||
        n.endsWith('.svg');
    //If no extension, check last 3 characters and add extension if needed
    if (!hasImageExtension) {
      final last3Chars = n.length >= 3 ? n.substring(n.length - 3) : '';
      final last4Chars = n.length >= 4 ? n.substring(n.length - 4) : '';

      if (last3Chars == 'jpg' || last3Chars == 'png' || last3Chars == 'svg') {
        // Trim the extension word and add it back with a dot
        final baseFileName = f.name.substring(0, f.name.length - 3);
        fileName = '$baseFileName.$last3Chars';
        hasImageExtension = true;
      } else if (last4Chars == 'jpeg') {
        // Trim the extension word and add it back with a dot
        final baseFileName = f.name.substring(0, f.name.length - 4);
        fileName = '$baseFileName.jpeg';
        hasImageExtension = true;
      }
    }

    if (hasImageExtension) {
      final bytes = Uint8List.fromList(f.content as List<int>);
      final originalKey = f.name.split('/').last;
      final correctedKey = fileName.split('/').last;
      // Store both original and corrected names
      images[originalKey] = bytes;
      images[correctedKey] = bytes;
      // Map original to corrected name
      originalToCorrectedNames[originalKey] = correctedKey;
      imageFilesList.add(fileName);
    }
  }

  /*
  //Save XML data and image files to .txt file
  final xmlString = doc.toXmlString(pretty: true);
  print('toXmlString data: $xmlString');
  try {
    final dir = await getApplicationDocumentsDirectory();
    final file = File('${dir.path}/parsed_xml_data.txt');

    // Combine XML data with image files list
    final combinedData = StringBuffer();
    combinedData.writeln('=== IMAGE FILES FOUND IN ARCHIVE ===');
    for (final imagePath in imageFilesList) {
      combinedData.writeln('Image file: $imagePath');
    }
    combinedData.writeln('=== END OF IMAGE FILES ===\n');
    combinedData.writeln('=== XML DATA ===');
    combinedData.write(xmlString);

    await file.writeAsString(combinedData.toString());
    print('XML data and image files saved to: ${file.path}');
  } catch (e) {
    print('Error saving XML data to file: $e');
  }
*/
  // 3) parse recipes
  final recipes = <RecipeZ>[];
  for (final r in doc.findAllElements('RcpE')) {
    String name = r.getAttribute('name') ?? '(Unnamed)';
    String? author = r.getAttribute('author');
    String? imageFileName = r.getAttribute('img');

    // find matching image bytes from map and get corrected filename
    Uint8List? imageBytes;
    String? correctedImageFileName = imageFileName;
    if (imageFileName != null) {
      final imageKey = imageFileName.split('/').last;
      imageBytes = images[imageKey];

      // Use corrected filename if available
      if (originalToCorrectedNames.containsKey(imageKey)) {
        correctedImageFileName = originalToCorrectedNames[imageKey];
      }
    }

    String? yieldValue = r.getElement('Yield')?.innerText.trim();
    String? prepTime = r.getElement('PrpT')?.getAttribute('elapsed');
    int? servings =
        int.tryParse(r.getElement('Serv')?.getAttribute('qty') ?? '');

    final categories = r
            .getElement('CatS')
            ?.findElements('CatT')
            .map((e) => e.innerText.trim())
            .where((s) => s.isNotEmpty)
            .toList() ??
        <String>[];

    final ingredients = r.findElements('IngR').map((i) {
      return Ingredient(
        qty: i.getAttribute('qty'),
        unit: i.getAttribute('unit'),
        name: i.getAttribute('name'),
      );
    }).toList();

    final directions = r
            .getElement('DirS')
            ?.findElements('DirT')
            .map((d) => d.innerText.trim())
            .where((s) => s.isNotEmpty)
            .toList() ??
        <String>[];

    final notes = r.getElement('Note')?.innerText.trim();
    final nutrition = r.getElement('Nutr')?.innerText.trim();

    // Extract cuisine information
    final cuisine = r.getElement('Natn')?.innerText.trim() ??
        r.getElement('Cuis')?.innerText.trim();

    recipes.add(RecipeZ(
      name: name,
      author: author,
      yieldValue: yieldValue,
      prepTime: prepTime,
      servings: servings,
      imageName: correctedImageFileName,
      imageBytes: imageBytes,
      categories: categories,
      ingredients: ingredients,
      directions: directions,
      notes: notes,
      nutrition: nutrition,
      cuisine: cuisine,
    ));
  }
  return Mz2ParseResult(recipes, images);
}

class RecipeZ {
  final String name;
  final String? author, yieldValue, prepTime, notes, nutrition, cuisine;
  final int? servings;
  final String? imageName;
  final Uint8List? imageBytes;
  final List<String> categories;
  final List<Ingredient> ingredients;
  final List<String> directions;

  RecipeZ({
    required this.name,
    this.author,
    this.yieldValue,
    this.prepTime,
    this.servings,
    this.imageName,
    this.imageBytes,
    required this.categories,
    required this.ingredients,
    required this.directions,
    this.notes,
    this.nutrition,
    this.cuisine,
  });
}

Future<CreateRecipeRequest> recipeZToRequest(RecipeZ r) async {
  File? imageFile =
      await saveImageTemp(r.imageName ?? 'cover.jpg', r.imageBytes);

  // Convert directions to proper JSON format
  String? directionsJson;
  if (r.directions.isNotEmpty) {
    // Convert List<String> directions to List<Map> format for convertInstructionsToDirectionsJson
    final instructionsData =
        r.directions.map((direction) => {'Text': direction}).toList();
    directionsJson = convertInstructionsToDirectionsJson(instructionsData);
  }

  return CreateRecipeRequest(
    type: "Recipe",
    name: r.name,
    description: r.nutrition ?? r.notes,
    categoryName: r.categories.isNotEmpty ? r.categories.first : null,
    cuisineName: r.cuisine,
    servings: (r.servings == null || r.servings == 0) ? null : r.servings,
    prepTime: r.prepTime,
    recipeMediaFiles: imageFile != null ? [imageFile] : null,
    ingredients: r.ingredients.map((i) {
      return [
        if (i.qty?.isNotEmpty ?? false) i.qty!,
        if (i.unit?.isNotEmpty ?? false) i.unit!,
        if (i.name?.isNotEmpty ?? false) i.name!,
      ].join(" ");
    }).toList(),
    directionsJson: directionsJson,
    notes: r.notes,
    authorName: r.author,
  );
}

/// Converts MZ2 JSON recipe data to CreateRecipeRequest
/// This function handles the JSON format you provided with Instructions array
Future<CreateRecipeRequest> mz2JsonToRequest(
    Map<String, dynamic> recipeJson) async {
  final name = recipeJson['Name']?.toString() ?? 'Untitled Recipe';
  final description = recipeJson['Description']?.toString();
  final notes = recipeJson['Notes']?.toString();
  final author = recipeJson['Author']?.toString();
  final servings = recipeJson['Servings'] as int?;
  final prepTime = recipeJson['PrepTime']?.toString();
  final cookTime = recipeJson['CookTime']?.toString();

  // Extract ingredients
  final ingredientsList = <String>[];
  final ingredients = recipeJson['Ingredients'] as List<dynamic>? ?? [];
  for (final ingredient in ingredients) {
    final qty = ingredient['qty']?.toString() ?? '';
    final unit = ingredient['unit']?.toString() ?? '';
    final name = ingredient['name']?.toString() ?? '';

    final parts = <String>[];
    if (qty.isNotEmpty) parts.add(qty);
    if (unit.isNotEmpty) parts.add(unit);
    if (name.isNotEmpty) parts.add(name);
    if (parts.isNotEmpty) {
      ingredientsList.add(parts.join(' '));
    }
  }

  // Convert Instructions to DirectionsJson
  final instructions = recipeJson['Instructions'] as List<dynamic>? ?? [];
  final directionsJson = convertInstructionsToDirectionsJson(instructions);

  // Extract categories
  final categories = recipeJson['Categories'] as List<dynamic>? ?? [];
  final categoryName = categories.isNotEmpty
      ? categories.first['Name']?.toString().trim()
      : null;

  // Extract cuisine
  final cuisine = recipeJson['Cuisine'] as Map<String, dynamic>?;
  final cuisineName = cuisine?['Name']?.toString().trim();

  return CreateRecipeRequest(
    type: "Recipe",
    name: name,
    description: description,
    categoryName: categoryName != "" ? categoryName : null,
    cuisineName: cuisineName != "" ? cuisineName : null,
    servings: servings,
    prepTime: prepTime,
    cookTime: cookTime,
    ingredients: ingredientsList,
    directionsJson: directionsJson.isNotEmpty ? directionsJson : null,
    notes: notes != "" ? notes : null,
    authorName: author != "" ? author : null,
  );
}

Future<File?> saveImageTemp(String name, Uint8List? bytes) async {
  if (bytes == null) return null;

  // Decode the image
  final image = img.decodeImage(bytes);
  if (image == null) return null;

  // Determine extension
  final ext = name.split('.').last.toLowerCase();

  List<int>? outBytes;

  if (ext == 'jpg' || ext == 'jpeg') {
    // JPEG with compression loop
    int quality = 90;
    while (quality > 10) {
      outBytes = img.encodeJpg(image, quality: quality);
      if (outBytes.length <= 100 * 1024) break; // under 100KB
      quality -= 10;
    }
    // fallback if still too big
    outBytes ??= img.encodeJpg(image, quality: 50);
  } else if (ext == 'png') {
    // PNG (no quality compression available)
    outBytes = img.encodePng(image);
    // If >100KB, optionally resize down
    if (outBytes.length > 100 * 1024) {
      final resized = img.copyResize(image, width: image.width ~/ 2);
      outBytes = img.encodePng(resized);
    }
  } else {
    // Default to JPG if unknown extension
    outBytes = img.encodeJpg(image, quality: 80);
  }

  // Save
  final dir = await getTemporaryDirectory();
  final file = File('${dir.path}/$name');
  await file.writeAsBytes(outBytes);
  return file;
}

/// Converts MZ2 Instructions array to DirectionsJson format
String convertInstructionsToDirectionsJson(List<dynamic> instructions) {
  if (instructions.isEmpty) return '';

  final directions = <Map<String, dynamic>>[];

  for (int i = 0; i < instructions.length; i++) {
    final instruction = instructions[i];
    final text = instruction['Text']?.toString().trim() ?? '';

    // Check if ImageFile exists and extract MediaFileName and MediaFileId
    String? mediaFileName;
    String? mediaFileId;

    if (instruction['ImageFile'] != null) {
      final imageFile = instruction['ImageFile'];
      mediaFileName = imageFile['FileName']?.toString();
      mediaFileId = imageFile['FileId']?.toString();
      print(
          'Found ImageFile in instruction $i: FileName=$mediaFileName, FileId=$mediaFileId');
    } else {
      print('No ImageFile found in instruction $i');
    }

    if (text.isNotEmpty) {
      // Create a title from the first few words or use a generic step title
      String title = 'Step ${i + 1}';
      String description = text;

      // If the text is long, try to extract a title from the first sentence
      if (text.length > 50) {
        final sentences = text.split('.');
        if (sentences.isNotEmpty && sentences[0].length <= 50) {
          title = sentences[0].trim();
          if (sentences.length > 1) {
            description = sentences.sublist(1).join('.').trim();
            if (description.startsWith('.')) {
              description = description.substring(1).trim();
            }
          }
        }
      }
      directions.add({
        'Title': title,
        'Description': description,
        'MediaFileName': mediaFileName,
        'MediaFileId': mediaFileId,
      });
    }
  }

  return jsonEncode(directions);
}
