
import 'dart:convert';
import 'package:flutter/services.dart' show rootBundle;
import 'package:universal_platform/universal_platform.dart';

import '../data/models/access_key.dart';


class AccessKeyHelper {
  static Future<String?> getAccessKey() async {
    final String response = await rootBundle.loadString('assets/access_keys.json');
    final data = json.decode(response);
    final List list = data["AccessKeys"];

    // Determine current platform
    String currentPlatform = getPlatformIdentifier();

    for (var item in list) {
      final key = AccessKeyModel.fromJson(item);
      if (key.platform == currentPlatform) {
        return key.accessKey;
      }
    }

    return null;
  }

  static String getPlatformIdentifier() {
    if (UniversalPlatform.isAndroid) return 'com.eapps.mcandroid';
    if (UniversalPlatform.isIOS) return 'com.eapps.mastercook';
    if (UniversalPlatform.isLinux) return 'com.eapps.mcaubuntu';
    if (UniversalPlatform.isWindows) return 'com.eapps.mcawindows';
    if (UniversalPlatform.isMacOS) return 'com.eapps.mcamacos';
    if (UniversalPlatform.isWeb) return 'com.web.mastercook';

    return '';
  }
}
