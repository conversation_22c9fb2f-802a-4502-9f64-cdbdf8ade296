import 'dart:async';

import 'package:flutter/material.dart';

typedef ItemBuilder<T> = Widget Function(BuildContext context, T item);
typedef ItemToString<T> = String Function(T item);
typedef OnSelected<T> = void Function(T item);
typedef FetchSuggestions<T> = Future<List<T>> Function(String query);

class CustomAutocomplete<T> extends StatefulWidget {
  final TextEditingController controller;
  final FetchSuggestions<T> fetchSuggestions;
  final ItemBuilder<T> itemBuilder;
  final ItemToString<T> itemToString;
  final OnSelected<T> onSelected;
  final InputDecoration? decoration;
  final TextStyle? style;
  final double suggestionsHeight;
  final Duration debounceDuration;
  final double? dropdownWidthMultiplier;
  final int? maxLength;

  const CustomAutocomplete({
    super.key,
    required this.controller,
    required this.fetchSuggestions,
    required this.itemBuilder,
    required this.itemToString,
    required this.onSelected,
    this.decoration,
    this.style,
    this.suggestionsHeight = 200.0,
    this.debounceDuration = const Duration(milliseconds: 300),
    this.dropdownWidthMultiplier,
    this.maxLength,
  });

  @override
  State<CustomAutocomplete<T>> createState() => _CustomAutocompleteState<T>();
}

class _CustomAutocompleteState<T> extends State<CustomAutocomplete<T>> {
  final LayerLink _layerLink = LayerLink();
  final FocusNode _focusNode = FocusNode();
  final ValueNotifier<List<T>> _suggestions = ValueNotifier([]);
  final ValueNotifier<bool> _isLoading = ValueNotifier(false);
  OverlayEntry? _overlayEntry;
  Timer? _debounceTimer;
  bool _isSelecting = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_handleFocusChange);
    widget.controller.addListener(_handleTextChanged);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    widget.controller.removeListener(_handleTextChanged);
    _debounceTimer?.cancel();
    _removeOverlay();
    _focusNode.dispose();
    super.dispose();
  }

  void _handleFocusChange() {
    if (_focusNode.hasFocus && widget.controller.text.isNotEmpty) {
      _showOverlay();
    } else if (!_focusNode.hasFocus && !_isSelecting) {
      _removeOverlay();
    }
  }

  void _handleTextChanged() {
    if (_isSelecting) {
      _isSelecting = false;
      return;
    }

    _debounceTimer?.cancel();
    _debounceTimer = Timer(widget.debounceDuration, () {
      if (widget.controller.text.isEmpty) {
        _suggestions.value = [];
        _removeOverlay();
        return;
      }

      if (_focusNode.hasFocus) {
        _fetchSuggestions(widget.controller.text);
      }
    });
  }

  Future<void> _fetchSuggestions(String query) async {
    if (query.isEmpty) {
      _suggestions.value = [];
      return;
    }

    _isLoading.value = true;
    try {
      final results = await widget.fetchSuggestions(query);
      _suggestions.value = results;
      if (results.isNotEmpty && _overlayEntry == null) {
        _showOverlay();
      } else if (results.isEmpty) {
        _removeOverlay();
      }
    } catch (e) {
      _suggestions.value = [];
      _removeOverlay();
    } finally {
      _isLoading.value = false;
    }
  }

  void _showOverlay() {
    if (_overlayEntry != null) return;

    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: size.width * (widget.dropdownWidthMultiplier ?? 1),
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, size.height + 5),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(12),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: widget.suggestionsHeight,
              ),
              child: ValueListenableBuilder<List<T>>(
                valueListenable: _suggestions,
                builder: (context, suggestions, _) {
                  return ValueListenableBuilder<bool>(
                    valueListenable: _isLoading,
                    builder: (context, isLoading, _) {
                      if (isLoading) {
                        return const Center(child: CircularProgressIndicator());
                      }
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: ListView.builder(
                          shrinkWrap: true,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          itemCount: suggestions.length,
                          itemBuilder: (context, index) {
                            final item = suggestions[index];
                            // return MouseRegion(
                            //   cursor: SystemMouseCursors.click,
                            //   child: GestureDetector(
                            //     behavior: HitTestBehavior.opaque,
                            //     onTapDown: (details) {
                            //       _isSelecting = true;

                            //       widget.onSelected(item);

                            //       Future.microtask(() {
                            //         _suggestions.value = [];
                            //         _removeOverlay();
                            //       });

                            //       WidgetsBinding.instance.addPostFrameCallback((_) {
                            //         _focusNode.unfocus();
                            //       });
                            //     },
                            //     child: widget.itemBuilder(context, item),
                            //   ),
                            // );
                            return Listener(
                              behavior: HitTestBehavior.opaque,
                              onPointerDown: (_) {
                                _isSelecting = true;

                                // Call onSelected
                                widget.onSelected(item);

                                // Remove overlay AFTER pointer down
                                Future.microtask(() {
                                  _suggestions.value = [];
                                  _removeOverlay();
                                  _focusNode.unfocus();
                                });
                              },
                              child: widget.itemBuilder(context, item),
                            );
                          },
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context, rootOverlay: true).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        controller: widget.controller,
        focusNode: _focusNode,
        style: widget.style,
        maxLength: widget.maxLength,
        decoration: widget.decoration ??
            InputDecoration(
              hintText: 'Search...',
              counterText: widget.maxLength != null ? '' : null, // Hide counter when maxLength is set
              suffixIcon: ValueListenableBuilder<bool>(
                valueListenable: _isLoading,
                builder: (context, isLoading, _) {
                  return isLoading
                      ? const Padding(
                          padding: EdgeInsets.all(8),
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.search);
                },
              ),
            ),
      ),
    );
  }
}