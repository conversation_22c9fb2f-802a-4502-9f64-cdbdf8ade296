import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

import '../../app/imports/packages_imports.dart';
import '../data/request_query/plan_request.dart';
import '../providers/profile/plans_notifier.dart';
import '../utils/Utils.dart';
import 'app_constant.dart';

// Define product IDs
const List<String> _productIds = ['basic', 'premium'];

// Provider for InAppPurchaseHelper
final inAppPurchaseProvider = Provider<InAppPurchaseHelper>((ref) {
  return InAppPurchaseHelper(ref);
});

// State provider for purchase status
final purchaseStatusProvider =
    StateProvider<PurchaseStatus>((ref) => PurchaseStatus.initial);

// State provider for purchased products list
final purchasedProductsProvider = StateProvider<List<String>>((ref) => []);

// Enum for purchase status
enum PurchaseStatus {
  initial,
  pending,
  purchased,
  error,
  restored,
}
// ... (existing imports and classes)

// In-App Purchase Helper Class
class InAppPurchaseHelper {
  final Ref ref;

  InAppPurchaseHelper(this.ref);

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  StreamSubscription<List<PurchaseDetails>>? _subscription;
  List<ProductDetails> _products = [];
  List<String> _purchasedProductIds = [];
  Set<String> _processedPurchaseIds = {};
  bool _isAvailable = false;
  int? planId;
  BuildContext? context;

  Future<void> initialize() async {
    try {
      _isAvailable = await _inAppPurchase.isAvailable();
      if (!_isAvailable) {
        debugPrint('In-App Purchase is not available on this device.');
        return;
      }
      final Stream<List<PurchaseDetails>> purchaseUpdated =
          _inAppPurchase.purchaseStream;
      _subscription = purchaseUpdated.listen(
            (purchaseDetailsList) {
          debugPrint(
              'Received purchase update for ${purchaseDetailsList.length} items');
          _listenToPurchaseUpdated(purchaseDetailsList);
        },
        onDone: () {
          _subscription?.cancel();
          debugPrint('Purchase stream closed');
        },
        onError: (error) {
          debugPrint('Purchase stream error: ${jsonEncode({
            'error': error.toString()
          })}');
          ref.read(purchaseStatusProvider.notifier).state =
              PurchaseStatus.error;
          _handlePurchaseError(error, context);
        },
      );
      await _fetchProducts();
    } catch (e) {
      debugPrint('Error initializing InAppPurchase: ${jsonEncode({
        'error': e.toString()
      })}');
      ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
      _handlePurchaseError(e, context);
    }
  }

  Future<void> _fetchProducts() async {
    try {
      final ProductDetailsResponse response =
      await _inAppPurchase.queryProductDetails(_productIds.toSet());
      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('Products not found: ${jsonEncode({
          'notFoundIDs': response.notFoundIDs
        })}');
      }
      _products = response.productDetails
        ..sort((a, b) {
          return _productIds.indexOf(a.id).compareTo(_productIds.indexOf(b.id));
        });
      debugPrint('Fetched products: ${jsonEncode(_products.map((p) => {
        'id': p.id,
        'title': p.title,
        'description': p.description,
        'price': p.price,
      }).toList())}');
    } catch (e) {
      debugPrint(
          'Error fetching products: ${jsonEncode({'error': e.toString()})}');
      ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
      _handlePurchaseError(e, context);
    }
  }

  List<ProductDetails> getProducts() => _products;

  List<String> getPurchasedProducts() => _purchasedProductIds;

  Future<bool> buyProduct(
      BuildContext context,
      ProductDetails productDetails,
      WidgetRef ref, {
        int? planId,
      }) async {
    debugPrint(
        'Initiating purchase for product: ${productDetails.id} with planId: $planId');
    this.planId = planId;
    this.context = context;
    if (_products.isEmpty) {
      debugPrint('No products available to purchase: ${jsonEncode({
        'error': 'Check App Store Connect configuration'
      })}');
      ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
      Utils().showFlushbar(context,
          message: 'No products available. Please try again later.',
          isError: true);
      return false;
    }
    ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.pending;

    final PurchaseParam purchaseParam = PurchaseParam(productDetails: productDetails);
    try {
      bool isSuccess = await _inAppPurchase.buyConsumable(
          purchaseParam: purchaseParam, autoConsume: true);
      debugPrint(
          'Purchase request sent for product: ${productDetails.id}, Success: $isSuccess');
      return isSuccess;
    } catch (e) {
      debugPrint(
          'Error initiating purchase: ${jsonEncode({'error': e.toString()})}');
      ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
      _handlePurchaseError(e, context);
      return false;
    }
  }

  Map<String, dynamic> _purchaseDetailsToJson(PurchaseDetails purchaseDetails) {
    return {
      'productID': purchaseDetails.productID,
      'purchaseID': purchaseDetails.purchaseID,
      'status': purchaseDetails.status.toString(),
      'transactionDate': purchaseDetails.transactionDate,
      'pendingCompletePurchase': purchaseDetails.pendingCompletePurchase,
      'error': purchaseDetails.error?.toString(),
      'verificationData': {
        'localVerificationData':
        purchaseDetails.verificationData.localVerificationData,
        'serverVerificationData':
        purchaseDetails.verificationData.serverVerificationData,
        'source': purchaseDetails.verificationData.source.toString(),
      },
    };
  }

  void _listenToPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) {
    purchaseDetailsList.forEach((PurchaseDetails purchaseDetails) async {
      if (purchaseDetails.purchaseID != null &&
          _processedPurchaseIds.contains(purchaseDetails.purchaseID)) {
        debugPrint(
            'Skipping already processed purchaseID: ${purchaseDetails.purchaseID}');
        return;
      }
      debugPrint(
          'Purchase update: ${jsonEncode(_purchaseDetailsToJson(purchaseDetails))}');
      if (purchaseDetails.status == PurchaseStatus.pending) {
        debugPrint(
            'Purchase pending for product: ${purchaseDetails.productID}');
        ref.read(purchaseStatusProvider.notifier).state =
            PurchaseStatus.pending;
        return;
      }
      if (purchaseDetails.status.name == PurchaseStatus.error.name) {
        debugPrint(
            'Purchase error for product: ${purchaseDetails.productID}, Error: ${jsonEncode({
              'error': purchaseDetails.error?.toString() ?? 'Unknown error'
            })}');
        ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
        _handlePurchaseError(purchaseDetails.error, context);
        return;
      }
      if (purchaseDetails.status.name == PurchaseStatus.purchased.name ||
          purchaseDetails.status.name == PurchaseStatus.restored.name) {
        bool valid = await _verifyPurchase(purchaseDetails);
        debugPrint(
            'Purchase verification result for product ${purchaseDetails.productID}: $valid');
        if (valid) {
          try {
            var parms = PlanRequest(
              planId: planId ?? 0,
              platform: Utils().getPlatformName(),
              store: (Utils().getPlatformName() == 'iOS' ||
                  Utils().getPlatformName() == 'macOS')
                  ? 'apple'
                  : Utils().getPlatformName() == 'Android'
                  ? 'google'
                  : '',
              receiptDataJson:
              jsonEncode(_purchaseDetailsToJson(purchaseDetails)),
            );
            debugPrint(
                'Calling purchasePlan with params: ${jsonEncode(parms.toJson())}');
            await ref.read(plansNotifierProvider.notifier).purchasePlan(parms, context!);
            debugPrint(
                'Purchase verified and plan updated for product: ${purchaseDetails.productID}');
            if (purchaseDetails.purchaseID != null) {
              _processedPurchaseIds.removeAll(_processedPurchaseIds);
              _processedPurchaseIds.add(purchaseDetails.purchaseID!);
            }
            if (!_purchasedProductIds.contains(purchaseDetails.productID)) {
              _purchasedProductIds.add(purchaseDetails.productID);
              ref.read(purchasedProductsProvider.notifier).state =
                  List.from(_purchasedProductIds);
            }
            ref.read(purchaseStatusProvider.notifier).state =
            purchaseDetails.status == PurchaseStatus.purchased
                ? PurchaseStatus.purchased
                : PurchaseStatus.restored;
            debugPrint('Current purchased products: ${jsonEncode({
              'purchasedProductIds': _purchasedProductIds
            })}');
            planId = null;
          } catch (e) {
            debugPrint(
                'Error calling purchasePlan for product: ${purchaseDetails.productID}, Error: ${jsonEncode({
                  'error': e.toString()
                })}');
            ref.read(purchaseStatusProvider.notifier).state =
                PurchaseStatus.error;
            _handlePurchaseError(e, context);
          }
        } else {
          debugPrint(
              'Purchase verification failed for product: ${purchaseDetails.productID}');
          ref.read(purchaseStatusProvider.notifier).state =
              PurchaseStatus.error;
          Utils().showFlushbar(context!,
              message: 'Purchase verification failed. Please try again.',
              isError: true);
        }
      }

      if (purchaseDetails.pendingCompletePurchase) {
        try {
          await _inAppPurchase.completePurchase(purchaseDetails);
          debugPrint(
              'Purchase completed for product: ${purchaseDetails.productID}');
        } catch (e) {
          debugPrint(
              'Error completing purchase for product: ${purchaseDetails.productID}, Error: ${jsonEncode({
                'error': e.toString()
              })}');
          ref.read(purchaseStatusProvider.notifier).state =
              PurchaseStatus.error;
          _handlePurchaseError(e, context);
        }
      }
    });
  }

  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    debugPrint('Verifying purchase for product: ${purchaseDetails.productID}');
    return purchaseDetails.status.name == PurchaseStatus.purchased.name ||
        purchaseDetails.status.name == PurchaseStatus.restored.name;
  }

  Future<void> _updatePlanStatus(PurchaseDetails purchaseDetails) async {
    final plan =
    purchaseDetails.productID == 'basic_plan' ? 'Basic' : 'Premium';
    debugPrint(
        'Plan status updated for product: ${purchaseDetails.productID}, Plan: $plan');
  }

  Future<void> restorePurchases(WidgetRef ref) async {
    ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.pending;
    try {
      await _inAppPurchase.restorePurchases();
      debugPrint('Purchases restored successfully');
      ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.restored;
      debugPrint('Restored purchased products: ${jsonEncode({
        'purchasedProductIds': _purchasedProductIds
      })}');
    } catch (e) {
      debugPrint(
          'Error restoring purchases: ${jsonEncode({'error': e.toString()})}');
      ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
      _handlePurchaseError(e, context);
    }
  }

  Future<String?> checkCurrentPlan() async {
    debugPrint('Checking current plan');
    if (_purchasedProductIds.isNotEmpty) {
      final plan = _purchasedProductIds.last == 'basic' ? 'Basic' : 'Premium';
      debugPrint('Current plan: ${jsonEncode({'plan': plan})}');
      return plan;
    }
    debugPrint('No current plan: ${jsonEncode({'plan': null})}');
    return null;
  }

  void dispose() {
    debugPrint('Disposing InAppPurchaseHelper');
    _subscription?.cancel();
    _processedPurchaseIds.clear();
  }

  void _handlePurchaseError(Object? error, BuildContext? context) {
    if (context == null) return;

    String errorMessage =
        'An unexpected error occurred. Please try again.';

    if (error is PlatformException) {
      switch (error.code) {
        case '2': // iOS: User cancelled
          errorMessage = 'Purchase cancelled by user.';
          break;
        case '4': // iOS: Cannot connect to iTunes Store
          errorMessage = 'Cannot connect to the App Store. Check your connection.';
          break;
        case '5': // iOS: Payment not authorized
          errorMessage = 'Payment not authorized. Please try a different payment method.';
          break;
        case '7': // Google Play: Item already owned
          errorMessage = 'You already own this item. Please restore your purchases.';
          break;
        case 'E_UNKNOWN': // Generic error on some platforms
          errorMessage = 'An unknown error occurred. Please try again later.';
          break;
        default:
          errorMessage = 'An error occurred during the purchase: ${error.message}';
          break;
      }
    } else if (error is String) {
      // Handle custom string errors if any
      errorMessage = error;
    }

   // Utils().showFlushbar(context, message: errorMessage, isError: true);
    debugPrint('Flushbar message: $errorMessage');
  }
}

// In-App Purchase Helper Class
// class InAppPurchaseHelper {
//   final Ref ref;
//
//   InAppPurchaseHelper(this.ref);
//
//   final InAppPurchase _inAppPurchase = InAppPurchase.instance;
//   StreamSubscription<List<PurchaseDetails>>? _subscription;
//   List<ProductDetails> _products = [];
//   List<String> _purchasedProductIds = [];
//   Set<String> _processedPurchaseIds = {}; // Track processed purchase IDs
//   bool _isAvailable = false;
//   int? planId;
//   BuildContext? context; // Nullable to handle unassigned case
//
//   // Initialize the in-app purchase system
//   Future<void> initialize() async {
//     try {
//       // Check if in-app purchase is available
//       _isAvailable = await _inAppPurchase.isAvailable();
//       if (!_isAvailable) {
//         debugPrint('In-App Purchase is not available on this device.');
//         return;
//       }
//
//       // Listen to purchase updates
//       final Stream<List<PurchaseDetails>> purchaseUpdated =
//           _inAppPurchase.purchaseStream;
//       _subscription = purchaseUpdated.listen(
//         (purchaseDetailsList) {
//           debugPrint(
//               'Received purchase update for ${purchaseDetailsList.length} items');
//           _listenToPurchaseUpdated(purchaseDetailsList);
//         },
//         onDone: () {
//           _subscription?.cancel();
//           debugPrint('Purchase stream closed');
//         },
//         onError: (error) {
//           debugPrint('Purchase stream error: ${jsonEncode({
//                 'error': error.toString()
//               })}');
//           ref.read(purchaseStatusProvider.notifier).state =
//               PurchaseStatus.error;
//         },
//       );
//
//       // Fetch product details
//       await _fetchProducts();
//     } catch (e) {
//       debugPrint('Error initializing InAppPurchase: ${jsonEncode({
//             'error': e.toString()
//           })}');
//       ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
//     }
//   }
//
//   // Fetch available products
//   Future<void> _fetchProducts() async {
//     try {
//       final ProductDetailsResponse response =
//       await _inAppPurchase.queryProductDetails(_productIds.toSet());
//       if (response.notFoundIDs.isNotEmpty) {
//         debugPrint('Products not found: ${jsonEncode({
//           'notFoundIDs': response.notFoundIDs
//         })}');
//       }
//       // Sort products to match the order of _productIds
//       _products = response.productDetails
//         ..sort((a, b) {
//           return _productIds.indexOf(a.id).compareTo(_productIds.indexOf(b.id));
//         });
//       debugPrint('Fetched products: ${jsonEncode(_products.map((p) => {
//         'id': p.id,
//         'title': p.title,
//         'description': p.description,
//         'price': p.price,
//       }).toList())}');
//     } catch (e) {
//       debugPrint(
//           'Error fetching products: ${jsonEncode({'error': e.toString()})}');
//       ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
//     }
//   }
//
//   // Get available products
//   List<ProductDetails> getProducts() => _products;
//
//   // Get purchased product IDs
//   List<String> getPurchasedProducts() => _purchasedProductIds;
//
// // Initiate purchase
//   Future<bool> buyProduct(
//     BuildContext context,
//     ProductDetails productDetails,
//     WidgetRef ref, {
//     int? planId,
//   }) async {
//     debugPrint(
//         'Initiating purchase for product: ${productDetails.id} with planId: $planId');
//     this.planId = planId; // Assign to class-level variable
//     this.context = context; // Assign to class-level variable
//     if (_products.isEmpty) {
//       debugPrint('No products available to purchase: ${jsonEncode({
//             'error': 'Check App Store Connect configuration'
//           })}');
//       ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
//       Utils().showFlushbar(context,
//           message: 'No products available. Please try again later.',
//           isError: true);
//       return false;
//     }
//     ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.pending;
//
//     final PurchaseParam purchaseParam = PurchaseParam(productDetails: productDetails);
//     try {
//        //_inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam)
//       bool isSuccess = await _inAppPurchase.buyConsumable(purchaseParam: purchaseParam,autoConsume: true);
//       debugPrint(
//           'Purchase request sent for product: ${productDetails.id}, Success: $isSuccess');
//       return isSuccess;
//     } catch (e) {
//       debugPrint(
//           'Error initiating purchase: ${jsonEncode({'error': e.toString()})}');
//       ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
//       Utils().showFlushbar(context,
//           message: 'Error initiating purchase: ${e.toString()} ',
//           isError: true);
//       return false;
//     }
//   }
//
//   // Convert PurchaseDetails to JSON
//   Map<String, dynamic> _purchaseDetailsToJson(PurchaseDetails purchaseDetails) {
//     return {
//       'productID': purchaseDetails.productID,
//       'purchaseID': purchaseDetails.purchaseID,
//       'status': purchaseDetails.status.toString(),
//       'transactionDate': purchaseDetails.transactionDate,
//       'pendingCompletePurchase': purchaseDetails.pendingCompletePurchase,
//       'error': purchaseDetails.error?.toString(),
//       'verificationData': {
//         'localVerificationData':
//             purchaseDetails.verificationData.localVerificationData,
//         'serverVerificationData':
//             purchaseDetails.verificationData.serverVerificationData,
//         'source': purchaseDetails.verificationData.source.toString(),
//       },
//     };
//   }
//
//   // Handle purchase updates
//   void _listenToPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) {
//     purchaseDetailsList.forEach((PurchaseDetails purchaseDetails) async {
//       // Skip if purchaseID has already been processed
//       if (purchaseDetails.purchaseID != null &&
//           _processedPurchaseIds.contains(purchaseDetails.purchaseID)) {
//         debugPrint(
//             'Skipping already processed purchaseID: ${purchaseDetails.purchaseID}');
//         return;
//       }
//
//       // Print PurchaseDetails in JSON format
//       debugPrint(
//           'Purchase update: ${jsonEncode(_purchaseDetailsToJson(purchaseDetails))}');
//
//       if (purchaseDetails.status == PurchaseStatus.pending) {
//         debugPrint(
//             'Purchase pending for product: ${purchaseDetails.productID}');
//         ref.read(purchaseStatusProvider.notifier).state =
//             PurchaseStatus.pending;
//         return;
//       }
//
//       if (purchaseDetails.status.name == PurchaseStatus.error.name) {
//         debugPrint(
//             'Purchase error for product: ${purchaseDetails.productID}, Error: ${jsonEncode({
//               'error': purchaseDetails.error?.toString() ?? 'Unknown error'
//             })}');
//         ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
//         return;
//       }
//
//       if (purchaseDetails.status.name == PurchaseStatus.purchased.name || purchaseDetails.status.name == PurchaseStatus.restored.name) {
//         bool valid = await _verifyPurchase(purchaseDetails);
//         debugPrint(
//             'Purchase verification result for product ${purchaseDetails.productID}: $valid');
//         if (valid) {
//           try {
//             var parms = PlanRequest(
//               planId: planId ?? 0, // Use 0 as fallback if planId is null
//               platform: Utils().getPlatformName(),
//               store: (Utils().getPlatformName() == 'iOS' ||
//                       Utils().getPlatformName() == 'macOS')
//                   ? 'apple'
//                   : Utils().getPlatformName() == 'Android'
//                       ? 'google'
//                       : '',
//               receiptDataJson:
//                   jsonEncode(_purchaseDetailsToJson(purchaseDetails)),
//             );
//             debugPrint(
//                 'Calling purchasePlan with params: ${jsonEncode(parms.toJson())}');
//             await ref.read(plansNotifierProvider.notifier).purchasePlan(parms,context!);
//             debugPrint(
//                 'Purchase verified and plan updated for product: ${purchaseDetails.productID}');
//             // Add purchaseID to processed set
//             if (purchaseDetails.purchaseID != null) {
//               _processedPurchaseIds.add(purchaseDetails.purchaseID!);
//             }
//             // Add to purchased products list if not already present
//             if (!_purchasedProductIds.contains(purchaseDetails.productID)) {
//               _purchasedProductIds.add(purchaseDetails.productID);
//               ref.read(purchasedProductsProvider.notifier).state =
//                   List.from(_purchasedProductIds);
//             }
//             ref.read(purchaseStatusProvider.notifier).state =
//                 purchaseDetails.status == PurchaseStatus.purchased
//                     ? PurchaseStatus.purchased
//                     : PurchaseStatus.restored;
//             // Print the purchased products list in JSON
//             debugPrint('Current purchased products: ${jsonEncode({
//                   'purchasedProductIds': _purchasedProductIds
//                 })}');
//             // Reset planId after processing
//             planId = null;
//           } catch (e) {
//             debugPrint(
//                 'Error calling purchasePlan for product: ${purchaseDetails.productID}, Error: ${jsonEncode({
//                   'error': e.toString()
//                 })}');
//             ref.read(purchaseStatusProvider.notifier).state =
//                 PurchaseStatus.error;
//           }
//         } else {
//           debugPrint(
//               'Purchase verification failed for product: ${purchaseDetails.productID}');
//           ref.read(purchaseStatusProvider.notifier).state =
//               PurchaseStatus.error;
//         }
//       }
//
//       if (purchaseDetails.pendingCompletePurchase) {
//         try {
//           await _inAppPurchase.completePurchase(purchaseDetails);
//
//           debugPrint(
//               'Purchase completed for product: ${purchaseDetails.productID}');
//         } catch (e) {
//           debugPrint(
//               'Error completing purchase for product: ${purchaseDetails.productID}, Error: ${jsonEncode({
//                 'error': e.toString()
//               })}');
//           ref.read(purchaseStatusProvider.notifier).state =
//               PurchaseStatus.error;
//         }
//       }
//     });
//   }
//
//   // Verify purchase
//   Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
//     debugPrint('Verifying purchase for product: ${purchaseDetails.productID}');
//     // Implement actual verification logic here, e.g., server-side validation
//     return purchaseDetails.status.name == PurchaseStatus.purchased.name ||
//         purchaseDetails.status.name == PurchaseStatus.restored.name;
//   }
//
//   // Update plan status
//   Future<void> _updatePlanStatus(PurchaseDetails purchaseDetails) async {
//     final plan =
//         purchaseDetails.productID == 'basic_plan' ? 'Basic' : 'Premium';
//     debugPrint(
//         'Plan status updated for product: ${purchaseDetails.productID}, Plan: $plan');
//   }
//
//   // Restore purchases
//   Future<void> restorePurchases(WidgetRef ref) async {
//     ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.pending;
//     try {
//       await _inAppPurchase.restorePurchases();
//       debugPrint('Purchases restored successfully');
//       ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.restored;
//       // Print the purchased products list in JSON
//       debugPrint('Restored purchased products: ${jsonEncode({
//             'purchasedProductIds': _purchasedProductIds
//           })}');
//     } catch (e) {
//       debugPrint(
//           'Error restoring purchases: ${jsonEncode({'error': e.toString()})}');
//       ref.read(purchaseStatusProvider.notifier).state = PurchaseStatus.error;
//     }
//   }
//
//   // Check current plan
//   Future<String?> checkCurrentPlan() async {
//     debugPrint('Checking current plan');
//     if (_purchasedProductIds.isNotEmpty) {
//       final plan = _purchasedProductIds.last == 'basic' ? 'Basic' : 'Premium';
//       debugPrint('Current plan: ${jsonEncode({'plan': plan})}');
//       return plan;
//     }
//     debugPrint('No current plan: ${jsonEncode({'plan': null})}');
//     return null;
//   }
//
//   // Dispose
//   void dispose() {
//     debugPrint('Disposing InAppPurchaseHelper');
//     _subscription?.cancel();
//     _processedPurchaseIds.clear(); // Clear processed IDs on dispose
//   }
// }
