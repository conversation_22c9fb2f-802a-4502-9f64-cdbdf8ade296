import 'package:intl/intl.dart';

class DateFormatter {

  static String formatFullDate(DateTime date) {
    return DateFormat.yMMMMd().format(date);
  }

  static String formatShortDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  static String formatMonthDay(DateTime date) {
    return DateFormat.MMMd().format(date);
  }

  static String timeAgo(DateTime date) {
    final now = DateTime.now();
    final diff = now.difference(date);

    if (diff.inSeconds < 60) return 'just now';
    if (diff.inMinutes < 60) return '${diff.inMinutes} min ago';
    if (diff.inHours < 24) return '${diff.inHours} hr ago';
    if (diff.inDays < 7) return '${diff.inDays} day${diff.inDays > 1 ? 's' : ''} ago';
    if (diff.inDays < 30) return '${(diff.inDays / 7).floor()} week${diff.inDays ~/ 7 > 1 ? 's' : ''} ago';
    if (diff.inDays < 365) return '${(diff.inDays / 30).floor()} month${diff.inDays ~/ 30 > 1 ? 's' : ''} ago';
    return '${(diff.inDays / 365).floor()} year${diff.inDays ~/ 365 > 1 ? 's' : ''} ago';
  }

  /// Convert UTC string to local DateTime
  static DateTime parseUtcToLocal(String utcString) {
    try {
      final utcDateTime = DateTime.parse(utcString);
      return utcDateTime.toLocal();
    } catch (e) {
      return DateTime.now(); // Fallback to current time
    }
  }

  /// Get time period category for grouping threads
  static String getTimePeriod(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final sevenDaysAgo = today.subtract(const Duration(days: 7));
    final thirtyDaysAgo = today.subtract(const Duration(days: 30));

    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly.isAtSameMomentAs(today)) {
      return 'Today';
    } else if (dateOnly.isAtSameMomentAs(yesterday)) {
      return 'Yesterday';
    } else if (dateOnly.isAfter(sevenDaysAgo)) {
      return 'Previous 7 days';
    } else if (dateOnly.isAfter(thirtyDaysAgo)) {
      return 'Previous 30 days';
    } else {
      return 'Older';
    }
  }
}
