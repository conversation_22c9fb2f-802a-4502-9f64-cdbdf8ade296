import 'package:permission_handler/permission_handler.dart';

class PermissionHelper {
  /// Request a single permission.
  /// If permanently denied → opens App Settings.
  /// For photos permission, accepts both granted and limited access.
  static Future<bool> request(Permission permission) async {
    final status = await permission.request();

    // For photos permission, accept both granted and limited access
    if (permission == Permission.photos) {
      if (status.isGranted || status.isLimited) {
        return true;
      }
    } else {
      // For other permissions, only accept granted
      if (status.isGranted) {
        return true;
      }
    }

    if (status.isPermanentlyDenied) {
      // Open settings when permanently denied
      await openAppSettings();
    }
    return false;
  }

  /// Request multiple permissions.
  /// Returns `true` if **all** are granted (or limited for photos).
  /// If any is permanently denied → opens App Settings.
  static Future<bool> requestMultiple(List<Permission> permissions) async {
    final statuses = await permissions.request();

    bool allGranted = true;
    for (final entry in statuses.entries) {
      final permission = entry.key;
      final status = entry.value;

      // For photos permission, accept both granted and limited access
      if (permission == Permission.photos) {
        if (!status.isGranted && !status.isLimited) {
          allGranted = false;
          break;
        }
      } else {
        // For other permissions, only accept granted
        if (!status.isGranted) {
          allGranted = false;
          break;
        }
      }
    }

    if (!allGranted) {
      // If any is permanently denied → settings
      if (statuses.values.any((status) => status.isPermanentlyDenied)) {
        await openAppSettings();
      }
    }

    return allGranted;
  }

  /// Check if a permission is considered valid.
  /// For photos permission, accepts both granted and limited access.
  /// For other permissions, only accepts granted.
  static bool isPermissionValid(Permission permission, PermissionStatus status) {
    if (permission == Permission.photos) {
      return status.isGranted || status.isLimited;
    } else {
      return status.isGranted;
    }
  }

  /// Get current status of a permission and check if it's valid.
  static Future<bool> isPermissionGranted(Permission permission) async {
    final status = await permission.status;
    return isPermissionValid(permission, status);
  }
}
