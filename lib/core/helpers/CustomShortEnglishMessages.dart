import 'package:timeago/timeago.dart' as timeago;

class CustomShortEnglishMessages implements timeago.LookupMessages {
  @override String prefixAgo() => '';
  @override String prefixFromNow() => '';
  @override String suffixAgo() => 'ago';
  @override String suffixFromNow() => 'from now';
  @override String lessThanOneMinute(int seconds) => 'just now';
  @override String aboutAMinute(int minutes) => '1 min';
  @override String minutes(int minutes) => '$minutes min';  // Changed from 'm' to 'min'
  @override String aboutAnHour(int minutes) => '1 hr';
  @override String hours(int hours) => '$hours hr';
  @override String aDay(int hours) => '1 day';
  @override String days(int days) => '$days days';
  @override String aboutAMonth(int days) => '1 mo';
  @override String months(int months) => '$months mo';
  @override String aboutAYear(int year) => '1 yr';
  @override String years(int years) => '$years yr';
  @override String wordSeparator() => ' ';
}