import '../data/models/get_ask_ai_response.dart';
import 'date_formatter.dart';

class ThreadSection {
  final String title;
  final List<Thread> threads;

  ThreadSection({
    required this.title,
    required this.threads,
  });
}

class ThreadGrouper {
  /// Group threads by time periods (Today, Yesterday, Previous 7 days, etc.)
  static List<ThreadSection> groupThreadsByTimePeriod(List<Thread> threads) {
    if (threads.isEmpty) return [];

    // Convert UTC strings to local DateTime and sort by date (newest first)
    final threadsWithLocalDates = threads.map((thread) {
      final localDate = DateFormatter.parseUtcToLocal(thread.createdAtUtc);
      return MapEntry(thread, localDate);
    }).toList();

    threadsWithLocalDates.sort((a, b) => b.value.compareTo(a.value));

    // Group threads by time period
    final Map<String, List<Thread>> groupedThreads = {};
    
    for (final entry in threadsWithLocalDates) {
      final thread = entry.key;
      final localDate = entry.value;
      final period = DateFormatter.getTimePeriod(localDate);
      
      if (!groupedThreads.containsKey(period)) {
        groupedThreads[period] = [];
      }
      groupedThreads[period]!.add(thread);
    }

    // Convert to ThreadSection list with proper ordering
    final List<ThreadSection> sections = [];
    final orderedPeriods = [
      'Today',
      'Yesterday', 
      'Previous 7 days',
      'Previous 30 days',
      'Older'
    ];

    for (final period in orderedPeriods) {
      if (groupedThreads.containsKey(period) && groupedThreads[period]!.isNotEmpty) {
        sections.add(ThreadSection(
          title: period,
          threads: groupedThreads[period]!,
        ));
      }
    }

    return sections;
  }

  /// Calculate total item count for ListView (sections + threads)
  static int calculateTotalItemCount(List<ThreadSection> sections) {
    return sections.fold(0, (count, section) => count + 1 + section.threads.length);
  }

  /// Get the item at a specific index in the flattened list
  static dynamic getItemAtIndex(List<ThreadSection> sections, int index) {
    int currentIndex = 0;
    
    for (final section in sections) {
      // Check if this index is the section header
      if (currentIndex == index) {
        return section; // Return the section for header
      }
      currentIndex++;
      
      // Check if this index is within the section's threads
      if (index < currentIndex + section.threads.length) {
        final threadIndex = index - currentIndex;
        return section.threads[threadIndex];
      }
      currentIndex += section.threads.length;
    }
    
    return null; // Index out of bounds
  }
}
