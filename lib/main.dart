import 'dart:io';

import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/routes/routes.dart';
import 'package:mastercookai/app/theme/light_theme.dart';
import 'package:mastercookai/core/helpers/CustomShortEnglishMessages.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/presentation/start_up_error_screen.dart';

import 'package:timeago/timeago.dart' as timeago;
import 'package:window_size/window_size.dart';
import 'app/imports/packages_imports.dart';
import 'core/helpers/app_constant.dart';
import 'core/network/app_repository.dart';
import 'package:flutter/material.dart';
import 'core/utils/device_utils.dart';

final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

final providerContainer = ProviderContainer(observers: []);

void main() async {
  timeago.setLocaleMessages('en_short', CustomShortEnglishMessages());
  WidgetsFlutterBinding.ensureInitialized();

  // Set window size for desktop platforms
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    setWindowMinSize(const Size(1360, 980)); // Minimum size
    // setWindowMaxSize(const Size(1920, 1080)); // (Optional) Maximum size
    setWindowFrame(const Rect.fromLTWH(100, 60, 1360, 980));
  }
  baseURL = "https://api.mastercook.ai/";
  await ScreenUtil.ensureScreenSize();
  final hasConnection = await providerContainer
      .read(appRepositoryProvider)
      .hasInternetConnection();
  if (!hasConnection) {
    runApp(
      UncontrolledProviderScope(
        container: providerContainer,
        child: const StartUpErrorScreen(),
      ),
    );
    return;
  }
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]).then((_) {
    runApp(ProviderScope(child: MyApp()));
  });
}

Size getFixedDesktopDesignSize(BuildContext context) {
  final width = MediaQuery.of(context).size.width;
  if (width > 2560) return const Size(2560, 1440);
  if (width > 1920) return const Size(1920, 1080);
  return const Size(1440, 900);
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(goRouterProvider);
    ScreenUtil.init(context, minTextAdapt: true, splitScreenMode: true);
    return ScreenUtilInit(
      designSize: const Size(2500, 1600),
      minTextAdapt: true,
      splitScreenMode: true,
      child: GestureDetector(
        onTap: () {
          Utils().hideKeyboardOnTap();
        },
        behavior: HitTestBehavior.opaque,
        child: Container(
          // decoration: const BoxDecoration(
          //   image: DecorationImage(
          //     image: AssetImage(AssetsManager.background_img),
          //     fit: BoxFit.cover,
          //   ),
          // ),
          color: getDeviceType(context).name == 'mobile'
              ? Colors.white
              : Colors.transparent,
          // decoration: const BoxDecoration(
          //   image: DecorationImage(
          //     image: AssetImage(AssetsManager.background_img),
          //     fit: BoxFit.cover,
          //   ),
          // ),
          child: MaterialApp.router(
            key: navigatorKey,
            builder: (context, child) {
              // Wrap the child with FlutterSmartDialog and another GestureDetector for dialogs
              return FlutterSmartDialog.init()(
                context,
                GestureDetector(
                  onTap: () {
                    debugPrint("Tapped inside dialog - dismissing keyboard");
                    Utils().hideKeyboardOnTap();
                  },
                  behavior: HitTestBehavior.opaque,
                  child: child,
                ),
              );
            },
            scaffoldMessengerKey: scaffoldMessengerKey,
            title: 'MasterCook AI',
            debugShowCheckedModeBanner: false,
            supportedLocales: const [Locale('en')],
            theme: lightTheme,
            routerConfig: router,
          ),
        ),
      ),
    );
  }
}
