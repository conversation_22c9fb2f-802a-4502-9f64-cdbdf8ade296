
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


import '../../core/utils/device_utils.dart';
import '../imports/core_imports.dart';
import '../text_styles.dart';


final ThemeData lightTheme = ThemeData.light().copyWith(
  brightness: Brightness.light,
  primaryColor: AppColors.primaryColor,
  scaffoldBackgroundColor: AppColors.lightPrimaryBackgroundColor,
  indicatorColor: AppColors.primaryBorderColor,
  cardColor: AppColors.secondaryColor,

  hintColor: AppColors.primaryGreyColor,
  appBarTheme: AppBarTheme(
    backgroundColor: AppColors.lightPrimaryBackgroundColor,
    surfaceTintColor: AppColors.lightPrimaryBackgroundColor,
    shadowColor: AppColors.secondaryColor,
    elevation: 0,
    systemOverlayStyle: const SystemUiOverlayStyle(
      statusBarColor: AppColors.lightPrimaryBackgroundColor,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
    titleTextStyle: globalTextStyle(
      fontSize: headingSixFontSize,
      color: AppColors.primaryGreyColor,
      fontWeight: FontWeight.w600,
    ),
    iconTheme: IconThemeData(
      color: AppColors.primaryGreyColor,
    ),
  ),
  bottomNavigationBarTheme: BottomNavigationBarThemeData(
    backgroundColor: AppColors.primaryColor,
    selectedItemColor: AppColors.secondaryColor,
    unselectedItemColor: AppColors.lightPrimaryBackgroundColor,
    type: BottomNavigationBarType.fixed,
    showSelectedLabels: false,
    showUnselectedLabels: false,
    selectedLabelStyle: const TextStyle(fontSize: 0),
    unselectedLabelStyle: const TextStyle(fontSize: 0),
  ),
  floatingActionButtonTheme: const FloatingActionButtonThemeData(
    backgroundColor: AppColors.primaryColor,
    elevation: 0,
  ),
  iconTheme: IconThemeData(
    color: AppColors.primaryColor,
    size: responsiveFont(20).sp,
  ),
  textTheme: TextTheme(
      bodyMedium: globalTextStyle(
        fontSize: responsiveFont(headingLargeFontSize),
        color: AppColors.primaryLightTextColor,
        fontWeight: FontWeight.w600,
      ),
    bodySmall: globalTextStyle(
      fontSize: headingLargeFontSize,
      color: AppColors.primaryLightTextColor,
      fontWeight: FontWeight.w600,
    ),
    titleLarge: globalTextStyle(
      fontSize: responsiveFont(headingOneFontSize),
      color: AppColors.primaryGreyColor,
      fontWeight: FontWeight.w600,
    ),
    titleMedium: globalTextStyle(
      fontSize: responsiveFont(headingFourFontSize),
      color: AppColors.lightPrimaryBackgroundColor,
      fontWeight: FontWeight.w500,
    ),
    titleSmall: globalTextStyle(
      fontSize: headingThreeFontSize,
      color: AppColors.primaryGreyColor,
      fontWeight: FontWeight.w600,
    ),

    labelLarge: globalTextStyle(
      fontSize: headingFourFontSize,
      color: AppColors.primaryGreyColor,
      fontWeight: FontWeight.w600,
    ),
    labelMedium: globalTextStyle(
      fontSize: responsiveFont(headingSixFontSize).sp,
      color: AppColors.primaryGreyColor,
      fontWeight: FontWeight.w600,
    ),
    labelSmall: globalTextStyle(
      fontSize: headingTwoFontSize,
      color: AppColors.backgroudInActiveColor,
      fontWeight: FontWeight.w600,
    ),

    displaySmall: globalTextStyle(
        fontSize: headingFourFontSize,
      color: AppColors.backgroudInActiveColor,
      fontWeight: FontWeight.w400,
    )

  ),
  inputDecorationTheme: InputDecorationTheme(
    filled: true,
    fillColor: AppColors.lightPrimaryBackgroundColor,
    hintStyle: globalTextStyle(
      fontSize: responsiveFont(headingSixFontSize).sp,
      color: AppColors.textGreyColor,
      fontWeight: FontWeight.w400,
    ),
    errorStyle: globalTextStyle(
      fontSize: headingSixFontSize,
      color: AppColors.errorColor,
      fontWeight: FontWeight.w400,
    ),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide:  BorderSide(
        color: AppColors.selectionColor,
        width: 10.w
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide:  BorderSide(
        color: AppColors.selectionColor,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(
        color: Colors.transparent,
      ),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(
        color: AppColors.errorColor,
      ),
    ),
  ),
  tabBarTheme: TabBarThemeData(
    labelStyle: globalTextStyle(
      fontSize: 12.sp,
      color: AppColors.primaryColor,
      fontWeight: FontWeight.w600,
    ),
    unselectedLabelStyle: globalTextStyle(
      fontSize: 12.sp,
      color: const Color(0xFF868590),
      fontWeight: FontWeight.w600,
    ),
    labelColor: AppColors.primaryColor,
    unselectedLabelColor: const Color(0xFF8E8E91),
    labelPadding: const EdgeInsets.symmetric(vertical: 6),
    indicator: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(10),
    ),
  ),
);
