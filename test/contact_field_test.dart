import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:country_pickers/country.dart';
import 'package:country_pickers/utils/utils.dart';
import 'package:mastercookai/presentation/profile/mobileui/sub_views/personal_details_section.dart';

void main() {
  group('PersonalDetailsSection Contact Field Tests', () {
    testWidgets('should render ContactTextField when provided with required parameters', (WidgetTester tester) async {
      // Create controllers and state variables
      final firstNameController = TextEditingController();
      final lastNameController = TextEditingController();
      final emailController = TextEditingController();
      final contactController = TextEditingController();
      final selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PersonalDetailsSection(
                firstNameController: firstName<PERSON>ontroller,
                lastNameController: lastName<PERSON>ontroller,
                emailController: emailController,
                contactController: contactController,
                selectedGender: 0,
                selectedDay: null,
                selectedMonth: null,
                selectedYear: null,
                selectedCountry: selectedCountry,
                onGenderChanged: (value) {},
                onDayChanged: (value) {},
                onMonthChanged: (value) {},
                onYearChanged: (value) {},
                onCountryChanged: (country) {},
              ),
            ),
          ),
        ),
      );

      // Verify the section renders
      expect(find.byType(PersonalDetailsSection), findsOneWidget);
      
      // Verify the Contact label is present
      expect(find.text('Contact'), findsOneWidget);
      
      // Verify ContactTextField is rendered (it should contain a phone input field)
      expect(find.byType(TextFormField), findsWidgets);
      
      // Verify country code is displayed
      expect(find.text('+1'), findsOneWidget);
      
      // Verify no render overflow errors
      expect(tester.takeException(), isNull);
      
      // Clean up controllers
      firstNameController.dispose();
      lastNameController.dispose();
      emailController.dispose();
      contactController.dispose();
    });

    testWidgets('should handle country picker interaction', (WidgetTester tester) async {
      final contactController = TextEditingController();
      final selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
      bool countryChanged = false;
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PersonalDetailsSection(
                firstNameController: TextEditingController(),
                lastNameController: TextEditingController(),
                emailController: TextEditingController(),
                contactController: contactController,
                selectedGender: 0,
                selectedDay: null,
                selectedMonth: null,
                selectedYear: null,
                selectedCountry: selectedCountry,
                onGenderChanged: (value) {},
                onDayChanged: (value) {},
                onMonthChanged: (value) {},
                onYearChanged: (value) {},
                onCountryChanged: (country) {
                  countryChanged = true;
                },
              ),
            ),
          ),
        ),
      );

      // Find and tap the country picker (should be a GestureDetector with the flag and country code)
      final countryPickerFinder = find.text('+1');
      expect(countryPickerFinder, findsOneWidget);
      
      // Verify no exceptions during rendering
      expect(tester.takeException(), isNull);
      
      contactController.dispose();
    });
  });
}
