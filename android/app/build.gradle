

plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}
def keystorePropertiesFile = rootProject.file("key.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "2"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.1"
}


android {
    namespace = "com.app.mastercookai"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.app.mastercookai"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }


    signingConfigs {
        release {
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
        }
        debug {
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }


}
afterEvaluate {
    tasks.matching { it.name.startsWith("assemble") }.all { task ->
        task.doLast {
            def buildType = task.name.replace("assemble", "").toLowerCase()
            def apkDir = "${buildDir}/outputs/flutter-apk"
            def apkName = "app-${buildType}.apk"   // Flutter expects this
//            def projectName = project.name.replace(" ", "-")
//            def versionName = android.defaultConfig.versionName
//            def versionCode = android.defaultConfig.versionCode
            def newName ="mastercookai.apk"// "${projectName}-${buildType}-v${versionName}(${versionCode}).apk"

            def apkFile = file("${apkDir}/${apkName}")
            if (apkFile.exists()) {
                def newFile = file("${apkDir}/${newName}")
                println("✅ Copying APK to ${newFile}")
                ant.copy(file: apkFile, tofile: newFile, overwrite: true)
            } else {
                println("⚠️ APK not found: ${apkFile}")
            }
        }
    }
}



flutter {
    source = "../.."
}
