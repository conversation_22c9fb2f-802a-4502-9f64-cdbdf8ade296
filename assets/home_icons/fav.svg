<svg width="89" height="89" viewBox="0 0 89 89" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="88" height="88" rx="16" fill="url(#paint0_linear_839_2242)"/>
<rect x="0.5" y="0.5" width="87" height="87" rx="15.5" stroke="url(#paint1_linear_839_2242)" stroke-opacity="0.4"/>
<path opacity="0.5" d="M88.8257 52.9116L88.8851 67.6017C88.8851 79.0123 79.6361 88.2613 68.2256 88.2613H44.5926L26.6624 70.331L44.7586 15.1738L65.8393 36.2545L69.5845 33.6124L88.1268 52.2127L88.8257 52.9116Z" fill="url(#paint2_linear_839_2242)"/>
<g filter="url(#filter0_i_839_2242)">
<path d="M59.5927 68.2871C59.3095 68.2871 59.0334 68.2174 58.7716 68.0798L44.5975 60.6282C44.2377 60.439 43.8323 60.3388 43.4257 60.3388C43.0191 60.3388 42.6137 60.439 42.2539 60.6282L28.0798 68.0798C27.8183 68.2174 27.542 68.2871 27.2587 68.2871C26.7469 68.2873 26.24 68.0484 25.9026 67.6482C25.5669 67.25 25.4313 66.7431 25.5209 66.2206L28.2278 50.4377C28.368 49.6208 28.0971 48.7875 27.5037 48.2087L16.0367 37.0313C15.545 36.5521 15.3781 35.8762 15.5903 35.2234C15.8024 34.5706 16.3347 34.1218 17.0139 34.0231L32.8609 31.7204C33.6813 31.6012 34.3902 31.0863 34.757 30.3429L41.844 15.9831C42.1478 15.3675 42.739 15 43.4255 15C44.1121 15 44.7034 15.3675 45.007 15.9831L52.0939 30.3429C52.4608 31.0864 53.1698 31.6014 53.9902 31.7204L69.8372 34.0231C70.5164 34.1218 71.0487 34.5706 71.2608 35.2234C71.473 35.8762 71.3061 36.5521 70.8145 37.0313L59.3474 48.2087C58.754 48.7876 58.4831 49.6208 58.6234 50.4379L61.3302 66.2206C61.42 66.7429 61.2842 67.25 60.9485 67.6481C60.6114 68.0482 60.1045 68.2871 59.5927 68.2871Z" fill="url(#paint3_linear_839_2242)"/>
</g>
<defs>
<filter id="filter0_i_839_2242" x="15.5" y="15" width="57.8511" height="55.2871" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_839_2242"/>
</filter>
<linearGradient id="paint0_linear_839_2242" x1="44" y1="0" x2="44" y2="88" gradientUnits="userSpaceOnUse">
<stop stop-color="#6EAFFD"/>
<stop offset="1" stop-color="#1B3594"/>
</linearGradient>
<linearGradient id="paint1_linear_839_2242" x1="44" y1="0" x2="44" y2="88" gradientUnits="userSpaceOnUse">
<stop stop-color="#1D3896"/>
<stop offset="1" stop-color="#6CACFB"/>
</linearGradient>
<linearGradient id="paint2_linear_839_2242" x1="32.158" y1="36.0063" x2="77.8841" y2="81.7324" gradientUnits="userSpaceOnUse">
<stop stop-color="#212385"/>
<stop offset="1" stop-color="#001E91" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_839_2242" x1="43.4255" y1="13.6688" x2="43.4255" y2="67.8226" gradientUnits="userSpaceOnUse">
<stop offset="0.005" stop-color="#FDE97E"/>
<stop offset="1" stop-color="#FBB728"/>
</linearGradient>
</defs>
</svg>
