<svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="88" height="88" rx="16" fill="url(#paint0_radial_839_2240)"/>
<g filter="url(#filter0_di_839_2240)">
<path d="M67.4211 33.9482C67.0789 33.6015 66.6717 33.3265 66.2231 33.1392C65.7745 32.952 65.2933 32.8561 64.8077 32.8571H57.4231V31C57.4231 27.5522 56.0615 24.2456 53.638 21.8076C51.2145 19.3696 47.9274 18 44.5 18C41.0725 18 37.7856 19.3696 35.362 21.8076C32.9385 24.2456 31.5769 27.5522 31.5769 31V32.8571H24.1923C23.213 32.8571 22.2739 33.2484 21.5815 33.9451C20.889 34.6416 20.5 35.5863 20.5 36.5714V61.6429C20.5 66.1696 24.3077 70 28.8077 70H60.1923C62.3689 70.0007 64.4596 69.1446 66.0169 67.6147C66.8006 66.8626 67.4247 65.9585 67.8514 64.957C68.2782 63.9556 68.4988 62.8775 68.5 61.7879V36.5714C68.5015 36.0842 68.4069 35.6014 68.2217 35.1512C68.0365 34.7008 67.7644 34.292 67.4211 33.9482ZM35.2692 31C35.2692 28.5373 36.2417 26.1754 37.9728 24.434C39.7039 22.6926 42.0518 21.7143 44.5 21.7143C46.9481 21.7143 49.296 22.6926 51.0271 24.434C52.7582 26.1754 53.7308 28.5373 53.7308 31V32.8571H35.2692V31ZM57.4231 42.1429C57.4231 45.5906 56.0615 48.8973 53.638 51.3352C51.2145 53.7732 47.9274 55.1429 44.5 55.1429C41.0725 55.1429 37.7856 53.7732 35.362 51.3352C32.9385 48.8973 31.5769 45.5906 31.5769 42.1429V40.2857C31.5769 39.7932 31.7714 39.3208 32.1176 38.9725C32.4639 38.6243 32.9334 38.4286 33.4231 38.4286C33.9127 38.4286 34.3823 38.6243 34.7284 38.9725C35.0747 39.3208 35.2692 39.7932 35.2692 40.2857V42.1429C35.2692 44.6055 36.2417 46.9675 37.9728 48.7089C39.7039 50.4502 42.0518 51.4286 44.5 51.4286C46.9481 51.4286 49.296 50.4502 51.0271 48.7089C52.7582 46.9675 53.7308 44.6055 53.7308 42.1429V40.2857C53.7308 39.7932 53.9252 39.3208 54.2715 38.9725C54.6177 38.6243 55.0872 38.4286 55.5769 38.4286C56.0665 38.4286 56.5361 38.6243 56.8823 38.9725C57.2285 39.3208 57.4231 39.7932 57.4231 40.2857V42.1429Z" fill="url(#paint1_linear_839_2240)" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_di_839_2240" x="17.5" y="16" width="56" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_839_2240"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_839_2240" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_839_2240"/>
</filter>
<radialGradient id="paint0_radial_839_2240" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(70 75) rotate(-130.456) scale(107.11)">
<stop stop-color="#40AA2F"/>
<stop offset="1" stop-color="#89F384"/>
</radialGradient>
<linearGradient id="paint1_linear_839_2240" x1="55.5" y1="21.5" x2="20.5" y2="81" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFF9F4" stop-opacity="0.81"/>
</linearGradient>
</defs>
</svg>
