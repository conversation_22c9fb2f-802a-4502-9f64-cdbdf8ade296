<svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="88" height="88" rx="16" fill="url(#paint0_linear_839_2113)"/>
<g filter="url(#filter0_d_839_2113)">
<g filter="url(#filter1_i_839_2113)">
<path d="M29.6998 17C25.635 17 22.3398 20.2968 22.3398 24.3636V63.6364C22.3398 67.7033 25.635 71 29.6998 71H59.1398C63.2048 71 66.4998 67.7033 66.4998 63.6364V24.3636C66.4998 20.2968 63.2048 17 59.1398 17H29.6998Z" fill="#FFFEE3"/>
</g>
<g filter="url(#filter2_i_839_2113)">
<rect x="18.5" y="32.4277" width="9.6" height="3.85714" rx="1.92857" fill="#F8E223"/>
<rect x="18.5" y="42.0703" width="9.6" height="3.85714" rx="1.92857" fill="#F8E223"/>
<rect x="18.5" y="51.7129" width="9.6" height="3.85714" rx="1.92857" fill="#F8E223"/>
</g>
<g filter="url(#filter3_i_839_2113)">
<path d="M34.9038 45.0725C35.3204 45.45 35.651 45.9132 35.8732 46.4306C36.0954 46.948 36.204 47.5075 36.1915 48.0708V49.7832H52.6469V48.0708C52.6342 47.5075 52.7426 46.9481 52.9647 46.4307C53.1868 45.9133 53.5173 45.45 53.9337 45.0725C54.7063 44.4069 55.2891 43.5472 55.6226 42.5813C55.9561 41.6154 56.0284 40.5778 55.832 39.5746C55.6356 38.5715 55.1776 37.6387 54.5049 36.8715C53.8321 36.1043 52.9686 35.5303 52.0027 35.2081C51.1031 34.901 50.1408 34.8271 49.2052 34.9932C48.6794 34.2038 47.968 33.5567 47.1339 33.1093C46.2999 32.6618 45.3689 32.4277 44.4233 32.4277C43.4777 32.4277 42.5467 32.6618 41.7127 33.1093C40.8786 33.5567 40.1672 34.2038 39.6413 34.9932C38.7057 34.8271 37.7435 34.901 36.8439 35.2081C35.8773 35.5292 35.0129 36.1025 34.3392 36.8692C33.6654 37.6359 33.2064 38.5685 33.009 39.5718C32.8117 40.5751 32.8832 41.613 33.2161 42.5795C33.549 43.5459 34.1314 44.4062 34.9038 45.0725ZM47.7267 42.1799C47.7757 41.9714 47.9034 41.7901 48.0829 41.6744C48.2624 41.5586 48.4797 41.5174 48.6888 41.5594C48.898 41.6014 49.0827 41.7234 49.2041 41.8996C49.3255 42.0758 49.374 42.2925 49.3393 42.5039L48.5166 46.6361C48.48 46.824 48.3796 46.9933 48.2325 47.1151C48.0855 47.2369 47.9009 47.3036 47.7103 47.3039C47.655 47.3043 47.5998 47.2987 47.5457 47.2874C47.4398 47.2657 47.3392 47.2232 47.2496 47.1625C47.1601 47.1017 47.0833 47.0239 47.0237 46.9333C46.9641 46.8427 46.9228 46.7413 46.9023 46.6347C46.8818 46.5281 46.8823 46.4185 46.9039 46.3122L47.7267 42.1799ZM43.5964 40.6923C43.5964 40.4732 43.6831 40.2629 43.8374 40.108C43.9917 39.953 44.201 39.8659 44.4192 39.8659C44.6374 39.8659 44.8467 39.953 45.001 40.108C45.1553 40.2629 45.2419 40.4732 45.2419 40.6923V46.4774C45.2419 46.6966 45.1553 46.9068 45.001 47.0618C44.8467 47.2168 44.6374 47.3039 44.4192 47.3039C44.201 47.3039 43.9917 47.2168 43.8374 47.0618C43.6831 46.9068 43.5964 46.6966 43.5964 46.4774V40.6923ZM40.1408 41.5353C40.2467 41.5132 40.356 41.5125 40.4622 41.5332C40.5685 41.5539 40.6695 41.5956 40.7596 41.6559C40.8496 41.7162 40.9268 41.7938 40.9867 41.8844C41.0466 41.9749 41.0879 42.0765 41.1083 42.1832L41.9311 46.3155C41.9739 46.5293 41.9308 46.7515 41.8112 46.9335C41.6916 47.1156 41.5052 47.2428 41.2926 47.2874C41.2385 47.2987 41.1834 47.3042 41.1281 47.3039C40.9386 47.3037 40.7549 47.2379 40.6081 47.1174C40.4614 46.9969 40.3605 46.8293 40.3226 46.6427L39.4998 42.5105C39.4561 42.2958 39.4991 42.0725 39.6193 41.8896C39.7395 41.7067 39.9271 41.5793 40.1408 41.5353Z" fill="#E01010"/>
<path d="M36.1917 51.4355V53.0942C36.1932 53.7508 36.4539 54.3799 36.9166 54.8436C37.3794 55.3073 38.0063 55.5678 38.66 55.5678H50.1788C50.8324 55.5678 51.4593 55.3073 51.9221 54.8436C52.3848 54.3799 52.6455 53.7508 52.6471 53.0942V51.4355H36.1917Z" fill="#E01010"/>
</g>
</g>
<defs>
<filter id="filter0_d_839_2113" x="18.5" y="17" width="51.9999" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_839_2113"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_839_2113" result="shape"/>
</filter>
<filter id="filter1_i_839_2113" x="22.3398" y="17" width="45.16" height="55" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_839_2113"/>
</filter>
<filter id="filter2_i_839_2113" x="17.5" y="32.4277" width="10.6" height="24.1426" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_839_2113"/>
</filter>
<filter id="filter3_i_839_2113" x="32.9" y="32.4277" width="25.04" height="25.1406" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_839_2113"/>
</filter>
<linearGradient id="paint0_linear_839_2113" x1="44.5" y1="5.5" x2="44.5" y2="88" gradientUnits="userSpaceOnUse">
<stop stop-color="#B96AE8"/>
<stop offset="1" stop-color="#6C2FAD"/>
</linearGradient>
</defs>
</svg>
