<svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="88" height="88" rx="16" fill="url(#paint0_linear_839_2243)"/>
<g filter="url(#filter0_d_839_2243)">
<g filter="url(#filter1_i_839_2243)">
<path d="M30.36 17C26.2952 17 23 20.2968 23 24.3636V63.6364C23 67.7033 26.2952 71 30.36 71H59.8C63.8649 71 67.16 67.7033 67.16 63.6364V24.3636C67.16 20.2968 63.8649 17 59.8 17H30.36Z" fill="#FEFEFE"/>
</g>
</g>
<g filter="url(#filter2_i_839_2243)">
<circle cx="33.6602" cy="34" r="3" fill="#60C26F"/>
<circle cx="33.6602" cy="44" r="3" fill="#268EFF"/>
<circle cx="33.6602" cy="54" r="3" fill="#E01010"/>
<rect x="38.6602" y="33" width="20" height="2" rx="1" fill="#E0E0E0"/>
<rect x="38.6602" y="43" width="20" height="2" rx="1" fill="#E0E0E0"/>
<rect x="38.6602" y="53" width="20" height="2" rx="1" fill="#E0E0E0"/>
</g>
<defs>
<filter id="filter0_d_839_2243" x="23" y="17" width="48.1599" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_839_2243"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_839_2243" result="shape"/>
</filter>
<filter id="filter1_i_839_2243" x="23" y="17" width="45.1599" height="55" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_839_2243"/>
</filter>
<filter id="filter2_i_839_2243" x="30.6602" y="31" width="29" height="27" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_839_2243"/>
</filter>
<linearGradient id="paint0_linear_839_2243" x1="44" y1="0" x2="44" y2="88" gradientUnits="userSpaceOnUse">
<stop stop-color="#FC7182"/>
<stop offset="1" stop-color="#E63F45"/>
</linearGradient>
</defs>
</svg>
